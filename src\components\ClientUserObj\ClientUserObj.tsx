import { CopyOutlined } from "@ant-design/icons";
import { App, Dropdown } from "antd";

export interface TClientUserObjProps {
  user: TClientUser;
  extraItems?: { icon: React.ReactNode, label: string, key: string, hide?: boolean, value?: string }[]
  children?: React.ReactNode
}

function ClientUserObj(props: TClientUserObjProps) {
  const { message } = App.useApp();
  return  props.user ? (
    <Dropdown menu={{
      items: [
        { icon: <CopyOutlined />, label: `ID: ${props.user.id}`, key: 'id', hide: false },
        { icon: <CopyOutlined />, label: `名字: ${props.user.firstName} ${props.user.lastName}`, key: 'name', hide: false },
        { icon: <CopyOutlined />, label: `邮箱: ${props.user.email || ''}`, key: 'email', hide: !props.user.email },
        { icon: <CopyOutlined />, label: `用户名: ${props.user.username || ''}`, key: 'username', hide: !props.user.username },
        ...(props.extraItems || []),
      ].filter(item => !item.hide),
      onClick: (item: any) => {
        const text = props.user[item.key] || (props.extraItems || []).find(i => i.key === item.key)?.value || ''
        if (text) {
          navigator.clipboard.writeText(text)
          message.success('复制成功')
        }
      }
    }}
      arrow
    >
      <div className="cursor-pointer">
        {props.children ? props.children : <div className="text-blue-500">{props.user.firstName} {props.user.lastName}</div>}
      </div>
    </Dropdown>
  ) : null
}

export default ClientUserObj
