import React from 'react';
import { THomeModule } from './types';
import Module<PERSON>enderer from './ModuleRenderer';
import { Card } from 'antd';

interface HomePreviewProps {
  modules: THomeModule[];
}

const HomePreview: React.FC<HomePreviewProps> = ({ modules }) => {
  if (modules.length === 0) {
    return (
      <Card className="text-center p-8">
        <p className="text-gray-500">暂无内容，请添加模块</p>
      </Card>
    );
  }

  return (
    <div className="home-preview">
      {modules.map((module) => (
        <div key={module.id} className="mb-4">
          <ModuleRenderer
            module={module}
            isFirst={false}
            isLast={false}
            onUpdate={() => {}}
            onDelete={() => {}}
            onMoveUp={() => {}}
            onMoveDown={() => {}}
            onInsertBefore={() => {}}
            isPreview={true}
          />
        </div>
      ))}
    </div>
  );
};

export default HomePreview;
