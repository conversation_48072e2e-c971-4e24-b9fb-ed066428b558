{"name": "dnhhsj-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "apis": "tsx ./src/bin/genApis.ts", "genp": "tsx ./src/bin/genPage.ts -n", "genc": "tsx ./src/bin/genComponent.ts -n", "geni": "tsx ./src/bin/genIcon.ts -u", "analyze": "vite build --mode analyze", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.4.0", "@ant-design/pro-components": "2.7.14", "@formily/antd-v5": "^1.2.2", "@formily/core": "^2.3.2", "@formily/react": "^2.3.2", "@formily/reactive": "^2.3.2", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "ahooks": "^3.8.0", "antd": "5.19.3", "async-wait-until": "^2.0.12", "axios": "^1.7.2", "clsx": "^2.1.1", "dayjs": "^1.11.12", "default-passive-events": "^2.0.0", "eventbus-z": "^2.0.2", "exceljs": "^4.4.0", "foca": "^3.2.0", "immer": "^10.1.1", "js-md5": "^0.8.3", "konva": "^9.3.20", "lodash": "^4.17.21", "nanoid": "^3.3.7", "number-precision": "^1.6.0", "pretty-bytes": "^6.1.1", "qs": "^6.14.0", "rc-field-form": "^1.22.0", "react": "~18.3.1", "react-activation": "^0.13.0", "react-dom": "~18.3.1", "react-konva": "^18.2.0", "react-router-dom": "^6.25.1", "tony-mockjs": "^1.1.4", "url-parse": "^1.5.10", "uuid": "^10.0.0", "zustand": "^5.0.1"}, "devDependencies": {"@babel/parser": "^7.26.2", "@babel/traverse": "^7.25.9", "@skorotkiewicz/snowflake-id": "^1.0.1", "@types/babel__traverse": "^7.20.6", "@types/js-md5": "^0.7.2", "@types/lodash": "^4.17.7", "@types/node": "^20.14.12", "@types/qrcode": "^1.5.5", "@types/qrcode-svg": "^1.1.5", "@types/qs": "^6.9.18", "@types/react": "~18.3.1", "@types/react-dom": "~18.3.1", "@types/url-parse": "^1.4.11", "@types/uuid": "^10.0.0", "@vitejs/plugin-react-swc": "^3.7.0", "autoprefixer": "^10.4.19", "commander": "^12.1.0", "eslint": "^9.7.0", "eslint-plugin-react": "^7.35.0", "js-yaml": "^4.1.0", "less": "^4.2.0", "postcss": "^8.4.40", "prettier": "^3.3.3", "sass": "^1.77.8", "typescript": "^5.5.4", "unocss": "^0.61.5", "vite": "^5.3.5", "vite-bundle-analyzer": "^0.10.6", "vite-plugin-externals": "^0.6.2", "vue-tsc": "^2.0.29"}, "packageManager": "pnpm@9.15.0"}