import { apiGetClientUsers } from "@/apis/gen.apis";
import { useRequest } from "ahooks";

export const useClientUserSearch = () => {

  const { data: options, runAsync: onSearch, loading } = useRequest(async (keyword: string) => {
    if (!keyword || !keyword.trim()) return [];
    const res = await apiGetClientUsers({
      current: 0,
      size: 100,
      filters: [{ field: 'username', operator: 'like', value: keyword.trim() }] as any,
      sorts: [],
    });
    return (res.records || []).map((item) => ({
      label: item.username,
      value: item.id,
    }));
  }, {
    manual: true,
    debounceWait: 300,
  });


  return {
    options,
    onSearch,
    loading,
    showSearch: true,
  };
};