import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';

const PATH_START_WITH = '/admin';
const OPEN_API_URL = 'http://*********:8888/q/openapi';

interface OpenAPISchema {
  openapi: string;
  info: any;
  paths: Record<string, any>;
  components?: {
    schemas?: Record<string, any>;
  };
}

interface SchemaProperty {
  type?: string;
  format?: string;
  nullable?: boolean;
  $ref?: string;
  description?: string;
  summary?: string;
  items?: {
    $ref?: string;
    type?: string;
  };
  properties?: Record<string, SchemaProperty>;
  enum?: string[];
}

const functionNameSet = new Set<string>();

// 将引用路径转换为类型名称
function refToTypeName(ref: string): string {
  if (!ref) return 'any';

  // 从引用路径中提取类型名称
  let typeName = ref.split('/').pop() || '';

  // 如果类型名不以T开头，则添加T前缀
  if (!typeName.startsWith('T')) {
    typeName = 'T' + typeName;
  }

  return typeName;
}

// OpenAPI类型转TS类型
function openApiTypeToTsType(type: string, format?: string): string {
  switch (type) {
    case 'integer':
    case 'number':
      return 'number';
    case 'string':
      return format === 'date-time' ? 'string' : 'string';
    case 'boolean':
      return 'boolean';
    case 'object':
      return 'Record<string, any>';
    case 'array':
      return 'any[]';
    default:
      return type || 'any';
  }
}

// 生成函数名称
function generateFunctionName(path: string, method: string, operation: any): string {
  // 使用operation的summary或operationId来生成函数名
  if (operation.operationId) {

    return 'api' + operation.operationId.charAt(0).toUpperCase() + operation.operationId.slice(1);
  }

  // 根据路径和方法生成函数名
  const pathParts = path.split('/').filter(part => part && !part.startsWith('{'));
  const methodName = method.toLowerCase();

  let functionName = 'api';

  // 添加路径相关的名称
  pathParts.forEach(part => {
    const cleanPart = part.replace(/-\w/g, (match) => match.slice(1).toUpperCase());
    functionName += cleanPart.charAt(0).toUpperCase() + cleanPart.slice(1);
  });

  // 根据HTTP方法添加前缀
  functionName += methodName.charAt(0).toUpperCase() + methodName.slice(1)

  let endNum = 0;
  let finalFunctionName = functionName;
  while (functionNameSet.has(finalFunctionName)) {
    endNum++;
    finalFunctionName = functionName + endNum;
  }
  functionName = finalFunctionName
  functionNameSet.add(functionName);
  return functionName;
}

// 生成API接口函数
function generateApiInterface(path: string, method: string, operation: any): string {
  const functionName = generateFunctionName(path, method, operation);

  // 提取路径参数
  const pathParams: string[] = [];
  const pathParamMatches = path.match(/\{([^}]+)\}/g);
  if (pathParamMatches) {
    pathParamMatches.forEach(match => {
      const paramName = match.slice(1, -1);
      pathParams.push(paramName);
    });
  }

  // 提取查询参数
  const queryParams: string[] = [];
  const parameters = operation.parameters || [];
  parameters.forEach((param: any) => {
    if (param.in === 'query') {
      queryParams.push(param.name);
    }
  });

  // 确定请求体类型
  let requestBodyType = '';
  if (operation.requestBody && operation.requestBody.content) {
    const content = operation.requestBody.content['application/json'];
    if (content && content.schema) {
      if (content.schema.$ref) {
        requestBodyType = refToTypeName(content.schema.$ref);
      } else if (content.schema.type) {
        requestBodyType = openApiTypeToTsType(content.schema.type);
      }
    }
  }

  // 确定响应类型
  let responseType = 'any';
  if (operation.responses && operation.responses['200']) {
    const response = operation.responses['200'];
    if (response.content && response.content['application/json']) {
      const schema = response.content['application/json'].schema;
      if (schema.$ref) {
        responseType = refToTypeName(schema.$ref);
      } else if (schema.type) {
        responseType = openApiTypeToTsType(schema.type);
      }
    }
  }

  // 构建函数参数
  const functionParams: string[] = [];

  // 添加路径参数
  pathParams.forEach(param => {
    functionParams.push(`${param}: string`);
  });

  // 添加请求体参数
  if (requestBodyType && ['post', 'put', 'patch'].includes(method.toLowerCase())) {
    functionParams.push(`data: ${requestBodyType}`);
  }

  // 添加查询参数（如果有的话，作为可选参数对象）
  if (queryParams.length > 0) {
    const queryParamTypes = queryParams.map(param => `${param}?: any`).join('; ');
    functionParams.push(`query?: { ${queryParamTypes} }`);
  }

  // 生成注释
  let apiFunction = `/** ${operation.summary || path} */\n`;

  // 生成函数签名
  apiFunction += `export function ${functionName}(${functionParams.join(', ')}) {\n`;

  // 替换路径中的参数
  let apiPath = path;
  pathParams.forEach(param => {
    apiPath = apiPath.replace(`{${param}}`, `\${${param}}`);
  });

  // 生成HTTP请求
  const httpMethod = method.toLowerCase();
  apiFunction += `  return http.${httpMethod}<${responseType}>(\`${apiPath}\``;

  // 添加请求体数据
  if (requestBodyType && ['post', 'put', 'patch'].includes(httpMethod)) {
    apiFunction += ', data';
  }

  // 添加查询参数配置
  if (queryParams.length > 0) {
    if (requestBodyType && ['post', 'put', 'patch'].includes(httpMethod)) {
      apiFunction += ', query';
    } else {
      apiFunction += ', query';
    }
  }

  apiFunction += ');\n';
  apiFunction += '}\n\n';

  return apiFunction;
}

// 生成TypeScript类型定义
function generateTypeDefinition(name: string, schema: SchemaProperty): string {
  const typeName = refToTypeName(name);

  if (schema.enum) {
    const enumValues = schema.enum.map(e => `"${e}"`).join(' | ');
    return `type ${typeName} = ${enumValues};\n\n`;
  }

  if (schema.type === 'object' && schema.properties) {
    let interfaceString = `/**\n * @description ${schema.description || name}\n */\n`;
    interfaceString += `interface ${typeName} {\n`;

    for (const [propName, propSchema] of Object.entries(schema.properties)) {
      let propType = 'any';
      if (propSchema.$ref) {
        propType = refToTypeName(propSchema.$ref);
      } else if (propSchema.type === 'array' && propSchema.items) {
        if (propSchema.items.$ref) {
          propType = `${refToTypeName(propSchema.items.$ref)}[]`;
        } else {
          propType = `${openApiTypeToTsType(propSchema.items.type || 'any')}[]`;
        }
      } else if (propSchema.type) {
        propType = openApiTypeToTsType(propSchema.type, propSchema.format);
      }

      // 如果字段以id或Id结尾，字段类型是number,设置字段类型为string
      if ((propName.endsWith('id') || propName.endsWith('Id')) && propType === 'number') {
        propType = 'string';
      }

      // 如果字段以ids或Ids结尾，字段类型是number[],设置字段类型为string[]
      if ((propName.endsWith('ids') || propName.endsWith('Ids')) && propType === 'number[]') {
        propType = 'string[]';
      }

      const isRequired = !propSchema.nullable;
      interfaceString += `  /** ${propSchema.description || ''} */\n`;
      interfaceString += `  ${propName}${isRequired ? '' : '?'}: ${propType};\n`;
    }

    interfaceString += '}\n\n';
    return interfaceString;
  }

  return '';
}

async function main() {
  try {
    const res = await fetch(OPEN_API_URL);
    const yamlContent = await res.text();
    // const yamlPath = path.resolve('./openapi.yml');

    // if (!fs.existsSync(yamlPath)) {
    //   throw new Error(`OpenAPI文档不存在: ${yamlPath}`);
    // }

    // console.log('正在读取OpenAPI文档...');
    // const yamlContent = fs.readFileSync(yamlPath, 'utf-8');

    console.log('解析YAML文档...');
    const openApiDoc = yaml.load(yamlContent) as OpenAPISchema;

    if (!openApiDoc || !openApiDoc.paths) {
      throw new Error('无效的OpenAPI文档');
    }

    console.log('生成类型定义...');
    let typeDefinitions = '// 此文件由 genApis 脚本自动生成，请勿手动修改\n';
    typeDefinitions += `// 生成时间: ${new Date().toISOString()}\n\n`;

    if (openApiDoc.components && openApiDoc.components.schemas) {
      for (const [schemaName, schema] of Object.entries(openApiDoc.components.schemas)) {
        typeDefinitions += generateTypeDefinition(schemaName, schema);
      }
    }

    const typesDir = path.resolve('./src/types');
    if (!fs.existsSync(typesDir)) {
      fs.mkdirSync(typesDir, { recursive: true });
    }

    const typesOutputPath = path.resolve('./src/types/gen.d.ts');
    fs.writeFileSync(typesOutputPath, typeDefinitions);
    console.log(`类型定义已生成到: ${typesOutputPath}`);

    console.log('生成API接口...');
    let apiInterfaces = `import http from '@/utils/http';\n\n`;

    // 添加生成说明注释
    apiInterfaces += `// 此文件由 genApis 脚本自动生成，请勿手动修改\n`;
    apiInterfaces += `// 生成时间: ${new Date().toISOString()}\n\n`;

    // 遍历所有路径和方法
    for (const [pathUrl, pathItem] of Object.entries(openApiDoc.paths)) {

      // 只处理以 /admin 开头的路径
      if (pathUrl.startsWith(PATH_START_WITH)) {
        // 移除 /admin 前缀
        const processedPath = pathUrl.substring(PATH_START_WITH.length) || '/';

        for (const [method, operation] of Object.entries(pathItem)) {
          if (['get', 'post', 'put', 'delete', 'patch'].includes(method)) {
            apiInterfaces += generateApiInterface(processedPath, method, operation);
          }
        }
      }
    }

    // 确保目录存在
    const apisDir = path.resolve('./src/apis');
    if (!fs.existsSync(apisDir)) {
      fs.mkdirSync(apisDir, { recursive: true });
    }

    // 写入生成的API文件
    const outputPath = path.resolve('./src/apis/gen.apis.ts');
    fs.writeFileSync(outputPath, apiInterfaces);

    console.log(`API接口已生成到: ${outputPath}`);
    console.log('生成完成！');

  } catch (error) {
    console.error('生成失败:', error);
    process.exit(1);
  }
}

// 执行主函数
main();
