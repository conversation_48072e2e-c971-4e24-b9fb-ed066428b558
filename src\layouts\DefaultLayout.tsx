
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { AliveScope } from "react-activation";
import { useEffect, useMemo, useState } from 'react';
import { Avatar, Dropdown, Menu, Space, Badge, Image } from 'antd';
import type { MenuProps } from 'antd';
import { TLayoutRoute, layoutRoute } from "@/router";
import { useCreation, useMemoizedFn } from 'ahooks';
import { nanoid } from '@ant-design/pro-components';
import { useModel } from 'foca';
import { authModel } from '@/stores/authModel';
import { BellOutlined, UserOutlined } from '@ant-design/icons';
import TabNav from '@/components/TabNav';
import { layoutModel } from '@/stores/layoutModel';
import { systemSettingsModel } from '@/stores/systemSettingsModel';


type MenuItem = Required<MenuProps>['items'][number] & {
  [key: string]: any;
}

const DefaultLayout = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const authModelState = useModel(authModel);
  const layoutModelState = useModel(layoutModel);
  const { websiteLogo, initialized } = useModel(systemSettingsModel);

  const mainWidth = useMemo(() => {
    return layoutModelState.windowWidth - layoutModelState.sidebarWidth;
  }, [layoutModelState.windowWidth, layoutModelState.sidebarWidth])

  const mainHeight = useMemo(() => {
    return layoutModelState.windowHeight - layoutModelState.headerHeight - layoutModelState.navbarHeight;
  }, [layoutModelState.windowHeight])

  const [menuOpenKeys, setMenuOpenKeys] = useState<string[]>([])
  const [menuSelectedKeys, setMenuSelectedKeys] = useState<string[]>([])


  const menuKeysMap = useCreation(() => new Map<string, string[]>(), [])
  // const menuParentKeysMap = useCreation(() => new Map<string, string[]>(), [])
  const menuKeyLinkMap = useCreation(() => new Map<string, string>(), [])

  const menuItems: MenuItem[] = useCreation(() => {
    const initMenuOpenKeys: string[] = []
    const transform = (items: TLayoutRoute[], parentKeys: string[] = []): MenuItem[] => {

      const result: MenuItem[] = []
      items.forEach(item => {

        if (!item.hideInMenu) {
          const key = nanoid()
          const children = item.routes ? transform(item.routes, [...parentKeys, key]) : []
          if (children.length > 0) {
            initMenuOpenKeys.push(key)
          }
          let label: any = item.name
          result.push({
            className: authModel.canGoTo(item) ? '' : 'hidden-component',
            key,
            link: item.path,
            icon: item.icon,
            label,
            children: children.length > 0 ? children : undefined,
          })
          menuKeysMap.set(item.path, [...(menuKeysMap.get(item.path) || []), key])
          menuKeyLinkMap.set(key, item.path)
        } else {
          menuKeysMap.set(item.path, parentKeys)
        }
      })
      return result
    }
    menuKeysMap.clear()
    setMenuOpenKeys(initMenuOpenKeys)
    return transform(layoutRoute.routes || [])
  }, [authModelState.permissions])

  useEffect(() => {
    setMenuSelectedKeys(menuKeysMap.get(location.pathname) || [])
  }, [menuItems, location.pathname])

  // Load system settings when component mounts
  // useEffect(() => {
  //   if (!initialized) {
  //     systemSettingsModel.loadSettings();
  //   }
  // }, [initialized]);




  return (authModelState.account ? (
    <div className="w-screen h-screen overflow-hidden flex flex-col items-stretch">
      <div
        style={{ borderBottom: '1px solid #eee', height: layoutModelState.headerHeight }}
        className='shrink-0 flex items-center px-[20px] border-t-2 border-blue-900'
      >
        {websiteLogo ? (
          <Image
            className='block h-[24px]'
            src={websiteLogo}
            alt="Logo"
            height={24}
            preview={false}
            style={{ objectFit: 'contain' }}
          />
        ) : (
          <img className='block h-[24px]' src="/logo.png" />
        )}
        <div className='text-[18px] font-bold ml-[10px]'>
          {import.meta.env.VITE_APP_TITLE}
        </div>
        <div className="ml-auto"></div>
        <Dropdown menu={{
          items: [
            { key: 'logout', label: '退出登录' }
          ],
          onClick: (item) => {
            switch (item.key) {
              case 'logout':
                authModel.logout()
                break;
              default:
            }
          },
        }} placement="bottomRight">
          <Space className='cursor-pointer'>
            <Avatar size="small" icon={<UserOutlined />} />
            <span>{authModelState.account?.nickname || ''}</span>
          </Space>
        </Dropdown>
      </div>
      <div className='flex-1 flex'>
        <div className='shrink-0 overflow-auto h-[calc(100vh-60px)]' style={{ width: layoutModelState.sidebarWidth }}>
          <Menu
            onOpenChange={(keys) => setMenuOpenKeys(keys)}
            onSelect={(e) => setMenuSelectedKeys(e.selectedKeys)}
            selectedKeys={menuSelectedKeys}
            openKeys={menuOpenKeys}
            mode="inline"
            inlineCollapsed={false}
            items={menuItems}
            onClick={(e) => {
              const link = menuKeyLinkMap.get(e.key)
              link && navigate(link)
            }}
          />
        </div>
        <div className='flex-1'>
          <div style={{
            height: layoutModelState.navbarHeight,
            width: mainWidth,
            borderBottom: '1px solid #eee'
          }}>
            <TabNav />
          </div>

          <div className='bg-[#eee] overflow-auto p-[20px]'
            style={{
              height: mainHeight,
              width: mainWidth,
            }}
          >
            <AliveScope>
              <Outlet />
            </AliveScope>

          </div>

        </div>
      </div>
    </div>
  ) : null
  );
}

export default DefaultLayout;