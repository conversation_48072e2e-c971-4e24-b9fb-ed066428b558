import { useState, useEffect } from 'react';
import {
  Button,
  Card,
  message,
  Space,
  Modal,
} from 'antd';
import {
  PlusOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import {
  THomeModule,
  ModuleType,
  ModuleSelector,
  ModuleRenderer,
  createModule
} from '@/components/HomeModules';
import {
  apiSystemSettingCreate,
  apiSystemSettingGetByKey,
  apiSystemSettingUpdate
} from '@/apis/apis.api';

function HomeSetting() {
  const [modules, setModules] = useState<THomeModule[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [moduleSelectorVisible, setModuleSelectorVisible] = useState(false);
  const [insertPosition, setInsertPosition] = useState<number | null>(null);
  // 新添加的模块ID，用于标记哪个模块需要默认处于编辑状态
  const [newModuleId, setNewModuleId] = useState<string | null>(null);

  // 模拟从后端加载数据
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // 从后端获取数据，使用key为client.homeLayout
        const response = await apiSystemSettingGetByKey('client_homeLayout');

        // 解析保存的模块数据
        if (response) {
          try {
            const parsedData = JSON.parse(response.value);
            setModules(parsedData);
          } catch (parseError) {
            console.error('解析首页配置数据失败', parseError);
            setModules([]);
          }
        } else {
          // 如果没有数据，设置为空数组
          setModules([]);
        }
      } catch (error) {
        console.error('加载首页配置失败', error);
        // message.error('加载首页配置失败');
        // 如果加载失败，设置为空数组
        setModules([]);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // 保存数据到后端
  const handleSave = async () => {
    setSaving(true);
    try {
      // 将模块数据转换为JSON字符串
      const moduleDataString = JSON.stringify(modules);

      // 首先检查是否已存在配置
      const existingConfig = await apiSystemSettingGetByKey('client_homeLayout');

      if (existingConfig) {
        // 如果存在，则更新
        // Use ID as string to avoid precision loss with snowflake IDs
        await apiSystemSettingUpdate(existingConfig.id, {
          value: moduleDataString
        });
      } else {
        // 如果不存在，则创建新的配置
        await apiSystemSettingCreate({
          key: 'client_homeLayout',
          value: moduleDataString,
          group: 'client',
          description: '首页布局配置'
        });
      }

      message.success('保存成功');
    } catch (error) {
      console.error('保存失败', error);
      message.error('保存失败');
    } finally {
      setSaving(false);
    }
  };

  // 打开模块选择器
  const handleAddModule = (position: number | null = null) => {
    setInsertPosition(position);
    setModuleSelectorVisible(true);
  };

  // 选择模块类型后创建新模块
  const handleSelectModuleType = (type: ModuleType) => {
    const newOrder = modules.length > 0
      ? Math.max(...modules.map(m => m.order)) + 1
      : 0;

    const newModule = createModule(type, newOrder);

    // 设置新模块ID，使其默认处于编辑状态
    setNewModuleId(newModule.id);

    if (insertPosition !== null) {
      // 在指定位置插入
      const updatedModules = [...modules];
      updatedModules.splice(insertPosition, 0, newModule);

      // 更新顺序
      const reorderedModules = updatedModules.map((module, index) => ({
        ...module,
        order: index
      }));

      setModules(reorderedModules);
    } else {
      // 添加到末尾
      setModules([...modules, newModule]);
    }

    setModuleSelectorVisible(false);
    setInsertPosition(null);
  };

  // 更新模块
  const handleUpdateModule = (updatedModule: THomeModule) => {
    const updatedModules = modules.map(module =>
      module.id === updatedModule.id ? updatedModule : module
    );
    setModules(updatedModules);

    // 如果是正在编辑的新模块，更新后清除新模块标记
    if (updatedModule.id === newModuleId) {
      setNewModuleId(null);
    }
  };

  // 删除模块
  const handleDeleteModule = (moduleId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个模块吗？',
      onOk: () => {
        const filteredModules = modules.filter(module => module.id !== moduleId);
        // 重新排序
        const reorderedModules = filteredModules.map((module, index) => ({
          ...module,
          order: index
        }));
        setModules(reorderedModules);

        // 如果删除的是新添加的模块，清除新模块标记
        if (moduleId === newModuleId) {
          setNewModuleId(null);
        }
      }
    });
  };

  // 上移模块
  const handleMoveUp = (index: number) => {
    if (index <= 0) return;

    const updatedModules = [...modules];
    const temp = updatedModules[index];
    updatedModules[index] = updatedModules[index - 1];
    updatedModules[index - 1] = temp;

    // 更新顺序
    const reorderedModules = updatedModules.map((module, idx) => ({
      ...module,
      order: idx
    }));

    setModules(reorderedModules);
  };

  // 下移模块
  const handleMoveDown = (index: number) => {
    if (index >= modules.length - 1) return;

    const updatedModules = [...modules];
    const temp = updatedModules[index];
    updatedModules[index] = updatedModules[index + 1];
    updatedModules[index + 1] = temp;

    // 更新顺序
    const reorderedModules = updatedModules.map((module, idx) => ({
      ...module,
      order: idx
    }));

    setModules(reorderedModules);
  };

  // 在模块前插入新模块
  const handleInsertBefore = (index: number) => {
    handleAddModule(index);
  };

  return (
    <div className="p-6">
      <Card title="首页设置" loading={loading} className="mb-4" extra={(
        <Space>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSave}
            loading={saving}
          >
            保存配置
          </Button>
        </Space>
      )}>

        {modules.map((module, index) => (
            <ModuleRenderer
              key={module.id}
              module={module}
              isFirst={index === 0}
              isLast={index === modules.length - 1}
              onUpdate={handleUpdateModule}
              onDelete={() => handleDeleteModule(module.id)}
              onMoveUp={() => handleMoveUp(index)}
              onMoveDown={() => handleMoveDown(index)}
              onInsertBefore={() => handleInsertBefore(index)}
              isEditing={module.id === newModuleId} // 传递是否是新添加的模块，用于默认进入编辑状态
            />
          ))}
          <Card className="text-center p-4">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => handleAddModule()}
            >
              添加模块
            </Button>
          </Card>
      </Card>

      <ModuleSelector
        visible={moduleSelectorVisible}
        onCancel={() => setModuleSelectorVisible(false)}
        onSelect={handleSelectModuleType}
      />
    </div>
  );
}

export default HomeSetting;

HomeSetting.auth = ['apiSystemSettingCreate', 'apiSystemSettingUpdate', 'apiSystemSettingGetByKey'];
