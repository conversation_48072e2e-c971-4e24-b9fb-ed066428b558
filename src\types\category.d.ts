declare namespace API {
  interface ProductCategoryBo {
    id: string;
    name: string;
    code: string;
    parentid: string | null;
    level: number;
    path: string;
    sortOrder: number;
    isActive: boolean;
    description: string;
    imageUrl: string;
    children?: ProductCategoryBo[];
    createdAt: string;
    updatedAt: string;
  }

  interface CategoryPageQuery extends PageQuery {
    parentId?: number;
    level?: number;
    isActive?: boolean;
    name?: string;
    code?: string;
  }

  interface PageResultProductCategoryBo {
    records: ProductCategoryBo[];
    total: number;
    size: number;
    current: number;
  }
}
