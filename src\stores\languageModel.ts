import { defineModel } from 'foca';
import { apiLanguagePageList } from '@/apis/apis.api';

export interface LanguageModelState {
  languages: TLanguage[];
  loading: boolean;
  initialized: boolean;
}

const initialState: LanguageModelState = {
  languages: [],
  loading: false,
  initialized: false
};

export const languageModel = defineModel('languageModel', {
  initialState,
  reducers: {
    setLanguages(state, languages: TLanguage[]) {
      state.languages = languages;
      state.initialized = true;
    },
    setLoading(state, loading: boolean) {
      state.loading = loading;
    }
  },
  methods: {
    async loadLanguages() {
      // If already loaded, don't fetch again
      if (this.state.initialized && this.state.languages.length > 0) {
        return this.state.languages;
      }

      this.setLoading(true);
      try {
        const response = await apiLanguagePageList({
          current: 0,
          size: 100,
          filters: [{ field: 'isActive', operator: 'eq', value: 1 }]
        });
        
        // Sort languages to put default language first
        const sortedLanguages = response.records.sort((a, b) => 
          a.isDefault ? -1 : b.isDefault ? 1 : 0
        );
        
        this.setLanguages(sortedLanguages);
        return sortedLanguages;
      } catch (error) {
        console.error('Failed to load languages:', error);
        return [];
      } finally {
        this.setLoading(false);
      }
    },
    getLanguageCodes() {
      return this.state.languages.map(lang => lang.code);
    },
    getLanguageName(code: string) {
      const language = this.state.languages.find(lang => lang.code === code);
      return language ? language.nativeName : code;
    }
  }
});
