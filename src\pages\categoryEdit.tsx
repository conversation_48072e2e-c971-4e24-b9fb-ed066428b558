import React, { useEffect, useState } from 'react';
import { Button, Card, Form, Input, InputNumber, Space, Switch, message, TreeSelect } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { apiCategoryCreateOrUpdate, apiCategoryGetById, apiCategoryTree } from '@/apis/apis.api';
import I18nField from '@/components/I18nField';
import I18nText from '@/components/I18nText';

const { TextArea } = Input;

const CategoryEdit: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [categoryTree, setCategoryTree] = useState<API.ProductCategoryBo[]>([]);

  useEffect(() => {
    fetchCategoryTree();
    if (id) {
      fetchCategoryDetail();
    }
  }, [id]);

  const fetchCategoryTree = async () => {
    try {
      const res = await apiCategoryTree();
      setCategoryTree(res);
    } catch (error) {
      message.error('获取类别树失败');
    }
  };

  const fetchCategoryDetail = async () => {
    try {
      setLoading(true);
      const res = await apiCategoryGetById(id!);
      const categoryData = res;

      // Handle multi-language fields
      const formData = {
        code: categoryData.code,
        parentId: categoryData.parentId,
        isActive: categoryData.isActive,
        sortOrder: categoryData.sortOrder,
      };

      // Process name field (multi-language)
      if (typeof categoryData.name === 'string') {
        try {
          // Try to parse as JSON for multi-language
          formData.name = categoryData.name;
        } catch (e) {
          // If not valid JSON, use as is
          formData.name = categoryData.name;
        }
      } else {
        formData.name = categoryData.name;
      }

      // Process description field (multi-language)
      if (typeof categoryData.description === 'string') {
        try {
          // Try to parse as JSON for multi-language
          formData.description = categoryData.description;
        } catch (e) {
          // If not valid JSON, use as is
          formData.description = categoryData.description;
        }
      } else {
        formData.description = categoryData.description;
      }

      form.setFieldsValue(formData);
    } catch (error) {
      message.error('获取类别详情失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);

      // Get form values
      const formValues = form.getFieldsValue();

      // Create category data object
      const categoryData: Partial<API.ProductCategoryBo> = {
        ...formValues,
      };

      if (id) {
        categoryData.id = Number(id);
      }

      // 根据父类别自动确定层级
      if (categoryData.parentId) {
        const parentCategory = findCategoryById(categoryTree, categoryData.parentId);
        if (parentCategory) {
          categoryData.level = parentCategory.level + 1;
        }
      } else {
        categoryData.level = 1;
      }

      await apiCategoryCreateOrUpdate(categoryData);
      message.success(`${id ? '更新' : '创建'}类别成功`);
      navigate('/category/list');
    } catch (error) {
      message.error(`${id ? '更新' : '创建'}类别失败`);
    } finally {
      setSubmitting(false);
    }
  };

  // 将类别树转换为TreeSelect所需的数据格式
  const transformCategoryTree = (data: API.ProductCategoryBo[]) => {
    return data.map(item => ({
      title: item.name,
      value: item.id,
      key: item.id,
      children: item.children ? transformCategoryTree(item.children) : undefined,
    }));
  };

  // 根据ID查找类别
  const findCategoryById = (data: API.ProductCategoryBo[], id: string) => {
    for (const item of data) {
      if (item.id === id) {
        return item;
      }
      if (item.children) {
        const found = findCategoryById(item.children, id);
        if (found) {
          return found;
        }
      }
    }
    return null;
  };

  return (
    <Card
      title={
        <Space>
          <Button icon={<ArrowLeftOutlined />} onClick={() => navigate('/category/list')}>
            返回
          </Button>
          <span>{id ? '编辑' : '新增'}类别</span>
        </Space>
      }
      loading={loading}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          isActive: true,
          sortOrder: 0,
        }}
      >
        <Form.Item
          name="name"
          label="类别名称"
          rules={[{ required: true, message: '请输入类别名称' }]}
        >
          <I18nField>
            <Input placeholder="请输入类别名称" />
          </I18nField>
        </Form.Item>

        <Form.Item
          name="code"
          label="类别编码"
          rules={[{ required: true, message: '请输入类别编码' }]}
        >
          <Input placeholder="请输入类别编码" />
        </Form.Item>

        <Form.Item
          name="parentId"
          label="父类别"
        >
          <TreeSelect
            placeholder="请选择父类别"
            allowClear
            treeData={transformCategoryTree(categoryTree)}
            treeDefaultExpandAll
            disabled={!!id} // 编辑时不允许修改父类别
          />
        </Form.Item>

        <Form.Item
          name="description"
          label="类别描述"
        >
          <I18nField>
            <Input.TextArea rows={4} placeholder="请输入类别描述" />
          </I18nField>
        </Form.Item>

        <Form.Item
          name="sortOrder"
          label="排序"
        >
          <InputNumber min={0} />
        </Form.Item>

        <Form.Item
          name="isActive"
          label="是否启用"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item>
          <Space>
            <Button type="primary" onClick={handleSubmit} loading={submitting}>
              保存
            </Button>
            <Button onClick={() => navigate('/category/list')}>取消</Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default CategoryEdit;

CategoryEdit.auth = ['apiCategoryCreateOrUpdate', 'apiCategoryGetById', 'apiCategoryTree'];
