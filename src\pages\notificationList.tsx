import {
  apiNotificationTemplateCreate,
  apiNotificationTemplateDelete,
  apiNotificationTemplateDisable,
  apiNotificationTemplateEnable,
  apiNotificationTemplatePage,
  apiNotificationTemplateUpdate
} from '@/apis/apis.api';
import { useFormModal } from '@/components/FormilyModal';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { App, Button, Popconfirm, Space, Tag } from 'antd';
import { useRef } from 'react';

type TTemplateFormData = {
  code: string;
  name: string;
  titleTemplate: string;
  contentTemplate: string;
  type: TNotificationType;
  businessType?: string;
  description?: string;
};

function NotificationList() {
  const tableRef = useRef<ActionType>();
  const { message, modal } = App.useApp();

  const { formModalRef: templateFormModalRef, formModalHolder: templateFormModalHolder } =
    useFormModal<TTemplateFormData>();

  const showTemplateModal = useMemoizedFn((record?: TNotificationTemplateBo) => {
    templateFormModalRef.current?.show({
      modalTitle: record ? '编辑通知模板' : '新增通知模板',
      modalWidth: 800,
      onAutoSubmit: async (values) => {
        try {
          if (record?.id) {
            await apiNotificationTemplateUpdate(record.id, values);
            message.success('更新成功');
          } else {
            await apiNotificationTemplateCreate(values);
            message.success('创建成功');
          }
          tableRef.current?.reload();
        } catch (error) {
          console.error('提交失败', error);
          message.error('操作失败，请重试');
        }
      },
      initialValues: record
        ? {
            ...record,
          }
        : {},
      schema: {
        type: 'object',
        properties: {
          code: {
            title: '模板代码',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          name: {
            title: '模板名称',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          // type: {
          //   title: '模板类型',
          //   type: 'string',
          //   required: true,
          //   'x-decorator': 'FormItem',
          //   'x-component': 'Select',
          //   enum: [
          //     { label: '系统', value: 'SYSTEM' },
          //     { label: '订单', value: 'ORDER' },
          //     { label: '交易', value: 'TRANSACTION' },
          //     { label: '产品', value: 'PRODUCT' },
          //     { label: '营销', value: 'MARKETING' },
          //     { label: '其他', value: 'OTHER' },
          //   ],
          // },
          businessType: {
            title: '业务类型',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          titleTemplate: {
            title: '标题模板',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          contentTemplate: {
            title: '内容模板',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 6,
            },
          },
          description: {
            title: '描述',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 3,
            },
          },
        },
      },
    });
  });

  const handleDelete = useMemoizedFn((id: string) => {
    modal.confirm({
      title: '确认删除',
      content: '确定要删除此通知模板吗？此操作不可恢复。',
      onOk: async () => {
        try {
          await apiNotificationTemplateDelete(id);
          message.success('删除成功');
          tableRef.current?.reload();
        } catch (error) {
          console.error('删除失败', error);
          message.error('删除失败，请重试');
        }
      },
    });
  });

  const handleToggleStatus = useMemoizedFn(async (id: string, isEnabled: boolean) => {
    try {
      if (isEnabled) {
        await apiNotificationTemplateDisable(id);
        message.success('已禁用');
      } else {
        await apiNotificationTemplateEnable(id);
        message.success('已启用');
      }
      tableRef.current?.reload();
    } catch (error) {
      console.error('操作失败', error);
      message.error('操作失败，请重试');
    }
  });

  return (
    <div>
      <ProTable<TNotificationTemplateBo>
        actionRef={tableRef}
        headerTitle='通知模板列表'
        rowKey='id'
        toolBarRender={() => [
          <Button key='add' type='primary' onClick={() => showTemplateModal()}>
            新增模板
          </Button>,
        ]}
        search={{
          defaultCollapsed: false,
        }}
        columns={[
          { title: '模板ID', dataIndex: 'id', search: false },
          { title: '模板代码', dataIndex: 'code' },
          { title: '模板名称', dataIndex: 'name' },
          // {
          //   title: '模板类型',
          //   dataIndex: 'type',
          //   valueEnum: {
          //     SYSTEM: { text: '系统' },
          //     ORDER: { text: '订单' },
          //     TRANSACTION: { text: '交易' },
          //     PRODUCT: { text: '产品' },
          //     MARKETING: { text: '营销' },
          //     OTHER: { text: '其他' },
          //   },
          // },
          { title: '业务类型', dataIndex: 'businessType', search: false },
          {
            title: '状态',
            dataIndex: 'isEnabled',
            search: false,
            render: (_, record) => (
              record.isEnabled ?
                <Tag color="green">已启用</Tag> :
                <Tag color="red">已禁用</Tag>
            ),
          },
          { title: '创建时间', dataIndex: 'createdAt', search: false, valueType: 'dateTime' },
          {
            title: '操作',
            valueType: 'option',
            key: 'option',
            render: (_, record) => {
              return (
                <Space>
                  <a onClick={() => showTemplateModal(record)}>编辑</a>
                  <Popconfirm
                    title={record.isEnabled ? "确定禁用此模板?" : "确定启用此模板?"}
                    onConfirm={() => handleToggleStatus(record.id, record.isEnabled)}
                  >
                    <a>{record.isEnabled ? '禁用' : '启用'}</a>
                  </Popconfirm>
                  <Popconfirm
                    title="确定删除此模板?"
                    onConfirm={() => handleDelete(record.id)}
                  >
                    <a style={{ color: 'red' }}>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
        request={async (params) => {
          const filters: TQueryFilter[] = [];

          if (params.code) {
            filters.push({ field: 'code', operator: 'like', value: params.code });
          }
          if (params.name) {
            filters.push({ field: 'name', operator: 'like', value: params.name });
          }
          if (params.type) {
            filters.push({ field: 'type', operator: 'eq', value: params.type });
          }

          const queryParams: TPageQuery = {
            current: params.current || 1,
            size: params.pageSize || 10,
            filters,
            sorts: [{ field: 'createdAt', direction: 'desc' }],
          };

          try {
            const res = await apiNotificationTemplatePage(queryParams);
            return {
              total: res.total,
              data: res.records,
              success: true,
            };
          } catch (error) {
            console.error('获取数据失败', error);
            message.error('获取数据失败，请重试');
            return {
              total: 0,
              data: [],
              success: false,
            };
          }
        }}
      />
      {templateFormModalHolder}
    </div>
  );
}

export default NotificationList;

NotificationList.auth = ['apiNotificationTemplateCreate', 'apiNotificationTemplateDelete', 'apiNotificationTemplateDisable', 'apiNotificationTemplateEnable', 'apiNotificationTemplatePage', 'apiNotificationTemplateUpdate'];
