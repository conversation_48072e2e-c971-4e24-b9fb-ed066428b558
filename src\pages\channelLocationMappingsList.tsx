import {
  apiListChannelLocationMappings,
  apiGetChannelLocationMappingById,
  apiAssignLocation,
  apiUnassignLocation,
  apiBatchAssignLocation,
  apiBatchUnassignLocation
} from '@/apis/gen.apis';
import { useFormModal } from '@/components/FormilyModal';
import I18nText from '@/components/I18nText';
import useLocations from '@/hooks/useLocations';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { App, Button, Space, Tag, Popconfirm } from 'antd';
import { useRef, useState } from 'react';

type TAssignFormData = {
  channelId: string;
  locationId: number;
};

type TBatchAssignFormData = {
  ids: number[];
  locationId: number;
};

function ChannelLocationMappingsList() {
  const tableRef = useRef<ActionType>();
  const { message } = App.useApp();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const { locationTreeOptions } = useLocations();

  const { formModalRef: assignFormModalRef, formModalHolder: assignFormModalHolder } =
    useFormModal<TAssignFormData>();

  const { formModalRef: batchAssignFormModalRef, formModalHolder: batchAssignFormModalHolder } =
    useFormModal<TBatchAssignFormData>();

  const showAssignModal = useMemoizedFn((record?: TChannelLocationMappingDTO) => {
    assignFormModalRef.current?.show({
      modalTitle: '分配地理位置',
      modalWidth: 600,
      onAutoSubmit: async (values: any) => {
        if (record?.id) {
          await apiAssignLocation(record.id.toString(), values.locationId[values.locationId.length - 1]);
        }
        message.success('分配成功');
        tableRef.current?.reload();
      },
      initialValues: record ? { channelId: record.externalCode } : {},
      schema: {
        type: 'object',
        properties: {
          channelId: {
            title: '渠道ID',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              disabled: !!record,
            },
          },
          locationId: {
            title: '地理位置ID',
            type: 'number',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Cascader',
            'x-component-props': {
              options: locationTreeOptions,
              changeOnSelect: true,
              
            },
          },
        },
      },
    });
  });

  const showBatchAssignModal = useMemoizedFn(() => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要批量分配的记录');
      return;
    }

    batchAssignFormModalRef.current?.show({
      modalTitle: '批量分配地理位置',
      modalWidth: 600,
      onAutoSubmit: async (values: any) => {
        await apiBatchAssignLocation({
          ids: selectedRowKeys as number[],
          locationId: values.locationId,
        });
        message.success('批量分配成功');
        setSelectedRowKeys([]);
        tableRef.current?.reload();
      },
      initialValues: {},
      schema: {
        type: 'object',
        properties: {
          locationId: {
            title: '地理位置ID',
            type: 'number',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
          },
        },
      },
    });
  });

  const handleUnassign = useMemoizedFn(async (id: string) => {
    await apiUnassignLocation(id);
    message.success('取消分配成功');
    tableRef.current?.reload();
  });

  const handleBatchUnassign = useMemoizedFn(async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要批量取消分配的记录');
      return;
    }

    await apiBatchUnassignLocation({
      ids: selectedRowKeys as string[],
    });
    message.success('批量取消分配成功');
    setSelectedRowKeys([]);
    tableRef.current?.reload();
  });

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  return (
    <div>
      <ProTable<TChannelLocationMappingDTO>
        actionRef={tableRef}
        headerTitle='渠道地理位置映射管理'
        rowKey='id'
        rowSelection={rowSelection}
        // toolBarRender={() => [
        //   <Button key='assign' type='primary' onClick={() => showAssignModal()}>
        //     分配地理位置
        //   </Button>,
        //   <Button
        //     key='batchAssign'
        //     onClick={showBatchAssignModal}
        //     disabled={selectedRowKeys.length === 0}
        //   >
        //     批量分配
        //   </Button>,
        //   <Popconfirm
        //     key='batchUnassign'
        //     title="确定要批量取消分配选中的记录吗？"
        //     onConfirm={handleBatchUnassign}
        //     disabled={selectedRowKeys.length === 0}
        //     okText="确定"
        //     cancelText="取消"
        //   >
        //     <Button danger disabled={selectedRowKeys.length === 0}>
        //       批量取消分配
        //     </Button>
        //   </Popconfirm>,
        // ]}
        search={{
          defaultCollapsed: false,
        }}
        columns={[
          { title: 'ID', dataIndex: 'id', width: 80 },
          {
            title: '渠道类型',
            dataIndex: 'channelType',
            width: 120,
            valueEnum: {
              0: '自营',
              1: 'IPNUX',
            },
          },
          { title: '外部代码', dataIndex: 'externalCode' },
          {
            title: '地理位置', dataIndex: 'locationId',
            valueType: 'cascader', fieldProps: { options: locationTreeOptions }
          },
          {
            title: '外部数据',
            dataIndex: 'externalData',
            search: false,
            render: (_, record) => {
              try {
                const data = JSON.parse(record.externalData);
                if (record.channelType as any === 1) {
                  return data.cityName
                }
              } catch (error) {
                return '-';
              }
            },
            // ellipsis: true,
          },
          {
            title: '创建时间',
            dataIndex: 'createdAt',
            width: 180,
            valueType: 'dateTime',
            search: false,
          },
          {
            title: '操作',
            valueType: 'option',
            key: 'option',
            render: (_text, record) => {
              return (
                <Space>
                  <a onClick={() => showAssignModal(record)}>重新分配</a>
                  <Popconfirm
                    title="确定要取消分配这个地理位置吗？"
                    onConfirm={() => handleUnassign(record.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <a style={{ color: 'red' }}>取消分配</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
        request={async (params) => {
          const filters: any[] = [];

          if(params.id){
            filters.push({ field: 'id', operator: 'eq', value: params.id });
          }

          if (params.channelType) {
            filters.push({ field: 'channelType', operator: 'eq', value: params.channelType });
          }
          if (params.externalCode) {
            filters.push({ field: 'externalCode', operator: 'eq', value: params.externalCode });
          }
          if (params.locationId && params.locationId.length > 0) {
            filters.push({ field: 'locationId', operator: 'eq', value: params.locationId[params.locationId.length - 1] });
          }

          const res = await apiListChannelLocationMappings({
            current: (params.current || 1) - 1,
            size: params.pageSize || 20,
            filters,
            sorts: [],
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}
      />
      {assignFormModalHolder}
      {batchAssignFormModalHolder}
    </div>
  );
}

export default ChannelLocationMappingsList;

ChannelLocationMappingsList.auth = [
  'apiListChannelLocationMappings',
  'apiGetChannelLocationMappingById',
  'apiAssignLocation',
  'apiUnassignLocation',
  'apiBatchAssignLocation',
  'apiBatchUnassignLocation'
];
