import React from 'react';
import { Modal, Card, Row, Col } from 'antd';
import { 
  PictureOutlined, 
  ShoppingOutlined, 
  TagOutlined, 
  FileTextOutlined, 
  PushpinOutlined 
} from '@ant-design/icons';
import { ModuleType } from './types';

interface ModuleSelectorProps {
  visible: boolean;
  onCancel: () => void;
  onSelect: (moduleType: ModuleType) => void;
}

const ModuleSelector: React.FC<ModuleSelectorProps> = ({
  visible,
  onCancel,
  onSelect
}) => {
  const moduleTypes = [
    {
      type: ModuleType.Carousel,
      title: '轮播图',
      icon: <PictureOutlined style={{ fontSize: 32 }} />,
      description: '添加一个轮播图模块，可以展示多张图片'
    },
    {
      type: ModuleType.ProductList,
      title: '商品列表',
      icon: <ShoppingOutlined style={{ fontSize: 32 }} />,
      description: '添加一个商品列表模块，展示多个商品'
    },
    {
      type: ModuleType.BrandList,
      title: '品牌列表',
      icon: <TagOutlined style={{ fontSize: 32 }} />,
      description: '添加一个品牌列表模块，展示多个品牌'
    },
    {
      type: ModuleType.HotspotImage,
      title: '热区图',
      icon: <PushpinOutlined style={{ fontSize: 32 }} />,
      description: '添加一个热区图模块，可以在图片上添加多个链接区域'
    }
  ];

  return (
    <Modal
      title="选择模块类型"
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
    >
      <Row gutter={[16, 16]}>
        {moduleTypes.map(moduleType => (
          <Col span={8} key={moduleType.type}>
            <Card
              hoverable
              className="text-center"
              onClick={() => onSelect(moduleType.type)}
            >
              <div className="py-4">
                <div className="mb-2 text-blue-500">{moduleType.icon}</div>
                <div className="text-lg font-medium mb-2">{moduleType.title}</div>
                <div className="text-gray-500 text-sm">{moduleType.description}</div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </Modal>
  );
};

export default ModuleSelector;
