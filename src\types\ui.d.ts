type TPreviewFile = {
  name: string;
  url: string;
  previewUrl: string;
  type: 'image' | 'video' | 'unknown';
};

type TAgreeOption = {
  value: string;
  label: string;
  data?: any; // 额外数据
  children?: TAgreeOption[];
};

type TAgreeMapOption = TAgreeOption & {
  parentValues: string[];
  childrenValues: string[];
  upValues: string[];
  downValues: string[];
};

type TAgreeDict = {
  name: string;
  options: TAgreeOption[];
  labels: string[];
  values: string[];
  valueLabelMap: Record<string, string>;
  labelValueMap: Record<string, string>;
  valueDataMap: {
    [key: string]: TAgreeMapOption;
  };
  labelDataMap: {
    [key: string]: TAgreeMapOption;
  };
  match(value: string | number, ...labels: string[]): boolean;
};

type TAgreeDicts = {
  [key in keyof TBaseAgreeDicts]: TAgreeDict;
};


type TI18nFieldValue = Record<string, any>