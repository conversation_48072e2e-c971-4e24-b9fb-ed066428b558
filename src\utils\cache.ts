class CacheUtils {
  static _prefix = import.meta.env.VITE_CACHE_KEY_PREFIX; // 缓存key前缀
  /**
   * 根据key获取缓存数据
   * @param key 
   * @returns 
   */
  static getItem<T>(key: string): T | undefined {
    const strData = localStorage.getItem(CacheUtils._prefix + key);
    let res: T | undefined = undefined;
    if (strData) {
      try {
        const data = JSON.parse(strData);
        if(data && (!data.expiredAt || (new Date(data.expiredAt) > new Date()))) {
          return data.value as T
        }
        return undefined
      } catch (e) {
        /* empty */
        return undefined;
      }
    }
    return res;
  }

  /**
   * 根据key设置缓存数据
   * @param key 
   * @param value
   * @returns 
   */
  static setItem(key: string, value: unknown, expiredAt: number = 0): void {
    localStorage.setItem(CacheUtils._prefix + key, JSON.stringify({
      value,
      expiredAt: expiredAt || 0,
    }));
  }

  /**
   * 根据key删除缓存数据
   * @param key 
   */
  static removeItem(key: string): void {
    localStorage.removeItem(CacheUtils._prefix + key);
  }
}

export default CacheUtils;
