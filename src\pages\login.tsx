import { useRequest } from 'ahooks';
import { authModel } from '@/stores/authModel';
import { useNavigate } from 'react-router-dom';
import { SchemaField, useForm } from '@/components/Formily';
import { FormProvider } from '@formily/react';
import { FormLayout, Submit } from '@formily/antd-v5';
import { useModel } from 'foca';
import { systemSettingsModel } from '@/stores/systemSettingsModel';
import { useEffect } from 'react';
import { Image } from 'antd';
type TLoginFormData = {
  username: string;
  password: string;
}

function LoginPage() {
  const navigate = useNavigate();
  const form = useForm<TLoginFormData>();
  const { websiteLogo, initialized } = useModel(systemSettingsModel);

  // Load system settings when component mounts
  // useEffect(() => {
  //   if (!initialized) {
  //     systemSettingsModel.loadSettings();
  //   }
  // }, [initialized]);

  const { loading, runAsync: submit } = useRequest(async (values: TLoginFormData) => {
    await authModel.loginByPwd(values)
    navigate('/')
  }, { manual: true })

  return (
    <div className="flex items-center justify-center w-full h-screen LoginPage bg-slate-800">
      <div className="flex flex-col p-8 bg-white rounded-lg w-[300px]">
        <div className='flex flex-col items-center'>
          {websiteLogo ? (
            <Image
              className="block h-12"
              src={websiteLogo}
              alt="Logo"
              preview={false}
              style={{ objectFit: 'contain' }}
            />
          ) : (
            <img className="block h-12" src='/images/logo.svg' />
          )}
          <div className='py-6 text-xl font-bold'>登录</div>
        </div>
        <FormProvider form={form}>
          <FormLayout layout="vertical">
            <SchemaField>
              <SchemaField.String
                name="username"
                title="用户"
                x-decorator="FormItem"
                x-component="Input"
                required
              />
              <SchemaField.String
                name="password"
                title="密码"
                x-decorator="FormItem"
                x-component="Input"
                required
                x-component-props={{
                  type: 'password',
                } as any}
              />
            </SchemaField>
          </FormLayout>

          <Submit className="w-full mt-2" onSubmit={submit} loading={loading}>登录</Submit>
        </FormProvider>
      </div>
    </div>
  );
}

export default LoginPage;

LoginPage.auth = [];
