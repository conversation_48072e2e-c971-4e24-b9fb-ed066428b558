import { apiCreateApplication, apiGetApplications, apiUpdateApplication, apiDeleteApplication } from '@/apis/gen.apis';
import { useFormModal } from '@/components/FormilyModal';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { App, Button, Space, Popconfirm } from 'antd';
import { useRef } from 'react';

type TEditFormData = {
  userId: string;
  appName: string;
  description: string;
  ipWhitelist: string;
  expiresAt: string;
  remarks: string;
};

interface ApiApplicationListComponentProps {
  userId?: string; // 可选的用户ID，用于过滤该用户的应用
  showUserIdColumn?: boolean; // 是否显示用户ID列
  headerTitle?: string; // 自定义标题
  showAddButton?: boolean; // 是否显示新增按钮
}

function ApiApplicationListComponent({ 
  userId, 
  showUserIdColumn = true, 
  headerTitle = 'API应用列表',
  showAddButton = true 
}: ApiApplicationListComponentProps) {
  const tableRef = useRef<ActionType>();
  const { message } = App.useApp();

  const { formModalRef: editFormModalRef, formModalHolder: editFormModalHolder } =
    useFormModal<TEditFormData>();

  const showEditModal = useMemoizedFn((record?: TApiApplicationBO) => {
    editFormModalRef.current?.show({
      modalTitle: record ? '修改API应用' : '新增API应用',
      modalWidth: 1000,
      onAutoSubmit: async (values) => {
        // 如果传入了userId，自动设置到表单数据中
        const formData = {
          ...values,
          expiresAt: values.expiresAt ? new Date(values.expiresAt).toISOString() : '',
        };
        
        if (record?.id) {
          await apiUpdateApplication(record.id, formData);
        } else {
          await apiCreateApplication(formData);
        }

        message.success('提交成功');
        tableRef.current?.reload();
      },
      initialValues: record
        ? {
            ...record,
            expiresAt: record.expiresAt ? new Date(record.expiresAt).toISOString() : '',
          }
        : userId ? { userId } : {},
      schema: {
        type: 'object',
        properties: {
          userId: {
            title: '用户ID',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-disabled': !!userId, // 如果传入了userId，则禁用编辑
          },
          appName: {
            title: '应用名称',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          description: {
            title: '描述',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
          },
          ipWhitelist: {
            title: 'IP白名单',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              placeholder: '多个IP用逗号分隔',
            },
          },
          expiresAt: {
            title: '过期时间',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'DatePicker',
            'x-component-props': {
              showTime: true,
              format: 'YYYY-MM-DD HH:mm:ss',
            },
          },
          remarks: {
            title: '备注',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
          },
        },
      },
    });
  });

  const handleDelete = useMemoizedFn(async (id: string) => {
    await apiDeleteApplication(id);
    message.success('删除成功');
    tableRef.current?.reload();
  });

  // 构建列配置
  const columns = [
    { title: 'App ID', dataIndex: 'appId', width: 200 },
    { title: '应用名称', dataIndex: 'appName' },
    ...(showUserIdColumn ? [{ title: '用户ID', dataIndex: 'userId' }] : []),
    { title: '描述', dataIndex: 'description', ellipsis: true, search: false },
    { 
      title: 'IP白名单', 
      dataIndex: 'ipWhitelist', 
      ellipsis: true,
      width: 150,
      search: false,
    },
    { 
      title: '过期时间', 
      dataIndex: 'expiresAt',
      valueType: 'dateTime' as const,
      width: 180,
      search: false,
    },
    { 
      title: '创建时间', 
      dataIndex: 'createdAt',
      valueType: 'dateTime' as const,
      width: 180,
      search: false,
    },
    {
      title: '操作',
      valueType: 'option' as const,
      key: 'option',
      width: 150,
      render: (_text: any, record: TApiApplicationBO) => {
        return (
          <Space>
            <a onClick={() => showEditModal(record)}>修改</a>
            <Popconfirm
              title="确定要删除这个API应用吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <a style={{ color: 'red' }}>删除</a>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <ProTable<TApiApplicationBO>
        actionRef={tableRef}
        headerTitle={headerTitle}
        rowKey='id'
        toolBarRender={() => showAddButton ? [
          <Button key='add' type='primary' onClick={() => showEditModal()}>
            新增
          </Button>,
        ] : []}
        search={{
          defaultCollapsed: false,
        }}
        columns={columns}
        request={async (params, sorter) => {
          const filters: any[] = [];

          if (params.appName) {
            filters.push({ field: 'appName', operator: 'eq', value: params.appName });
          }
          if (params.appId) {
            filters.push({ field: 'appId', operator: 'eq', value: params.appId });
          }

          if(userId){
            filters.push({ field: 'userId', operator: 'eq', value: userId });
          } 
      
          if (params.userId && !userId) { // 只有在没有预设userId时才允许搜索
            filters.push({ field: 'userId', operator: 'eq', value: params.userId });
          }

          const res = await apiGetApplications({
            current: params.current - 1,
            size: params.pageSize,
            filters,
            sorts: [],
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}
      />
      {editFormModalHolder}
    </div>
  );
}

export default ApiApplicationListComponent;
