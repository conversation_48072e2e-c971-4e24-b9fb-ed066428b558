import { apiArticleCreateOrUpdate, apiArticleGetById, apiLanguagePageList } from "@/apis/apis.api";
import { SchemaField, useForm } from "@/components/Formily";
import { FileUtils } from "@/utils/file";
import { ProCard } from "@ant-design/pro-components";
import { FormLayout, Submit } from "@formily/antd-v5";
import { FormProvider } from "@formily/react";
import { useAsyncEffect, useRequest } from "ahooks";
import { App, UploadFile, Button } from "antd";
import { $emit } from "eventbus-z";
import { useEffect, useMemo, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

type TArticleForm = {
  id: string;
  code: string;
  title: string;
  content: string;
  type: string;
  author: string;
  headerImageFile: UploadFile[];
  publishDate: string;
  isActive: boolean;
  summary: string;
  sortOrder: number;
  languageCode?: string;
}

type TArticleBo = {
  id: string;
  code: string;
  title: string;
  content: string;
  type: string;
  author: string;
  headerImage: string;
  publishDate: string;
  isActive: boolean;
  summary: string;
  sortOrder: number;
  languageCode?: string;
}

function ArticleEdit() {
  const location = useLocation();
  const navigate = useNavigate();
  const { message } = App.useApp();

  const form = useForm<TArticleForm>({});

  const id = useMemo(() => location?.state?.id, [location?.state?.id])
  const viewMode = useMemo(() => location?.state?.viewMode, [location?.state?.viewMode])

  const [article, setArticle] = useState<TArticleBo>()

  // 文章类型选项
  const ARTICLE_TYPES = [
    { label: '服务条款/隐私政策', value: 0 },
    { label: '新闻', value: 1 },
    { label: '用户指南/教程', value: 2 },
    { label: '公告', value: 3 },
    { label: '常见问题', value: 4 },
    { label: '关于我们', value: 5 },
    { label: '其他', value: 6 }
  ];

  // 获取所有激活的语言
  const { data: languages = [] } = useRequest(
    () => apiLanguagePageList({
      current: 0,
      size: 100,
      filters: [{ field: 'isActive', operator: 'eq', value: 1 }]
    }).then(res => res.records || []),
    {
      manual: false,
      cacheKey: 'active-languages',
    }
  );

  // 语言选项
  const LANGUAGE_OPTIONS = languages.map(lang => ({
    label: `${lang.nativeName} (${lang.code})`,
    value: lang.code
  }));

  useAsyncEffect(async () => {
    if (id) {
      // 获取文章详情
      const article = await apiArticleGetById(id)
      setArticle(article)
    }
  }, [id])

  useEffect(() => {
    if (!article) return
    const formData: TArticleForm = {
      id: article.id,
      code: article.code,
      title: article.title,
      content: article.content,
      type: article.type,
      author: article.author,
      headerImageFile: article.headerImage ? FileUtils.urlsToUploadFiles([article.headerImage]) : [],
      publishDate: article.publishDate,
      isActive: article.isActive,
      summary: article.summary,
      sortOrder: article.sortOrder,
      languageCode: article.languageCode
    }
    form.setValues(formData)
  }, [article])

  const { loading, runAsync: submit } = useRequest(async (values: TArticleForm) => {
    // 转换为API需要的格式
    const submitData: Partial<TArticleBo> = {
      id: values.id,
      code: values.code,
      title: values.title,
      content: values.content,
      type: values.type,
      author: values.author,
      headerImage: FileUtils.uploadFilesToUrls(values.headerImageFile || [])[0] || '',
      publishDate: values.publishDate,
      isActive: values.isActive,
      summary: values.summary,
      sortOrder: values.sortOrder,
      languageCode: values.languageCode
    }
    
    await apiArticleCreateOrUpdate(submitData)
    message.success("提交成功")
    $emit('articleListReload')
    navigate(-1)
  }, { manual: true })


  return (
    <ProCard title="文章编辑" className="max-w-1000px mx-auto pb-20px">
      <FormProvider form={form}>
        <FormLayout layout="vertical" disabled={viewMode}>
          <SchemaField>
            <SchemaField.Markup
              name="languageCode"
              title="语言"
              x-decorator="FormItem"
              x-component="Select"
              enum={LANGUAGE_OPTIONS}
              x-component-props={{ 
                placeholder: "请选择语言",
                allowClear: true,
                showSearch: true,
                optionFilterProp: "label"
              }}
              required
            />
            <SchemaField.String
              name="title"
              title="标题"
              x-decorator="FormItem"
              x-component="Input"
              x-validator={[]}
              required
            />
            <SchemaField.String
              name="code"
              title="编码"
              x-decorator="FormItem"
              x-component="Input"
              x-validator={[]}
            />
            <SchemaField.Markup
              name="type"
              title="类型"
              x-decorator="FormItem"
              x-component="Select"
              enum={ARTICLE_TYPES}
              required
            />
            <SchemaField.String
              name="author"
              title="作者"
              x-decorator="FormItem"
              x-component="Input"
              x-validator={[]}
              required
            />
            <SchemaField.String
              name="content"
              title="内容"
              x-decorator="FormItem"
              x-component="RichText"
              x-validator={[]}
              required
            />
            <SchemaField.Markup
              name="headerImageFile"
              title="封面图片"
              x-decorator="FormItem"
              x-component="FileUpload"
              x-component-props={{ listType: "picture-card", maxCount: 1 }}
              x-validator={[]}
            />
            <SchemaField.String
              name="publishDate"
              title="发布日期"
              x-decorator="FormItem"
              x-component="DatePicker"
              x-component-props={{ 
                showTime: true,
                format: "YYYY-MM-DD HH:mm:ss"
              } as any}
              x-validator={[]}
            />
            <SchemaField.Boolean
              name="isActive"
              title="是否激活"
              x-decorator="FormItem"
              x-component="Switch"
              x-validator={[]}
              default={true}
              x-component-props={{ checkedChildren: "是", unCheckedChildren: "否" }}
            />
            <SchemaField.String
              name="summary"
              title="摘要"
              x-decorator="FormItem"
              x-component="Input.TextArea"
              x-validator={[]}
              x-component-props={{ rows: 4 }}
            />
            <SchemaField.Number
              name="sortOrder"
              title="排序"
              x-decorator="FormItem"
              x-component="NumberPicker"
              x-validator={[]}
              default={0}
            />
          </SchemaField>
        </FormLayout>
        {!viewMode && <Submit onSubmit={submit} loading={loading} className="mt-4">提交</Submit>}
        {viewMode && (
          <Button onClick={() => navigate(-1)} className="mt-4">返回</Button>
        )}
      </FormProvider>
    </ProCard>
  );
}

export default ArticleEdit;

ArticleEdit.auth = ['apiArticleCreateOrUpdate', 'apiArticleGetById', 'apiLanguagePageList'];
