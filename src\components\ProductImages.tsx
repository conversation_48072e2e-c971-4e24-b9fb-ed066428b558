import React, { useState } from 'react';
import { Button, Typography, Upload, Space, message } from 'antd';
import { PlusOutlined, ArrowLeftOutlined, DeleteOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd/es/upload/interface';
import http from '@/utils/http';

// 定义文件上传响应结构
interface FileUploadResponse {
  id: number;
  originalFilename: string;
  storedFilename: string;
  contentType: string;
  size: number;
  extension: string;
  url: string;
  module: string;
  referenceId?: number;
  referenceType?: string;
  temporary: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ProductImageProps {
  images: string[];
  setImages: (images: string[]) => void;
  mainImage: string | null;
  setMainImage: (image: string) => void;
  isEdit: boolean;
  id?: string;
  submitting: boolean;
  onSave: () => void;
}

const ProductImages: React.FC<ProductImageProps> = ({
  images,
  setImages,
  mainImage,
  setMainImage,
  isEdit,
  id,
  submitting,
  onSave,
}) => {
  const [uploading, setUploading] = useState(false);

  // 处理图片上传
  const handleImageUpload: UploadProps['customRequest'] = async ({ file, onSuccess, onError }) => {
    if (!(file instanceof File)) {
      message.error('文件格式错误');
      onError && onError(new Error('文件格式错误'));
      return;
    }

    setUploading(true);
    
    try {
      // 创建FormData对象用于文件上传
      const formData = new FormData();
      formData.append('file', file);
      formData.append('module', 'product');
      
      // 如果是编辑状态，添加productId作为referenceId
      if (isEdit && id) {
        formData.append('referenceId', id);
        formData.append('referenceType', 'product');
        formData.append('temporary', 'false');
      } else {
        // 如果是新建状态，标记为临时文件
        formData.append('temporary', 'true');
      }

      // 使用http工具发送请求
      const response = await http.request<FileUploadResponse>({
        url: '/files',
        method: 'POST',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      // 获取返回的文件URL
      const fileUrl = "/api" + response.url;
      
      // 更新图片列表
      const newImages = [...images, fileUrl];
      setImages(newImages);
      
      // 如果是第一张图片，自动设置为主图
      if (images.length === 0 || !mainImage) {
        setMainImage(fileUrl);
      }
      
      message.success(`${file.name} 上传成功`);
      onSuccess && onSuccess(response);
    } catch (error) {
      console.error('上传失败:', error);
      message.error(`${file.name} 上传失败`);
      onError && onError(new Error('上传失败'));
    } finally {
      setUploading(false);
    }
  };

  // 设置主图
  const setAsMainImage = (imageUrl: string) => {
    setMainImage(imageUrl);
  };

  // 删除图片
  const removeImage = (index: number) => {
    const imageToRemove = images[index];
    const newImages = [...images];
    newImages.splice(index, 1);
    
    setImages(newImages);
    
    // 如果删除的是主图，则将第一张图片设为主图
    if (imageToRemove === mainImage && newImages.length > 0) {
      setMainImage(newImages[0]);
    } else if (newImages.length === 0) {
      setMainImage('');
    }
  };

  // 移动图片顺序
  const moveImage = (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === images.length - 1)
    ) {
      return;
    }

    const newImages = [...images];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;

    [newImages[index], newImages[targetIndex]] = [newImages[targetIndex], newImages[index]];

    setImages(newImages);
  };

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Upload
          name="file"
          listType="picture-card"
          showUploadList={false}
          customRequest={handleImageUpload}
          disabled={uploading}
        >
          <div>
            <PlusOutlined />
            <div style={{ marginTop: 8 }}>上传图片</div>
          </div>
        </Upload>
        {uploading && <Typography.Text type="secondary">正在上传...</Typography.Text>}
      </div>

      <div style={{ display: 'flex', flexWrap: 'wrap', gap: 16 }}>
        {images.map((imageUrl, index) => (
          <div
            key={index}
            style={{
              border: imageUrl === mainImage ? '2px solid #1890ff' : '1px solid #d9d9d9',
              borderRadius: 4,
              padding: 8,
              width: 200,
            }}
          >
            <img
              src={imageUrl}
              alt={`Product image ${index + 1}`}
              style={{ width: '100%', height: 150, objectFit: 'cover' }}
            />
            <div style={{ marginTop: 8 }}>
              <Space>
                <Button
                  type={imageUrl === mainImage ? 'primary' : 'default'}
                  size="small"
                  onClick={() => setAsMainImage(imageUrl)}
                >
                  主图
                </Button>
                <Button
                  size="small"
                  icon={<ArrowLeftOutlined />}
                  disabled={index === 0}
                  onClick={() => moveImage(index, 'up')}
                />
                <Button
                  size="small"
                  icon={<ArrowLeftOutlined style={{ transform: 'rotate(180deg)' }} />}
                  disabled={index === images.length - 1}
                  onClick={() => moveImage(index, 'down')}
                />
                <Button
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => removeImage(index)}
                />
              </Space>
            </div>
          </div>
        ))}
      </div>

      {images.length === 0 && (
        <Typography.Text type="secondary">暂无图片，请上传商品图片</Typography.Text>
      )}

      <div style={{ marginTop: 16 }}>
        <Typography.Text type="secondary" style={{ marginRight: 16 }}>
          已上传 {images.length} 张图片，主图：{mainImage ? '已设置' : '未设置'}
        </Typography.Text>
        <Button type="primary" onClick={onSave} loading={submitting} disabled={images.length === 0 || !mainImage}>
          保存
        </Button>
      </div>
    </div>
  );
};

export default ProductImages; 