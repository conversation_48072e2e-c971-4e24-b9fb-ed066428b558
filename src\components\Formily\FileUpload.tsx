import { UploadOutlined } from "@ant-design/icons";
import { App, Button, UploadProps } from "antd";
import { Upload } from "@formily/antd-v5";
import { Image, UploadFile } from "antd";
import {
  connect,
  mapReadPretty,
  useField,
} from '@formily/react'
import { useState } from "react";
import {FileUtils } from "@/utils/file";
import HttpUtils from "@/utils/http";

type TFileUploadProps = {
  maxCount?: number;
  listType: 'text' | 'picture' | 'picture-card' | 'picture-circle';
  value: string[];
  keepName?: boolean;
  saveMedia?: boolean;
}


const FileUpload = (props: TFileUploadProps) => {
  const field = useField() as any;
  const { message} = App.useApp();
  const [previewData, setPreviewData] = useState<{
    url: 'image' | 'video';
    type: string;
  }>();
  const [previewOpen, setPreviewOpen] = useState(false);

  // 处理图片上传
  const handleImageUpload: UploadProps['customRequest'] = async ({ file, onSuccess, onError }) => {
    if (!(file instanceof File)) {
      message.error('文件格式错误');
      onError && onError(new Error('文件格式错误'));
      return;
    }

  
    try {

      // 使用http工具发送请求
      const url = await HttpUtils.uploadFile(file);
      const response = {
        url
      };
   
      field.setValue([...(field.value || []), ...FileUtils.urlsToUploadFiles([ response.url])]);
      message.success(`${file.name} 上传成功`);
      onSuccess && onSuccess(response);
    } catch (error) {
      message.error(`${file.name} 上传失败`);
      onError && onError(new Error('上传失败'));
    }
  };

  // const actionUrl = useMemo(() => {
  //   return `${HttpUtils.API_SITE}/files`
  // }, [])

  const handlePreview = async (file: UploadFile) => {
    if (file.response?.url) {
      const ext = (file.response.url.split('.').pop()).toLowerCase()
      const type = {
        jpg: 'image',
        jpeg: 'image',
        png: 'image',
        gif: 'image',
        webp: 'image',
        '3gp': 'video',
        mpg: 'video',
        mpeg: 'video',
        mp4: 'video',
        m4v: 'video',
        m4p: 'video',
        ogv: 'video',
        ogg: 'video',
        mov: 'video',
        webm: 'video',
      }[ext]
      if (!type) return
      setPreviewData({
        type,
        url: file.response?.url
      })
      setPreviewOpen(true)
    }
  };
  return (
    <>
      <Upload
        listType={props.listType}
        {...props}
        customRequest={handleImageUpload}
        beforeUpload={FileUtils.beforeUpload}
        onPreview={handlePreview}
      >
        {props.maxCount && props.value?.length >= props.maxCount ? null : props.listType === "picture-card" ? (
          <UploadOutlined style={{ fontSize: 20 }} />
        ) : (
          <Button icon={<UploadOutlined />}>上传文件</Button>
        )}

      </Upload>
      {previewData && previewData.type === 'image' ? (
        <Image
          wrapperStyle={{ display: 'none' }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewData(undefined),
            destroyOnClose: true,
          }}
          src={previewData.url}
        />
      ) : null}
      {previewData && previewData.type === 'video' ? (

        <Image
          wrapperStyle={{ display: 'none' }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewData(undefined),
            destroyOnClose: true,
            imageRender: () => (
              <video
                muted
                // width="100%"
                controls
                src={previewData.url}
              />
            ),
            toolbarRender: () => null,
          }}
        // src={previewImage}
        />
      ) : null}
    </>

  );
};

export default connect(
  FileUpload,
  mapReadPretty(() => null),
);
