import { defineConfig, loadEnv } from 'vite';
import path from 'path';
import React from '@vitejs/plugin-react-swc';
import UnoCSS from 'unocss/vite';
import { analyzer } from 'vite-bundle-analyzer';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd()); 
  return {
    build: {
      // sourcemap: true,
      chunkSizeWarningLimit: 10000,
    },
    plugins: [
      React(),
      UnoCSS(),
      mode === 'analyze' ? analyzer() : undefined,
    ],
    server: {
      host: true,
      port: 20001,
      proxy: {
        '/api': {
          target: env.VITE_API_SITE,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
      }
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src/'),
      },
    },
  };
});
