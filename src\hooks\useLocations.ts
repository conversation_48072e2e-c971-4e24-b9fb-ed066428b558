import { useRequest } from "ahooks";
import { apiListLocations } from "@/apis/gen.apis";
import { ToolsUtil } from "@/utils/tools";

type TLocationTreeOption = {
  label: any;
  value: any;
  children: TLocationTreeOption[];
}


function locationToTree(locations: TLocationDTO[], parentId?: string): TLocationTreeOption[] {
  const result: TLocationTreeOption[] = [];
  locations.forEach((item) => {
    if (item.parentId === parentId || (!parentId && item.parentId == '0')) {
      result.push({
        label: ToolsUtil.i18nText(item.name),
        value: item.id,
        children: locationToTree(locations, item.id),
      });
    }
  });
  return result;
}

export default function useLocations() {
  const { data: locations = [] } = useRequest(async () => {
    return (await apiListLocations({
      current: 0,
      size: 10000,
    })).records;
  });
  
  return {
    locations,
    locationsEnum: locations.reduce((acc, item) => {
      acc[item.id] = ToolsUtil.i18nText(item.name);
      return acc;
    }, {} as Record<string, string>),
    locationTreeOptions: locationToTree(locations),
  };
}