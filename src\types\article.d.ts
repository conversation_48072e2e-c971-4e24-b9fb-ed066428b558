/**
 * 文章相关数据模型
 */

// 文章类型枚举
enum ArticleTypeEnum {
  /**
   * Terms of service, privacy policy, etc.
   */
  POLICY,

  /**
   * News articles
   */
  NEWS,

  /**
   * User guides and tutorials
   */
  GUIDE,

  /**
   * Announcements
   */
  ANNOUNCEMENT,

  /**
   * FAQ
   */
  FAQ,

  /**
   * About us
   */
  ABOUT,

  /**
   * Other
   */
  OTHER
}

// 文章业务对象
interface TArticleBo {
  id: string;
  code: string;
  title: string;
  content: string;
  type: ArticleTypeEnum;
  author: string;
  headerImage: string;
  publishDate: string;
  isActive: boolean;
  summary: string;
  viewCount: number;
  sortOrder: number;
  languageCode?: string;
  createdAt: string;
  updatedAt: string;
}

// 文章分页结果
interface TPageResultArticleBo {
  records: TArticleBo[];
  total: number;
  size: number;
  current: number;
}

// 文章分页查询参数
interface TArticlePageQuery extends TPageListReq {
  keyword?: string;
  type?: ArticleTypeEnum;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  languageCode?: string;
}

// 文章表单数据类型
interface TArticleForm {
  id?: number;
  code?: string;
  title: string;
  content: string;
  type: ArticleTypeEnum;
  author: string;
  headerImageFile?: UploadFile[];
  publishDate?: string;
  isActive: boolean;
  summary?: string;
  sortOrder?: number;
  languageCode?: string;
}
