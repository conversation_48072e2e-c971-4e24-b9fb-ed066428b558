---
openapi: 3.1.0
paths:
  /admin/channel-business-type-mappings/batch/assign:
    put:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BatchAssignBusinessTypeBO"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BatchOperationResultDTO"
        "400":
          description: Bad Request
      summary: Batch Assign Business Type
      tags:
      - Channel Business Type Mapping Admin Controller
  /admin/channel-business-type-mappings/batch/unassign:
    put:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BatchUnassignBO"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BatchOperationResultDTO"
        "400":
          description: Bad Request
      summary: Batch Unassign Business Type
      tags:
      - Channel Business Type Mapping Admin Controller
  /admin/channel-business-type-mappings/page:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PageQuery"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageResultChannelBusinessTypeMappingDTO"
        "400":
          description: Bad Request
      summary: List
      tags:
      - Channel Business Type Mapping Admin Controller
  /admin/channel-business-type-mappings/{id}:
    get:
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChannelBusinessTypeMappingDTO"
      summary: Get By Id
      tags:
      - Channel Business Type Mapping Admin Controller
  /admin/channel-business-type-mappings/{id}/assign/{businessTypeId}:
    put:
      parameters:
      - name: businessTypeId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChannelBusinessTypeMappingDTO"
      summary: Assign Business Type
      tags:
      - Channel Business Type Mapping Admin Controller
  /admin/channel-business-type-mappings/{id}/unassign:
    put:
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChannelBusinessTypeMappingDTO"
      summary: Unassign Business Type
      tags:
      - Channel Business Type Mapping Admin Controller
  /admin/channel-location-mappings/batch/assign:
    put:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BatchAssignLocationBO"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BatchOperationResultDTO"
        "400":
          description: Bad Request
      summary: Batch Assign Location
      tags:
      - Channel Location Mapping Admin Controller
  /admin/channel-location-mappings/batch/unassign:
    put:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BatchUnassignBO"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BatchOperationResultDTO"
        "400":
          description: Bad Request
      summary: Batch Unassign Location
      tags:
      - Channel Location Mapping Admin Controller
  /admin/channel-location-mappings/page:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PageQuery"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageResultChannelLocationMappingDTO"
        "400":
          description: Bad Request
      summary: List
      tags:
      - Channel Location Mapping Admin Controller
  /admin/channel-location-mappings/{id}:
    get:
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChannelLocationMappingDTO"
      summary: Get By Id
      tags:
      - Channel Location Mapping Admin Controller
  /admin/channel-location-mappings/{id}/assign/{locationId}:
    put:
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      - name: locationId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChannelLocationMappingDTO"
      summary: Assign Location
      tags:
      - Channel Location Mapping Admin Controller
  /admin/channel-location-mappings/{id}/unassign:
    put:
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChannelLocationMappingDTO"
      summary: Unassign Location
      tags:
      - Channel Location Mapping Admin Controller
  /admin/locations:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LocationBO"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LocationDTO"
        "400":
          description: Bad Request
      summary: Create
      tags:
      - Location Admin Controller
  /admin/locations/page:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PageQuery"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageResultLocationDTO"
        "400":
          description: Bad Request
      summary: List
      tags:
      - Location Admin Controller
  /admin/locations/{id}:
    put:
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LocationBO"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LocationDTO"
        "400":
          description: Bad Request
      summary: Update
      tags:
      - Location Admin Controller
    get:
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LocationDTO"
      summary: Get By Id
      tags:
      - Location Admin Controller
    delete:
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "204":
          description: No Content
      summary: Delete
      tags:
      - Location Admin Controller
  /admin/login:
    post:
      summary: 账号登录
      tags:
      - 管理员安全接口
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UsernameLoginRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SessionInfoLongAdminUserBo"
        "400":
          description: Bad Request
  /admin/logout:
    post:
      summary: 账号登出
      tags:
      - 管理员安全接口
      responses:
        "201":
          description: Created
  /admin/permissions:
    get:
      summary: 获取所有管理员权限
      tags:
      - 管理员权限管理
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/AdminPermissionBo"
    post:
      summary: 创建新的管理员权限
      tags:
      - 管理员权限管理
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAdminPermissionRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminPermissionBo"
        "400":
          description: Bad Request
  /admin/permissions/code/{code}:
    get:
      summary: 通过代码获取管理员权限
      tags:
      - 管理员权限管理
      parameters:
      - name: code
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminPermissionBo"
  /admin/permissions/page:
    post:
      summary: 分页获取管理员权限
      tags:
      - 管理员权限管理
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PageQuery"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageResultAdminPermissionBo"
        "400":
          description: Bad Request
  /admin/permissions/{id}:
    put:
      summary: 更新管理员权限
      tags:
      - 管理员权限管理
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAdminPermissionRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminPermissionBo"
        "400":
          description: Bad Request
    get:
      summary: 通过ID获取管理员权限
      tags:
      - 管理员权限管理
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminPermissionBo"
  /admin/permissions/{id}/active:
    patch:
      summary: 设置管理员权限的激活状态
      tags:
      - 管理员权限管理
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      - name: active
        in: query
        schema:
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminPermissionBo"
  /admin/roles:
    get:
      summary: 获取所有管理员角色
      tags:
      - 管理员角色管理
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/AdminRoleBo"
    post:
      summary: 创建新的管理员角色
      tags:
      - 管理员角色管理
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAdminRoleRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminRoleBo"
        "400":
          description: Bad Request
  /admin/roles/code/{code}:
    get:
      summary: 通过代码获取管理员角色
      tags:
      - 管理员角色管理
      parameters:
      - name: code
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminRoleBo"
  /admin/roles/page:
    post:
      summary: 分页获取管理员角色
      tags:
      - 管理员角色管理
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PageQuery"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageResultAdminRoleBo"
        "400":
          description: Bad Request
  /admin/roles/{id}:
    put:
      summary: 更新管理员角色
      tags:
      - 管理员角色管理
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAdminRoleRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminRoleBo"
        "400":
          description: Bad Request
    get:
      summary: 通过ID获取管理员角色
      tags:
      - 管理员角色管理
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminRoleBo"
  /admin/roles/{id}/active:
    patch:
      summary: 设置管理员角色激活状态
      tags:
      - 管理员角色管理
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      - name: active
        in: query
        schema:
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminRoleBo"
  /admin/roles/{id}/permissions:
    get:
      summary: 通过ID获取管理员角色权限
      tags:
      - 管理员角色管理
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
    post:
      summary: 为管理员角色分配权限（通过权限ID）
      tags:
      - 管理员角色管理
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              type: array
              uniqueItems: true
              items:
                type: integer
                format: int64
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminRoleBo"
        "400":
          description: Bad Request
  /admin/roles/{id}/permissions/codes:
    post:
      summary: 为管理员角色分配权限（通过权限代码）
      tags:
      - 管理员角色管理
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              type: array
              uniqueItems: true
              items:
                type: string
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminRoleBo"
        "400":
          description: Bad Request
  /admin/roles/{roleId}/permissions/code/{permissionCode}:
    delete:
      summary: 从管理员角色中移除权限（通过权限代码）
      tags:
      - 管理员角色管理
      parameters:
      - name: permissionCode
        in: path
        required: true
        schema:
          type: string
      - name: roleId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminRoleBo"
    post:
      summary: 为管理员角色添加权限（通过权限代码）
      tags:
      - 管理员角色管理
      parameters:
      - name: permissionCode
        in: path
        required: true
        schema:
          type: string
      - name: roleId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminRoleBo"
  /admin/roles/{roleId}/permissions/{permissionId}:
    delete:
      summary: 从管理员角色中移除权限（通过权限ID）
      tags:
      - 管理员角色管理
      parameters:
      - name: permissionId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      - name: roleId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminRoleBo"
    post:
      summary: 为管理员角色添加权限（通过权限ID）
      tags:
      - 管理员角色管理
      parameters:
      - name: permissionId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      - name: roleId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminRoleBo"
  /admin/session:
    get:
      summary: 获取当前登录用户
      tags:
      - 管理员安全接口
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SessionInfoLongAdminUserBo"
  /admin/static-lines/batch/toggle-enabled:
    put:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BatchToggleEnabledBO"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BatchOperationResultDTO"
        "400":
          description: Bad Request
      summary: Batch Toggle Enabled
      tags:
      - Static Line Admin Controller
  /admin/static-lines/page:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PageQuery"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageResultStaticLineDTO"
        "400":
          description: Bad Request
      summary: List
      tags:
      - Static Line Admin Controller
  /admin/static-lines/{id}:
    put:
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/StaticLineBO"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StaticLineDTO"
        "400":
          description: Bad Request
      summary: Update
      tags:
      - Static Line Admin Controller
    get:
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StaticLineDTO"
      summary: Get By Id
      tags:
      - Static Line Admin Controller
  /admin/static-lines/{id}/health:
    get:
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LineHealthDTO"
      summary: Get Line Health
      tags:
      - Static Line Admin Controller
  /admin/static/business-types:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/StaticBusinessTypeBO"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StaticBusinessTypeDTO"
        "400":
          description: Bad Request
      summary: Create
      tags:
      - Static Business Type Admin Controller
  /admin/static/business-types/page:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PageQuery"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageResultStaticBusinessTypeDTO"
        "400":
          description: Bad Request
      summary: List
      tags:
      - Static Business Type Admin Controller
  /admin/static/business-types/{id}:
    put:
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/StaticBusinessTypeBO"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StaticBusinessTypeDTO"
        "400":
          description: Bad Request
      summary: Update
      tags:
      - Static Business Type Admin Controller
    get:
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StaticBusinessTypeDTO"
      summary: Get By Id
      tags:
      - Static Business Type Admin Controller
    delete:
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "204":
          description: No Content
      summary: Delete
      tags:
      - Static Business Type Admin Controller
  /admin/users:
    post:
      summary: 创建新的管理员用户
      tags:
      - Admin User Management
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAdminUserRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminUserBo"
        "400":
          description: Bad Request
  /admin/users/page:
    post:
      summary: 分页获取管理员用户
      tags:
      - Admin User Management
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PageQuery"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageResultAdminUserBo"
        "400":
          description: Bad Request
  /admin/users/{id}:
    put:
      summary: 更新管理员用户
      tags:
      - Admin User Management
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateAdminUserRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminUserBo"
        "400":
          description: Bad Request
    get:
      summary: 根据ID获取管理员用户
      tags:
      - Admin User Management
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminUserBo"
  /admin/users/{id}/active:
    patch:
      summary: 设置管理员用户活动状态
      tags:
      - Admin User Management
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      - name: active
        in: query
        schema:
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminUserBo"
  /admin/users/{id}/password:
    put:
      summary: 更新管理员用户密码
      tags:
      - Admin User Management
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdatePasswordRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminUserBo"
        "400":
          description: Bad Request
  /admin/users/{id}/roles:
    post:
      summary: 为管理员用户分配角色（通过角色ID）
      tags:
      - Admin User Management
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              type: array
              uniqueItems: true
              items:
                type: integer
                format: int64
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminUserBo"
        "400":
          description: Bad Request
  /admin/users/{id}/roles/codes:
    post:
      summary: 为管理员用户分配角色（通过角色代码）
      tags:
      - Admin User Management
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              type: array
              uniqueItems: true
              items:
                type: string
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminUserBo"
        "400":
          description: Bad Request
  /admin/users/{userId}/roles/code/{roleCode}:
    delete:
      summary: 从管理员用户中移除角色（通过角色代码）
      tags:
      - Admin User Management
      parameters:
      - name: roleCode
        in: path
        required: true
        schema:
          type: string
      - name: userId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminUserBo"
    post:
      summary: 为管理员用户添加角色（通过角色代码）
      tags:
      - Admin User Management
      parameters:
      - name: roleCode
        in: path
        required: true
        schema:
          type: string
      - name: userId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminUserBo"
  /admin/users/{userId}/roles/{roleId}:
    delete:
      summary: 从管理员用户中移除角色（通过角色ID）
      tags:
      - Admin User Management
      parameters:
      - name: roleId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      - name: userId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminUserBo"
    post:
      summary: 为管理员用户添加角色（通过角色ID）
      tags:
      - Admin User Management
      parameters:
      - name: roleId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      - name: userId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminUserBo"
  /health:
    get:
      responses:
        "200":
          description: OK
          content:
            text/plain:
              schema:
                type: string
      summary: Health
      tags:
      - Health Api
  /openapi/v1/account/balance:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApiV1Request"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
        "400":
          description: Bad Request
      summary: Get Balance
      tags:
      - Get Balance Controller
  /openapi/v1/bizenv/search:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApiV1Request"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
        "400":
          description: Bad Request
      summary: Search
      tags:
      - Get Bizenv Controller
  /openapi/v1/city/search:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApiV1Request"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
        "400":
          description: Bad Request
      summary: Search
      tags:
      - Get City Controller
  /openapi/v1/staticresidentialline/availablecities:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApiV1Request"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
        "400":
          description: Bad Request
      summary: Available Cities
      tags:
      - Static Residential Line Available Cities Controller
  /openapi/v1/staticresidentialline/order/create:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApiV1Request"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
        "400":
          description: Bad Request
      summary: Create
      tags:
      - Static Residential Line Order Create Controller
  /openapi/v1/staticresidentialline/order/proxy/renewal:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApiV1Request"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
        "400":
          description: Bad Request
      summary: Renewal
      tags:
      - Static Residential Line Order Proxy Renewal Controller
  /openapi/v1/staticresidentialline/order/proxy/replacementIP:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApiV1Request"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
        "400":
          description: Bad Request
      summary: Replacement IP
      tags:
      - Static Residential Line Order Proxy Replacement IP Controller
  /openapi/v1/staticresidentialline/order/proxy/search:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApiV1Request"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
        "400":
          description: Bad Request
      summary: Search
      tags:
      - Static Residential Line Order Proxy Search Controller
  /openapi/v1/staticresidentialline/order/search:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApiV1Request"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
        "400":
          description: Bad Request
      summary: Search
      tags:
      - Static Residential Line Order Search Controller
  /openapi/v1/staticresidentialline/search:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApiV1Request"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
        "400":
          description: Bad Request
      summary: Search
      tags:
      - Static Residential Line Search Controller
  /openapi/v1/verify:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApiV1Request"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
        "400":
          description: Bad Request
      summary: Verify Sign
      tags:
      - Verify Sign Controller
components:
  schemas:
    AdminPermissionBo:
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        code:
          type: string
        description:
          type: string
        isActive:
          type: boolean
        createdAt:
          $ref: "#/components/schemas/LocalDateTime"
        updatedAt:
          $ref: "#/components/schemas/LocalDateTime"
    AdminRoleBo:
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        code:
          type: string
        description:
          type: string
        home:
          type: string
        isActive:
          type: boolean
        permissions:
          type: array
          uniqueItems: true
          items:
            $ref: "#/components/schemas/AdminPermissionBo"
        createdAt:
          $ref: "#/components/schemas/LocalDateTime"
        updatedAt:
          $ref: "#/components/schemas/LocalDateTime"
    AdminUserBo:
      type: object
      properties:
        id:
          type: integer
          format: int64
        username:
          type: string
        realname:
          type: string
        email:
          type: string
        phone:
          type: string
        password:
          type: string
        avatar:
          type: string
        nickname:
          type: string
        lastLoginAt:
          $ref: "#/components/schemas/LocalDateTime"
        lastLoginIp:
          type: string
        roles:
          type: array
          items:
            type: string
        permissions:
          type: array
          items:
            type: string
        isActive:
          type: boolean
        createdAt:
          $ref: "#/components/schemas/LocalDateTime"
        updatedAt:
          $ref: "#/components/schemas/LocalDateTime"
    ApiV1Request:
      type: object
      properties:
        version:
          type: string
        appId:
          type: string
        nonce:
          type: string
        signature:
          type: string
        timestamp:
          type: string
        data:
          type: string
    BatchAssignBusinessTypeBO:
      type: object
      properties:
        ids:
          type: array
          items:
            type: integer
            format: int64
        businessTypeId:
          type: integer
          format: int64
    BatchAssignLocationBO:
      type: object
      properties:
        ids:
          type: array
          items:
            type: integer
            format: int64
        locationId:
          type: integer
          format: int64
    BatchOperationResultDTO:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        updatedCount:
          type: integer
          format: int32
    BatchToggleEnabledBO:
      type: object
      properties:
        ids:
          type: array
          items:
            type: integer
            format: int64
        enabled:
          type: boolean
    BatchUnassignBO:
      type: object
      properties:
        ids:
          type: array
          items:
            type: integer
            format: int64
    ChannelBusinessTypeMappingDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        channelType:
          $ref: "#/components/schemas/ChannelType"
        externalCode:
          type: string
        businessTypeId:
          type: integer
          format: int64
        createdAt:
          $ref: "#/components/schemas/LocalDateTime"
        updatedAt:
          $ref: "#/components/schemas/LocalDateTime"
        businessType:
          $ref: "#/components/schemas/StaticBusinessTypeDTO"
    ChannelLocationMappingDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        channelType:
          $ref: "#/components/schemas/ChannelType"
        externalCode:
          type: string
        locationId:
          type: integer
          format: int64
        externalData:
          type: string
        createdAt:
          $ref: "#/components/schemas/LocalDateTime"
        updatedAt:
          $ref: "#/components/schemas/LocalDateTime"
        location:
          $ref: "#/components/schemas/LocationDTO"
    ChannelType:
      type: string
      enum:
      - SELF
      - IPNUX
    CreateAdminPermissionRequest:
      type: object
      required:
      - name
      - code
      properties:
        name:
          type: string
          maxLength: 100
          pattern: \S
        code:
          type: string
          maxLength: 50
          pattern: \S
        description:
          type: string
    CreateAdminRoleRequest:
      type: object
      required:
      - name
      - code
      properties:
        name:
          type: string
          maxLength: 100
          pattern: \S
        code:
          type: string
          maxLength: 50
          pattern: \S
        description:
          type: string
        home:
          type: string
        permissionIds:
          type: array
          uniqueItems: true
          items:
            type: integer
            format: int64
        permissionCodes:
          type: array
          uniqueItems: true
          items:
            type: string
    CreateAdminUserRequest:
      type: object
      required:
      - username
      - password
      properties:
        username:
          type: string
          minLength: 3
          maxLength: 50
          pattern: \S
        password:
          type: string
          minLength: 6
          pattern: \S
        email:
          type: string
        phone:
          type: string
        nickname:
          type: string
        roleIds:
          type: array
          uniqueItems: true
          items:
            type: integer
            format: int64
        roleCodes:
          type: array
          uniqueItems: true
          items:
            type: string
    IsEnum:
      type: string
      enum:
      - "FALSE"
      - "TRUE"
    IspTypeEnum:
      type: string
      enum:
      - NONE
      - SHARED
      - PREMIUM
    LineHealthDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        usageRate:
          type: number
          format: double
        healthStatus:
          type: string
        enabled:
          type: boolean
        status:
          $ref: "#/components/schemas/StaticProxyLineStatusEnum"
        totalQuantity:
          type: integer
          format: int32
        usedQuantity:
          type: integer
          format: int32
        reservedQuantity:
          type: integer
          format: int32
        availableQuantity:
          type: integer
          format: int32
    LocalDateTime:
      type: string
      format: date-time
      examples:
      - 2022-03-10T12:15:50
    LocationBO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        parentId:
          type: integer
          format: int64
        code:
          type: string
        name:
          type: string
        level:
          $ref: "#/components/schemas/LocationLevelEnum"
        iso2Code:
          type: string
        iso3Code:
          type: string
        numericCode:
          type: string
        sortOrder:
          type: integer
          format: int32
    LocationDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        parentId:
          type: integer
          format: int64
        code:
          type: string
        name:
          type: string
        level:
          $ref: "#/components/schemas/LocationLevelEnum"
        sortOrder:
          type: integer
          format: int32
    LocationLevelEnum:
      type: string
      enum:
      - CONTINENT
      - COUNTRY
      - STATE_PROVINCE
      - CITY
    PageQuery:
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: "#/components/schemas/QueryFilter"
        sorts:
          type: array
          items:
            $ref: "#/components/schemas/QuerySort"
        distinct:
          type: boolean
        distinctField:
          type: string
        current:
          type: integer
          format: int32
        size:
          type: integer
          format: int32
        offset:
          type: integer
          format: int32
    PageResultAdminPermissionBo:
      type: object
      properties:
        size:
          type: integer
          format: int32
        current:
          type: integer
          format: int32
        total:
          type: integer
          format: int32
        records:
          type: array
          items:
            $ref: "#/components/schemas/AdminPermissionBo"
        offset:
          type: integer
          format: int32
    PageResultAdminRoleBo:
      type: object
      properties:
        size:
          type: integer
          format: int32
        current:
          type: integer
          format: int32
        total:
          type: integer
          format: int32
        records:
          type: array
          items:
            $ref: "#/components/schemas/AdminRoleBo"
        offset:
          type: integer
          format: int32
    PageResultAdminUserBo:
      type: object
      properties:
        size:
          type: integer
          format: int32
        current:
          type: integer
          format: int32
        total:
          type: integer
          format: int32
        records:
          type: array
          items:
            $ref: "#/components/schemas/AdminUserBo"
        offset:
          type: integer
          format: int32
    PageResultChannelBusinessTypeMappingDTO:
      type: object
      properties:
        size:
          type: integer
          format: int32
        current:
          type: integer
          format: int32
        total:
          type: integer
          format: int32
        records:
          type: array
          items:
            $ref: "#/components/schemas/ChannelBusinessTypeMappingDTO"
        offset:
          type: integer
          format: int32
    PageResultChannelLocationMappingDTO:
      type: object
      properties:
        size:
          type: integer
          format: int32
        current:
          type: integer
          format: int32
        total:
          type: integer
          format: int32
        records:
          type: array
          items:
            $ref: "#/components/schemas/ChannelLocationMappingDTO"
        offset:
          type: integer
          format: int32
    PageResultLocationDTO:
      type: object
      properties:
        size:
          type: integer
          format: int32
        current:
          type: integer
          format: int32
        total:
          type: integer
          format: int32
        records:
          type: array
          items:
            $ref: "#/components/schemas/LocationDTO"
        offset:
          type: integer
          format: int32
    PageResultStaticBusinessTypeDTO:
      type: object
      properties:
        size:
          type: integer
          format: int32
        current:
          type: integer
          format: int32
        total:
          type: integer
          format: int32
        records:
          type: array
          items:
            $ref: "#/components/schemas/StaticBusinessTypeDTO"
        offset:
          type: integer
          format: int32
    PageResultStaticLineDTO:
      type: object
      properties:
        size:
          type: integer
          format: int32
        current:
          type: integer
          format: int32
        total:
          type: integer
          format: int32
        records:
          type: array
          items:
            $ref: "#/components/schemas/StaticLineDTO"
        offset:
          type: integer
          format: int32
    QueryFilter:
      type: object
      properties:
        field:
          type: string
        operator:
          type: string
        value: {}
        children:
          type: array
          items:
            $ref: "#/components/schemas/QueryFilter"
    QuerySort:
      type: object
      properties:
        field:
          type: string
        direction:
          type: string
    SessionInfoLongAdminUserBo:
      type: object
      properties:
        name:
          type: string
        token:
          type: string
        expiredAt:
          $ref: "#/components/schemas/LocalDateTime"
        user:
          $ref: "#/components/schemas/AdminUserBo"
        order:
          type: integer
          format: int64
        kicked:
          type: boolean
        permissions:
          type: array
          items:
            type: string
        roles:
          type: array
          items:
            type: string
        userId:
          type: integer
          format: int64
    StaticBusinessTypeBO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        code:
          type: string
        name:
          type: string
        description:
          type: string
        isEnabled:
          $ref: "#/components/schemas/IsEnum"
        config:
          type: string
        isPopular:
          $ref: "#/components/schemas/IsEnum"
    StaticBusinessTypeDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        code:
          type: string
        name:
          type: string
        description:
          type: string
        isEnabled:
          $ref: "#/components/schemas/IsEnum"
        config:
          type: string
        isPopular:
          $ref: "#/components/schemas/IsEnum"
        createdAt:
          $ref: "#/components/schemas/LocalDateTime"
        updatedAt:
          $ref: "#/components/schemas/LocalDateTime"
    StaticLineBO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        channelId:
          type: integer
          format: int64
        locationId:
          type: integer
          format: int64
        businessTypeId:
          type: integer
          format: int64
        ispType:
          $ref: "#/components/schemas/IspTypeEnum"
        amount:
          type: number
        name:
          type: string
        description:
          type: string
        enabled:
          type: boolean
        quantity:
          type: integer
          format: int32
        usedQuantity:
          type: integer
          format: int32
        reservedQuantity:
          type: integer
          format: int32
        protocols:
          type: integer
          format: int32
        status:
          $ref: "#/components/schemas/StaticProxyLineStatusEnum"
        priority:
          type: integer
          format: int32
        qualityScore:
          type: integer
          format: int32
        successRate:
          type: integer
          format: int32
        avgResponseTime:
          type: integer
          format: int32
        maxConcurrentConnections:
          type: integer
          format: int32
        config:
          type: string
        remarks:
          type: string
    StaticLineDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        channelId:
          type: integer
          format: int64
        locationId:
          type: integer
          format: int64
        businessTypeId:
          type: integer
          format: int64
        ispType:
          $ref: "#/components/schemas/IspTypeEnum"
        amount:
          type: number
        name:
          type: string
        description:
          type: string
        enabled:
          type: boolean
        quantity:
          type: integer
          format: int32
        usedQuantity:
          type: integer
          format: int32
        reservedQuantity:
          type: integer
          format: int32
        protocols:
          type: integer
          format: int32
        status:
          $ref: "#/components/schemas/StaticProxyLineStatusEnum"
        priority:
          type: integer
          format: int32
        qualityScore:
          type: integer
          format: int32
        successRate:
          type: integer
          format: int32
        avgResponseTime:
          type: integer
          format: int32
        maxConcurrentConnections:
          type: integer
          format: int32
        config:
          type: string
        remarks:
          type: string
        createdAt:
          $ref: "#/components/schemas/LocalDateTime"
        updatedAt:
          $ref: "#/components/schemas/LocalDateTime"
    StaticProxyLineStatusEnum:
      type: string
      enum:
      - ACTIVE
      - INACTIVE
      - MAINTENANCE
    UpdateAdminUserRequest:
      type: object
      properties:
        email:
          type: string
        phone:
          type: string
        nickname:
          type: string
        avatar:
          type: string
        password:
          type: string
          minLength: 6
    UpdatePasswordRequest:
      type: object
      required:
      - oldPassword
      - newPassword
      - confirmPassword
      properties:
        oldPassword:
          type: string
          pattern: \S
        newPassword:
          type: string
          minLength: 6
          maxLength: 100
          pattern: \S
        confirmPassword:
          type: string
          pattern: \S
    UsernameLoginRequest:
      type: object
      required:
      - username
      - password
      properties:
        username:
          type: string
          pattern: \S
        password:
          type: string
          pattern: \S
tags:
- name: Admin User Management
- name: 管理员安全接口
- name: 管理员权限管理
- name: 管理员角色管理
info:
  title: proxy-admin API
  version: 1.0-SNAPSHOT
