import { apiCreateTransaction, apiGetTransactions, apiUpdateTransaction, apiDeleteTransaction } from '@/apis/gen.apis';
import { useFormModal } from '@/components/FormilyModal';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { App, Button, Space, Popconfirm, Tag } from 'antd';
import { useRef } from 'react';

type TEditFormData = {
  transactionNo: string;
  externalTransactionNo: string;
  userId: string;
  transactionType: string;
  status: string;
  amount: number;
  currency: string;
  balanceBefore: number;
  balanceAfter: number;
  transactionTime: string;
  paymentMethod: string;
  paymentChannel: string;
  description: string;
  fee: number;
  feeCurrency: string;
  needsApproval: boolean;
  isTest: boolean;
  remarks: string;
};

interface ClientTransactionListComponentProps {
  userId?: string; // 可选的用户ID，用于过滤该用户的交易
  showUserIdColumn?: boolean; // 是否显示用户ID列
  headerTitle?: string; // 自定义标题
  showAddButton?: boolean; // 是否显示新增按钮
}

function ClientTransactionListComponent({ 
  userId, 
  showUserIdColumn = true, 
  headerTitle = '客户交易列表',
  showAddButton = true 
}: ClientTransactionListComponentProps) {
  const tableRef = useRef<ActionType>();
  const { message } = App.useApp();

  const { formModalRef: editFormModalRef, formModalHolder: editFormModalHolder } =
    useFormModal<TEditFormData>();

  const showEditModal = useMemoizedFn((record?: TClientUserTransactionBO) => {
    editFormModalRef.current?.show({
      modalTitle: record ? '修改交易' : '新增交易',
      modalWidth: 1200,
      onAutoSubmit: async (values) => {
        // 如果传入了userId，自动设置到表单数据中
        const formData = userId ? { ...values, userId } : values;
        
        if (record?.id) {
          await apiUpdateTransaction(record.id, formData);
        } else {
          await apiCreateTransaction(formData);
        }

        message.success('提交成功');
        tableRef.current?.reload();
      },
      initialValues: record
        ? {
            ...record,
            transactionTime: record.transactionTime ? new Date(record.transactionTime).toISOString().slice(0, 16) : '',
          }
        : userId ? { userId } : {},
      schema: {
        type: 'object',
        properties: {
          userId: {
            title: '用户ID',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-disabled': !!userId, // 如果传入了userId，则禁用编辑
          },
          transactionNo: {
            title: '交易号',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          transactionType: {
            title: '交易类型',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              options: [
                { label: '充值', value: 'DEPOSIT' },
                { label: '提现', value: 'WITHDRAW' },
                { label: '转账', value: 'TRANSFER' },
                { label: '消费', value: 'CONSUME' },
              ],
            },
          },
          status: {
            title: '状态',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              options: [
                { label: '待处理', value: 'PENDING' },
                { label: '处理中', value: 'PROCESSING' },
                { label: '成功', value: 'SUCCESS' },
                { label: '失败', value: 'FAILED' },
                { label: '已取消', value: 'CANCELLED' },
              ],
            },
          },
          amount: {
            title: '金额',
            type: 'number',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              precision: 2,
              min: 0,
            },
          },
          currency: {
            title: '货币',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          description: {
            title: '描述',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
          },
          remarks: {
            title: '备注',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
          },
        },
      },
    });
  });

  const handleDelete = useMemoizedFn(async (id: string) => {
    await apiDeleteTransaction(id);
    message.success('删除成功');
    tableRef.current?.reload();
  });

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      PENDING: 'orange',
      PROCESSING: 'blue',
      SUCCESS: 'green',
      FAILED: 'red',
      CANCELLED: 'gray',
    };
    return colorMap[status] || 'default';
  };

  // 构建列配置
  const columns = [
    { title: '交易号', dataIndex: 'transactionNo', width: 200 },
    { title: '外部交易号', dataIndex: 'externalTransactionNo', width: 150 },
    ...(showUserIdColumn ? [{ title: '用户ID', dataIndex: 'userId', width: 120 }] : []),
    { title: '交易类型', dataIndex: 'transactionType', width: 100 },
    { 
      title: '状态', 
      dataIndex: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    { 
      title: '金额', 
      dataIndex: 'amount',
      width: 120,
      render: (amount: number, record: TClientUserTransactionBO) => `${amount} ${record.currency}`,
    },
    { 
      title: '交易时间', 
      dataIndex: 'transactionTime',
      valueType: 'dateTime' as const,
      width: 180,
    },
    { title: '支付方式', dataIndex: 'paymentMethod', width: 120 },
    { title: '描述', dataIndex: 'description', ellipsis: true, width: 200 },
    { 
      title: '手续费', 
      dataIndex: 'fee',
      width: 100,
      render: (fee: number, record: TClientUserTransactionBO) => fee ? `${fee} ${record.feeCurrency}` : '-',
    },
    {
      title: '操作',
      valueType: 'option' as const,
      key: 'option',
      width: 150,
      render: (_text: any, record: TClientUserTransactionBO) => {
        return (
          <Space>
            <a onClick={() => showEditModal(record)}>修改</a>
            <Popconfirm
              title="确定要删除这个交易吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <a style={{ color: 'red' }}>删除</a>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <ProTable<TClientUserTransactionBO>
        actionRef={tableRef}
        headerTitle={headerTitle}
        rowKey='id'
        toolBarRender={() => showAddButton ? [
          <Button key='add' type='primary' onClick={() => showEditModal()}>
            新增
          </Button>,
        ] : []}
        search={{
          defaultCollapsed: false,
        }}
        scroll={{ x: 1200 }}
        columns={columns}
        request={async (params, sorter) => {
          const filters: any[] = [];

          // 如果传入了userId，自动添加用户过滤条件
          if (userId) {
            filters.push({ field: 'userId', operator: 'eq', value: userId });
          }

          if (params.transactionNo) {
            filters.push({ field: 'transactionNo', operator: 'like', value: params.transactionNo });
          }
          if (params.userId && !userId) { // 只有在没有预设userId时才允许搜索
            filters.push({ field: 'userId', operator: 'eq', value: params.userId });
          }
          if (params.status) {
            filters.push({ field: 'status', operator: 'eq', value: params.status });
          }
          if (params.transactionType) {
            filters.push({ field: 'transactionType', operator: 'eq', value: params.transactionType });
          }

          const res = await apiGetTransactions({
            current: params.current - 1,
            size: params.pageSize,
            filters,
            sorts: [],
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}
      />
      {editFormModalHolder}
    </div>
  );
}

export default ClientTransactionListComponent;
