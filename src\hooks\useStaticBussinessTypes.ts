import { useRequest } from "ahooks";
import { apiListStaticBusinessTypes } from "@/apis/gen.apis";

export default function useStaticBusinessTypes() {
  const { data: staticBusinessTypes = [] } = useRequest(async () => {
    return (await apiListStaticBusinessTypes({
      current: 0,
      size: 10000,
    })).records;
  });

  return {
    staticBusinessTypes,
    staticBusinessTypeOptions: staticBusinessTypes.map((item) => ({ label: item.name, value: item.id })),
    staticBusinessTypeEnum: staticBusinessTypes.reduce((prev, cur) => ({ ...prev, [cur.id]: cur.name }), {}),
  };
}
