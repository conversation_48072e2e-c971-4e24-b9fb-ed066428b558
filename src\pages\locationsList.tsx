import { apiCreateLocation, apiListLocations, apiUpdateLocation, apiDeleteLocation } from '@/apis/gen.apis';
import { useFormModal } from '@/components/FormilyModal';
import I18nText from '@/components/I18nText';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { App, Button, Space, Popconfirm } from 'antd';
import { useRef } from 'react';

type TEditFormData = {
  id?: number;
  parentId?: number;
  code: string;
  name: string;
  level: string;
  iso2Code?: string;
  iso3Code?: string;
  numericCode?: string;
  sortOrder?: number;
};

function LocationsList() {
  const tableRef = useRef<ActionType>();
  const { message } = App.useApp();

  const { formModalRef: editFormModalRef, formModalHolder: editFormModalHolder } =
    useFormModal<TEditFormData>();

  const showEditModal = useMemoizedFn((record?: TLocationDTO) => {
    editFormModalRef.current?.show({
      modalTitle: record ? '修改地理位置' : '新增地理位置',
      modalWidth: 800,
      onAutoSubmit: async (values: any) => {
        if (record?.id) {
          await apiUpdateLocation(record.id.toString(), values);
        } else {
          await apiCreateLocation(values);
        }

        message.success('提交成功');
        tableRef.current?.reload();
      },
      initialValues: record
        ? {
            ...record,
          }
        : {
            level: '1',
            sortOrder: 0,
          },
      schema: {
        type: 'object',
        properties: {
          name: {
            title: '名称',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          code: {
            title: '编码',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          level: {
            title: '级别',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            enum: [
              { label: '大洲', value: '0' },
              { label: '国家', value: '1' },
              { label: '州/省', value: '2' },
              { label: '城市', value: '3' },
            ],
          },
          parentId: {
            title: '父级ID',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
          },
          iso2Code: {
            title: 'ISO2代码',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          iso3Code: {
            title: 'ISO3代码',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          numericCode: {
            title: '数字代码',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          sortOrder: {
            title: '排序',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
          },
        },
      },
    });
  });

  const handleDelete = useMemoizedFn(async (id: number) => {
    await apiDeleteLocation(id.toString());
    message.success('删除成功');
    tableRef.current?.reload();
  });

  return (
    <div>
      <ProTable<TLocationDTO>
        actionRef={tableRef}
        headerTitle='地理位置列表'
        rowKey='id'
        // toolBarRender={() => [
        //   <Button key='add' type='primary' onClick={() => showEditModal()}>
        //     新增
        //   </Button>,
        // ]}
        search={{
          defaultCollapsed: false,
        }}
        columns={[
          { title: 'ID', dataIndex: 'id', width: 80 },
          { title: '名称', dataIndex: 'name', render: (_, record) => <I18nText value={record.name} /> },
          { title: '编码', dataIndex: 'code' },
          { 
            title: '级别', 
            dataIndex: 'level',
            valueEnum: {
              0: '大洲',
              1: '国家',
              2: '州/省',
              3: '城市',
            },
          },
          { title: '父级ID', dataIndex: 'parentId', width: 100, search: false },
          { title: '排序', dataIndex: 'sortOrder', width: 80, search: false },
          // {
          //   title: '操作',
          //   valueType: 'option',
          //   key: 'option',
          //   render: (_text, record) => {
          //     return (
          //       <Space>
          //         <a onClick={() => showEditModal(record)}>修改</a>
          //         <Popconfirm
          //           title="确定要删除这个地理位置吗？"
          //           onConfirm={() => handleDelete(record.id)}
          //           okText="确定"
          //           cancelText="取消"
          //         >
          //           <a style={{ color: 'red' }}>删除</a>
          //         </Popconfirm>
          //       </Space>
          //     );
          //   },
          // },
        ]}
        request={async (params) => {
          const filters: any[] = [];

          if (params.name) {
            filters.push({ field: 'name', operator: 'like', value: params.name });
          }
          if (params.code) {
            filters.push({ field: 'code', operator: 'like', value: params.code });
          }
          if (params.level) {
            filters.push({ field: 'level', operator: 'eq', value: params.level });
          }

          const res = await apiListLocations({
            current: (params.current || 1) - 1,
            size: params.pageSize || 20,
            filters,
            sorts: [],
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}
      />
      {editFormModalHolder}
    </div>
  );
}

export default LocationsList;

LocationsList.auth = ['apiCreateLocation', 'apiListLocations', 'apiUpdateLocation', 'apiDeleteLocation'];
