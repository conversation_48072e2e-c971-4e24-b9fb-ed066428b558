import React from 'react';
import { Form, Input, Select, InputNumber, Modal } from 'antd';

interface RuleFormModalProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (values: any) => void;
  initialValues: TExchangeRateRuleBo | null;
}

const RuleFormModal: React.FC<RuleFormModalProps> = ({
  visible,
  onCancel,
  onSubmit,
  initialValues
}) => {
  const [form] = Form.useForm();

  // Reset form when modal opens/closes
  React.useEffect(() => {
    if (visible) {
      form.resetFields();
      if (initialValues) {
        form.setFieldsValue({
          name: initialValues.name,
          description: initialValues.description,
          ruleType: initialValues.ruleType,
          value: initialValues.value,
        });
      }
    }
  }, [visible, initialValues, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSubmit(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal
      title={initialValues ? '编辑汇率规则' : '创建汇率规则'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      width={600}
    >
      <Form 
        form={form} 
        layout="vertical"
      >
        <Form.Item
          name="name"
          label="规则名称"
          rules={[{ required: true, message: '请输入规则名称' }]}
        >
          <Input placeholder="例如: 促销折扣" />
        </Form.Item>

        <Form.Item
          name="description"
          label="规则描述"
        >
          <Input.TextArea rows={2} placeholder="规则的详细描述" />
        </Form.Item>

        <Form.Item
          name="ruleType"
          label="规则类型"
          rules={[{ required: true, message: '请选择规则类型' }]}
        >
          <Select placeholder="选择规则类型">
            <Select.Option value="FIXED_RATE">固定汇率</Select.Option>
            <Select.Option value="PERCENTAGE_MARKUP">百分比加价</Select.Option>
            <Select.Option value="PERCENTAGE_DISCOUNT">百分比折扣</Select.Option>
            <Select.Option value="FIXED_MARKUP">固定加价</Select.Option>
            <Select.Option value="FIXED_DISCOUNT">固定折扣</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="value"
          label="规则值"
          rules={[{ required: true, message: '请输入规则值' }]}
        >
          <InputNumber 
            style={{ width: '100%' }} 
            placeholder="根据规则类型输入相应的值"
            precision={6}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default RuleFormModal;

RuleFormModal.auth = [];
