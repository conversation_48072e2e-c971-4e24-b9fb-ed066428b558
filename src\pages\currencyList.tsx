import React, { useRef } from 'react';
import { Button, Form, Input, Modal, Select, Space, message } from 'antd';
import { PlusOutlined, CheckCircleOutlined, SwapOutlined } from '@ant-design/icons';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { 
  apiCurrencyPageList, 
  apiCurrencyCreate, 
  apiCurrencyUpdate, 
  apiCurrencySetDefault, 
  apiCurrencySetActive 
} from '@/apis/apis.api';
import { ToolsUtil } from '@/utils/tools';
import { useNavigate } from 'react-router-dom';

// Currency List Component
const CurrencyList = () => {
  const [form] = Form.useForm();
  const tableRef = useRef<ActionType>();
  const [editingCurrency, setEditingCurrency] = React.useState<TCurrency | null>(null);
  const [isModalVisible, setIsModalVisible] = React.useState(false);
  const navigate = useNavigate();

  // Handle form submission
  const handleSubmit = useMemoizedFn(async () => {
    try {
      const values = await form.validateFields();
      
      if (editingCurrency) {
        // Update existing currency
        await apiCurrencyUpdate(editingCurrency.code, values);
        message.success('货币更新成功');
      } else {
        // Create new currency
        await apiCurrencyCreate(values);
        message.success('货币创建成功');
      }
      
      setIsModalVisible(false);
      form.resetFields();
      tableRef.current?.reload();
    } catch (error) {
      console.error('表单提交错误:', error);
    }
  });

  // Handle setting a currency as default
  const handleSetDefault = useMemoizedFn(async (code: string) => {
    try {
      await apiCurrencySetDefault(code);
      message.success('默认货币设置成功');
      tableRef.current?.reload();
    } catch (error) {
      message.error('设置默认货币失败');
    }
  });

  // Handle activating/deactivating a currency
  const handleSetActive = useMemoizedFn(async (code: string, active: boolean) => {
    try {
      await apiCurrencySetActive(code, active);
      message.success(`货币${active ? '激活' : '停用'}成功`);
      tableRef.current?.reload();
    } catch (error) {
      message.error(`货币${active ? '激活' : '停用'}失败`);
    }
  });

  // Open modal for editing
  const handleEdit = useMemoizedFn((record: TCurrency) => {
    setEditingCurrency(record);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  });

  // Open modal for creating
  const handleCreate = useMemoizedFn(() => {
    setEditingCurrency(null);
    form.resetFields();
    setIsModalVisible(true);
  });

  // Navigate to exchange rate page for a specific currency
  const handleViewExchangeRates = useMemoizedFn((currency: TCurrency) => {
    navigate(`/exchange-rates/${currency.code}`);
  });

  // Table columns
  const columns = [
    {
      title: '货币代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '货币名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '货币符号',
      dataIndex: 'symbol',
      key: 'symbol',
    },
    {
      title: '默认',
      dataIndex: 'isDefault',
      key: 'isDefault',
      render: (isDefault: boolean, record: TCurrency) => (
        isDefault ? 
          <CheckCircleOutlined style={{ color: 'green' }} /> : 
          <Button 
            type="link" 
            onClick={() => handleSetDefault(record.code)}
          >
            设为默认
          </Button>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <span style={{ color: isActive ? 'green' : 'red' }}>
          {isActive ? '已启用' : '已停用'}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      valueType: 'option',
      render: (_: any, record: TCurrency) => (
        <Space>
          <a onClick={() => handleEdit(record)}>编辑</a>
          {record.isActive && (
            <a onClick={() => handleViewExchangeRates(record)}>
              <SwapOutlined /> 汇率管理
            </a>
          )}
          <a onClick={() => handleSetActive(record.code, !record.isActive)}>
            {record.isActive ? '停用' : '启用'}
          </a>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<TCurrency>
        actionRef={tableRef}
        headerTitle="货币管理"
        rowKey="code"
        columns={columns}
        search={{
          defaultCollapsed: false,
        }}
        toolBarRender={() => [
          <Button 
            key="add"
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={handleCreate}
          >
            添加货币
          </Button>,
        ]}
        request={async (params, sorter) => {
          const filters: TFilterItem<TCurrency>[] = [];
          
          // 添加搜索条件
          if (params.code) {
            filters.push({ field: 'code', operator: 'like', value: params.code });
          }
          if (params.name) {
            filters.push({ field: 'name', operator: 'like', value: params.name });
          }

          let sorts = ToolsUtil.tableSorterToPageListReqSort(sorter);
          
          // 添加默认排序，激活状态优先
          if (!sorts || sorts.length === 0) {
            sorts = [{ field: 'isActive', direction: 'DESC' }];
          }

          const res = await apiCurrencyPageList({
            current: params.current - 1,
            size: params.pageSize,
            filters,
            sorts,
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}
      />

      {/* Currency Modal */}
      <Modal
        title={editingCurrency ? '编辑货币' : '添加货币'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => setIsModalVisible(false)}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ isActive: true }}
        >
          <Form.Item
            name="code"
            label="货币代码"
            rules={[{ required: true, message: '请输入货币代码' }]}
            disabled={!!editingCurrency}
          >
            <Input placeholder="例如: CNY, USD" disabled={!!editingCurrency} />
          </Form.Item>
          <Form.Item
            name="name"
            label="货币名称"
            rules={[{ required: true, message: '请输入货币名称' }]}
          >
            <Input placeholder="例如: 人民币, 美元" />
          </Form.Item>
          <Form.Item
            name="symbol"
            label="货币符号"
            rules={[{ required: true, message: '请输入货币符号' }]}
          >
            <Input placeholder="例如: ¥, $" />
          </Form.Item>
          <Form.Item
            name="isActive"
            label="状态"
            valuePropName="checked"
          >
            <Select>
              <Select.Option value={true}>激活</Select.Option>
              <Select.Option value={false}>停用</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

// Add auth property to the component
export default CurrencyList;

CurrencyList.auth = ['apiCurrencyPageList', 'apiCurrencyCreate', 'apiCurrencyUpdate', 'apiCurrencySetDefault', 'apiCurrencySetActive'];
