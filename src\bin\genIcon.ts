/**
 * 增加组件
 * 用法：`pnpm geni //at.alicdn.com/t/c/font_4657722_xb2njsw9m1.js`
 * 将根据src路径生成Iconfont组件
 */
import { program } from 'commander';
import fs from 'fs';

program.name('pnpm gens').option('-u, --url <string>', '设置资源地址').helpOption('-h, --help', '打印帮助信息');

program.parse();

const { url } = program.opts();

if (!url) {
  throw new Error('请输入资源地址');
}

if (!url.startsWith('//at.alicdn.com')) {
  throw new Error('资源地址不合法');
}

fetch('https:' + url)
  .then((res) => res.text())
  .then((res) => {
    // id="icon-tricycle"
    const icons = res
      .match(/id="icon-.*?"/g)
      .map((item) => `'${item.slice(4, -1)}'`)
      .join(' | ');
    fs.writeFileSync(
      `src/components/IconFont/IconFont.tsx`,
      `import { createFromIconfontCN } from "@ant-design/icons";

export interface IconFontProps {
  type: ${icons};
}

const IconFont = createFromIconfontCN<IconFontProps['type']>({
  scriptUrl: '${url}',
});

export default IconFont
`
    );
  });
