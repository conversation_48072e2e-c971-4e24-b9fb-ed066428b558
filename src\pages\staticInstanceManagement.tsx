import { apiCreateInstance, apiGetInstances, apiUpdateInstance, apiActivateInstance, apiDeactivateInstance } from '@/apis/gen.apis';
import { useFormModal } from '@/components/FormilyModal';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { App, Button, Space, Popconfirm, Tag } from 'antd';
import { useRef } from 'react';

type TEditFormData = {
  lineId: string;
  proxyId: string;
  orderCode: string;
  userId: string;
  ip: string;
  port: number;
  username: string;
  password: string;
  expiresAt: string;
  createdAt: string;
  activatedAt: string;
  status: TStaticProxyInstanceStatusEnum;
  channelId: string;
  thirdProxyId: string;
  config: string;
  remarks: string;
  trafficLimit: number;
  concurrentLimit: number;
};

function StaticInstanceManagement() {
  const tableRef = useRef<ActionType>();
  const { message } = App.useApp();

  const { formModalRef: editFormModalRef, formModalHolder: editFormModalHolder } =
    useFormModal<TEditFormData>();

  const showEditModal = useMemoizedFn((record?: TStaticInstanceBO) => {
    editFormModalRef.current?.show({
      modalTitle: record ? '修改静态代理实例' : '新增静态代理实例',
      modalWidth: 1000,
      onAutoSubmit: async (values: any) => {
        if (record?.id) {
          await apiUpdateInstance(record.id, values);
        } else {
          await apiCreateInstance(values);
        }

        message.success('提交成功');
        tableRef.current?.reload();
      },
      initialValues: record
        ? {
            ...record,
            expiresAt: record.expiresAt ? new Date(record.expiresAt).toISOString().slice(0, 16) : '',
            createdAt: record.createdAt ? new Date(record.createdAt).toISOString().slice(0, 16) : '',
            activatedAt: record.activatedAt ? new Date(record.activatedAt).toISOString().slice(0, 16) : '',
          }
        : {
            port: 8080,
            trafficLimit: 0,
            concurrentLimit: 10,
          },
      schema: {
        type: 'object',
        properties: {
          lineId: {
            title: '线路ID',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          proxyId: {
            title: '代理ID',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          orderCode: {
            title: '订单编码',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          userId: {
            title: '用户ID',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          ip: {
            title: 'IP地址',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          port: {
            title: '端口',
            type: 'number',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              min: 1,
              max: 65535,
              style: { width: '100%' },
            },
          },
          username: {
            title: '用户名',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          password: {
            title: '密码',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input.Password',
          },
          status: {
            title: '状态',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              options: [
                { label: '待处理', value: 'PENDING' },
                { label: '激活', value: 'ACTIVE' },
                { label: '已过期', value: 'EXPIRED' },
                { label: '已取消', value: 'CANCELLED' },
              ],
            },
          },
          channelId: {
            title: '渠道ID',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          thirdProxyId: {
            title: '第三方代理ID',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          expiresAt: {
            title: '过期时间',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'DatePicker',
            'x-component-props': {
              showTime: true,
              format: 'YYYY-MM-DD HH:mm:ss',
            },
          },
          trafficLimit: {
            title: '流量限制(MB)',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              min: 0,
              style: { width: '100%' },
            },
          },
          concurrentLimit: {
            title: '并发限制',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              min: 1,
              style: { width: '100%' },
            },
          },
          config: {
            title: '配置',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 3,
            },
          },
          remarks: {
            title: '备注',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
          },
        },
      },
    });
  });

  const handleActivate = useMemoizedFn(async (id: string) => {
    await apiActivateInstance(id);
    message.success('激活成功');
    tableRef.current?.reload();
  });

  const handleDeactivate = useMemoizedFn(async (id: string) => {
    await apiDeactivateInstance(id);
    message.success('停用成功');
    tableRef.current?.reload();
  });

  const getStatusColor = (status: TStaticProxyInstanceStatusEnum) => {
    const colorMap: Record<TStaticProxyInstanceStatusEnum, string> = {
      PENDING: 'orange',
      ACTIVE: 'green',
      EXPIRED: 'red',
      CANCELLED: 'gray',
    };
    return colorMap[status] || 'default';
  };

  const getStatusText = (status: TStaticProxyInstanceStatusEnum) => {
    const textMap: Record<TStaticProxyInstanceStatusEnum, string> = {
      PENDING: '待处理',
      ACTIVE: '激活',
      EXPIRED: '已过期',
      CANCELLED: '已取消',
    };
    return textMap[status] || status;
  };

  return (
    <div>
      <ProTable<TStaticInstanceBO>
        actionRef={tableRef}
        headerTitle='静态代理实例管理'
        rowKey='id'
        toolBarRender={() => [
          <Button key='add' type='primary' onClick={() => showEditModal()}>
            新增
          </Button>,
        ]}
        search={{
          defaultCollapsed: false,
        }}
        scroll={{ x: 2000 }}
        columns={[
          { title: '代理ID', dataIndex: 'proxyId', width: 150, fixed: 'left' },
          { title: '订单编码', dataIndex: 'orderCode', width: 150 },
          { title: '用户ID', dataIndex: 'userId', width: 120 },
          { title: 'IP地址', dataIndex: 'ip', width: 150 },
          { title: '端口', dataIndex: 'port', width: 80 },
          { title: '用户名', dataIndex: 'username', width: 120 },
          { 
            title: '状态', 
            dataIndex: 'status',
            width: 100,
            render: (status: TStaticProxyInstanceStatusEnum) => (
              <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
            ),
          },
          { title: '线路ID', dataIndex: 'lineId', width: 120 },
          { title: '渠道ID', dataIndex: 'channelId', width: 120 },
          { title: '第三方代理ID', dataIndex: 'thirdProxyId', width: 150 },
          { 
            title: '使用次数', 
            dataIndex: 'usageCount',
            width: 100,
          },
          { 
            title: '流量使用(MB)', 
            dataIndex: 'trafficUsed',
            width: 120,
            render: (used: number) => used ? used.toFixed(2) : '0',
          },
          { 
            title: '流量限制(MB)', 
            dataIndex: 'trafficLimit',
            width: 120,
            render: (limit: number) => limit ? limit.toFixed(2) : '无限制',
          },
          { title: '并发限制', dataIndex: 'concurrentLimit', width: 100 },
          { 
            title: '过期时间', 
            dataIndex: 'expiresAt',
            valueType: 'dateTime',
            width: 180,
          },
          { 
            title: '激活时间', 
            dataIndex: 'activatedAt',
            valueType: 'dateTime',
            width: 180,
          },
          { 
            title: '最后使用时间', 
            dataIndex: 'lastUsedAt',
            valueType: 'dateTime',
            width: 180,
          },
          {
            title: '操作',
            valueType: 'option',
            key: 'option',
            width: 200,
            fixed: 'right',
            render: (_text, record) => {
              return (
                <Space>
                  <a onClick={() => showEditModal(record)}>修改</a>
                  {record.status === 'PENDING' && (
                    <a onClick={() => handleActivate(record.id)} style={{ color: 'green' }}>
                      激活
                    </a>
                  )}
                  {record.status === 'ACTIVE' && (
                    <Popconfirm
                      title="确定要停用这个实例吗？"
                      onConfirm={() => handleDeactivate(record.id)}
                      okText="确定"
                      cancelText="取消"
                    >
                      <a style={{ color: 'orange' }}>停用</a>
                    </Popconfirm>
                  )}
                </Space>
              );
            },
          },
        ]}
        request={async (params, sorter) => {
          const filters: any[] = [];

          if (params.proxyId) {
            filters.push({ field: 'proxyId', operator: 'like', value: params.proxyId });
          }
          if (params.userId) {
            filters.push({ field: 'userId', operator: 'eq', value: params.userId });
          }
          if (params.status) {
            filters.push({ field: 'status', operator: 'eq', value: params.status });
          }
          if (params.ip) {
            filters.push({ field: 'ip', operator: 'like', value: params.ip });
          }
          if (params.orderCode) {
            filters.push({ field: 'orderCode', operator: 'like', value: params.orderCode });
          }

          const res = await apiGetInstances({
            current: params.current - 1,
            size: params.pageSize,
            filters,
            sorts: [],
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}
      />
      {editFormModalHolder}
    </div>
  );
}

export default StaticInstanceManagement;

StaticInstanceManagement.auth = ['apiCreateInstance', 'apiGetInstances', 'apiUpdateInstance', 'apiActivateInstance', 'apiDeactivateInstance'];
