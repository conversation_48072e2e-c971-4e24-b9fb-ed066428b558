import { useState, useEffect } from 'react';
import { Button, Form, Input, Modal, message, Space, Table, Tabs, InputNumber, Select, Tag } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import {
  apiWalletGetUserWallets,
  apiWalletCreateDefaultWallet,
  apiWalletDeposit,
  apiWalletWithdraw,
  apiWalletGetTransactionsPage
} from '@/apis/apis.api';

interface WalletManagementModalProps {
  visible: boolean;
  onCancel: () => void;
  userId: number;
  userName: string;
  currencies: Array<{ code: string; name: string }>;
}

const WalletManagementModal: React.FC<WalletManagementModalProps> = ({
  visible,
  onCancel,
  userId,
  userName,
  currencies
}) => {
  const [walletForm] = Form.useForm();
  const [wallets, setWallets] = useState<WalletBo[]>([]);
  const [selectedWallet, setSelectedWallet] = useState<WalletBo | null>(null);
  const [walletAction, setWalletAction] = useState<'deposit' | 'withdraw' | null>(null);
  const [walletTransactions, setWalletTransactions] = useState<TransactionBo[]>([]);
  const [transactionLoading, setTransactionLoading] = useState(false);
  const [activeTabKey, setActiveTabKey] = useState('wallets');

  // Load wallet data when modal becomes visible
  useEffect(() => {
    if (visible) {
      loadWallets();
    }
  }, [visible, userId]);

  // Load wallets
  const loadWallets = async () => {
    try {
      const userWallets = await apiWalletGetUserWallets(userId);
      setWallets(userWallets);

      // If there are wallets, select the first one and load its transactions
      if (userWallets.length > 0) {
        setSelectedWallet(userWallets[0]);
        await loadWalletTransactions(userWallets[0].id);
      }
    } catch (error) {
      console.error('获取用户钱包失败:', error);
      message.error('获取用户钱包失败');
    }
  };

  // Load wallet transactions
  const loadWalletTransactions = async (walletId: number) => {
    try {
      setTransactionLoading(true);
      const response = await apiWalletGetTransactionsPage(walletId, {
        current: 0,
        size: 10,
        sorts: [{ field: 'createdAt', direction: 'DESC' }]
      });
      setWalletTransactions(response.records || []);
    } catch (error) {
      console.error('获取钱包交易记录失败:', error);
      message.error('获取钱包交易记录失败');
    } finally {
      setTransactionLoading(false);
    }
  };

  // Create a default wallet for user
  const handleCreateDefaultWallet = async () => {
    try {
      const values = await walletForm.validateFields();
      await apiWalletCreateDefaultWallet(userId, values.currency);

      message.success('创建钱包成功');

      // Refresh wallet list
      await loadWallets();

      walletForm.resetFields();
    } catch (error) {
      console.error('创建钱包失败:', error);
      message.error('创建钱包失败');
    }
  };

  // Handle wallet deposit
  const handleWalletDeposit = async () => {
    try {
      if (!selectedWallet) return;

      const values = await walletForm.validateFields();
      await apiWalletDeposit(selectedWallet.id, values.amount, values.referenceId);

      message.success('充值成功');

      // Refresh wallet data
      await loadWallets();

      setWalletAction(null);
      walletForm.resetFields();
    } catch (error) {
      console.error('充值失败:', error);
      message.error('充值失败');
    }
  };

  // Handle wallet withdraw
  const handleWalletWithdraw = async () => {
    try {
      if (!selectedWallet) return;

      const values = await walletForm.validateFields();
      await apiWalletWithdraw(selectedWallet.id, values.amount, values.referenceId);

      message.success('提现成功');

      // Refresh wallet data
      await loadWallets();

      setWalletAction(null);
      walletForm.resetFields();
    } catch (error) {
      console.error('提现失败:', error);
      message.error('提现失败');
    }
  };

  // Format transaction type
  const formatTransactionType = (type: string | number) => {
    // 交易类型数值映射
    const TRANSACTION_TYPE_VALUE_MAP = {
      0: 'DEPOSIT',
      1: 'WITHDRAW',
      2: 'PAYMENT',
      3: 'REFUND',
      4: 'TRANSFER_IN',
      5: 'TRANSFER_OUT',
      6: 'BONUS',
      7: 'ADJUSTMENT',
      8: 'FREEZE',
      9: 'UNFREEZE',
      10: 'REMITTANCE_DEPOSIT'
    };

    // 如果是数字，先从映射中获取对应的字符串值
    const typeKey = typeof type === 'number' ? TRANSACTION_TYPE_VALUE_MAP[type] : type;

    const typeMap: Record<string, string> = {
      'DEPOSIT': '充值',
      'WITHDRAW': '提现',
      'PAYMENT': '支付',
      'REFUND': '退款',
      'TRANSFER_IN': '转入',
      'TRANSFER_OUT': '转出',
      'BONUS': '奖励',
      'ADJUSTMENT': '调整',
      'FREEZE': '冻结',
      'UNFREEZE': '解冻',
      'REMITTANCE_DEPOSIT': '汇款充值',
    };

    // 如果没有找到映射，则直接返回原始值
    if (!typeKey && typeof type === 'number') {
      return `未知类型(${type})`;
    }

    return typeMap[typeKey] || typeKey;
  };

  // Format transaction status
  const formatTransactionStatus = (status: string | number) => {
    // 交易状态数值映射
    const TRANSACTION_STATUS_VALUE_MAP = {
      0: 'PENDING',
      1: 'COMPLETED',
      2: 'FAILED',
      3: 'CANCELLED',
      4: 'REJECTED'
    };

    // 如果是数字，先从映射中获取对应的字符串值
    const statusKey = typeof status === 'number' ? TRANSACTION_STATUS_VALUE_MAP[status] : status;

    const statusMap: Record<string, { text: string, color: string }> = {
      'PENDING': { text: '处理中', color: 'orange' },
      'COMPLETED': { text: '已完成', color: 'green' },
      'FAILED': { text: '失败', color: 'red' },
      'CANCELLED': { text: '已取消', color: 'gray' },
      'REJECTED': { text: '已拒绝', color: 'red' },
    };

    // 如果没有找到映射，则直接返回原始值
    if (!statusKey && typeof status === 'number') {
      return <Tag color="default">未知状态({status})</Tag>;
    }

    const statusInfo = statusMap[statusKey] || { text: statusKey, color: 'default' };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  // Handle tab change
  const handleTabChange = (activeKey: string) => {
    setActiveTabKey(activeKey);
    // 切换tab时清除充值提现操作
    if (walletAction) {
      setWalletAction(null);
      walletForm.resetFields();
    }
  };

  return (
    <Modal
      title={`钱包管理 - ${userName}`}
      open={visible}
      width={800}
      footer={null}
      onCancel={onCancel}
    >
      <Tabs defaultActiveKey="wallets" onChange={handleTabChange}>
        <Tabs.TabPane tab="钱包列表" key="wallets">
          <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
            <h4>用户钱包</h4>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setWalletAction(null);
                walletForm.resetFields();
                Modal.confirm({
                  title: '创建新钱包',
                  content: (
                    <Form form={walletForm} layout="vertical">
                      <Form.Item
                        name="currency"
                        label="货币"
                        rules={[{ required: true, message: '请选择货币' }]}
                      >
                        <Select
                          placeholder="请选择货币"
                          options={currencies.map(currency => ({
                            label: `${currency.name} (${currency.code})`,
                            value: currency.code
                          }))}
                          showSearch
                          optionFilterProp="label"
                        />
                      </Form.Item>
                    </Form>
                  ),
                  onOk: handleCreateDefaultWallet,
                });
              }}
            >
              创建钱包
            </Button>
          </div>
          <Table
            dataSource={wallets}
            columns={[
              {
                title: 'ID',
                dataIndex: 'id',
                key: 'id',
              },
              {
                title: '货币',
                dataIndex: 'currency',
                key: 'currency',
              },
              {
                title: '余额',
                dataIndex: 'balance',
                key: 'balance',
              },
              {
                title: '操作',
                key: 'actions',
                render: (_, record: WalletBo) => (
                  <Space>
                    <a onClick={() => {
                      setSelectedWallet(record);
                      setWalletAction('deposit');
                    }}>充值</a>
                    <a onClick={() => {
                      setSelectedWallet(record);
                      setWalletAction('withdraw');
                    }}>提现</a>
                  </Space>
                ),
              },
            ]}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab="交易记录" key="transactions">
          <Table
            dataSource={walletTransactions}
            columns={[
              {
                title: '交易号',
                dataIndex: 'transactionNo',
                key: 'transactionNo',
                ellipsis: true,
                copyable: true,
              },
              {
                title: '类型',
                dataIndex: 'type',
                key: 'type',
                render: (type) => formatTransactionType(type),
              },
              {
                title: '金额',
                dataIndex: 'amount',
                key: 'amount',
                render: (amount, record) => `${amount} ${record.currency}`,
              },
              {
                title: '状态',
                dataIndex: 'status',
                key: 'status',
                render: (status) => formatTransactionStatus(status),
              },
              {
                title: '创建时间',
                dataIndex: 'createdAt',
                key: 'createdAt',
                render: (timestamp) => new Date(timestamp).toLocaleString(),
              },
            ]}
            loading={transactionLoading}
          />
        </Tabs.TabPane>
      </Tabs>

      {walletAction && (
        <Form
          form={walletForm}
          layout="vertical"
          style={{ marginTop: 16 }}
        >
          <Form.Item
            name="amount"
            label={walletAction === 'deposit' ? '充值金额' : '提现金额'}
            rules={[
              { required: true, message: '请输入金额' },
              { min: 0, message: '金额不能为负' }
            ]}
          >
            <InputNumber placeholder="请输入金额" style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="referenceId"
            label="参考ID"
            rules={[{ required: true, message: '请输入参考ID' }]}
          >
            <Input placeholder="请输入参考ID" />
          </Form.Item>

          <Button
            type="primary"
            onClick={walletAction === 'deposit' ? handleWalletDeposit : handleWalletWithdraw}
          >
            {walletAction === 'deposit' ? '充值' : '提现'}
          </Button>
        </Form>
      )}
    </Modal>
  );
};

export default WalletManagementModal;