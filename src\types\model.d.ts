// /**  数据模型 */
// type TFilterItem<T = any> =
//   | { field: string; operator: 'custom'; value: any }
//   | { field: keyof T | (keyof T)[]; operator: 'like'; value: string }
//   | { field: keyof T | (keyof T)[]; operator: 'rl'; value: string }
//   | { field: keyof T; operator: 'gt'; value: string | number }
//   | { field: keyof T; operator: 'gte'; value: string | number }
//   | { field: keyof T; operator: 'lt'; value: string | number }
//   | { field: keyof T; operator: 'lte'; value: string | number }
//   | { field: keyof T; operator: 'eq'; value: string | number | boolean | null }
//   | { field: keyof T; operator: 'ne'; value: string | number | boolean | null }
//   | { field: keyof T; operator: 'in'; value: (string | number)[] }
//   | { field: keyof T; operator: 'nin'; value: (string | number)[] }
//   | { field: keyof T; operator: 'range'; value: [number | null, number | null] } // 如果是date应转为时间戳
//   | { field: keyof T; operator: 'inRange'; value: [number | null, number | null][] };

// type TSortItem<T = any> = { field: keyof T; direction: 'ASC' | 'DESC' };

type TPageListReq= {
  current: number;
  size: number;
  filters?: TQueryFilter[];
  sorts?: TQuerySort[];
};

type TPageListRes<T = any> = { records: T[]; total: number };

/** 管理员用户对象 */
interface TAdminUser {
  /** 用户ID */
  id: string;
  /** 用户名 */
  username: string;
  /** 昵称 */
  nickname: string;
  /** 权限列表 */
  permissions: string[];
  /** 角色列表 */
  roles: string[];
  /** 首页路径 */
  home: string;
  /** 最后登录时间 */
  lastLoginAt: string;
  /** 最后登录IP */
  lastLoginIp: string;
  /** 创建时间 */
  createdAt: string;
}

/** 管理员用户业务对象 */
interface TAdminUserBo {
  /** 用户ID */
  id: string;
  /** 用户名 */
  username: string;
  /** 真实姓名 */
  realname: string;
  /** 邮箱 */
  email: string;
  /** 电话 */
  phone: string;
  /** 密码 */
  password: string;
  /** 头像 */
  avatar: string;
  /** 昵称 */
  nickname: string;
  /** 最后登录时间 */
  lastLoginAt: string;
  /** 最后登录IP */
  lastLoginIp: string;
  /** 角色列表 */
  roles: string[];
  /** 权限列表 */
  permissions: string[];
  /** 是否激活 */
  isActive: boolean;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
}

/** 更新管理员用户请求 */
interface TUpdateAdminUserRequest {
  /** 邮箱 */
  email?: string;
  /** 电话 */
  phone?: string;
  /** 昵称 */
  nickname?: string;
  /** 头像 */
  avatar?: string;
  /** 密码 */
  password?: string;
}

/** 更新密码请求 */
interface TUpdatePasswordRequest {
  /** 旧密码 */
  oldPassword: string;
  /** 新密码 */
  newPassword: string;
  /** 确认密码 */
  confirmPassword: string;
}

/** 创建管理员用户请求 */
interface TCreateAdminUserRequest {
  /** 用户名 */
  username: string;
  /** 密码 */
  password: string;
  /** 邮箱 */
  email?: string;
  /** 电话 */
  phone?: string;
  /** 昵称 */
  nickname?: string;
  /** 角色ID列表 */
  roleIds?: string[];
  /** 角色代码列表 */
  roleCodes?: string[];
}

/** 管理员用户分页结果 */
interface TPageResultAdminUserBo {
  /** 每页大小 */
  size: number;
  /** 当前页码 */
  current: number;
  /** 总记录数 */
  total: number;
  /** 记录列表 */
  records: TAdminUserBo[];
  /** 偏移量 */
  offset: number;
}

interface TMsgDetailBo {
  _id: string;
  content: string;
  createdAt: string;
  expiredAt: string;
  extra: { [key: string]: any };
  outerid: string;
  outerType: string;
  processUserid: string;
  /** 是否已读 */
  read: boolean;
  receiveUserid: string;
  sendUserid: string;
  status: string;
  title: string;
  /** 0:通知 1:待办 */
  type: string;
  upstringdAt: string;
}

/** 管理员角色对象 */
interface TAdminRole {
  /** 角色ID */
  id: string;
  /** 角色名称 */
  name: string;
  /** 角色代码 */
  code: string;
  /** 角色描述 */
  description: string;
  /** 是否激活 */
  isActive: boolean;
  /** 权限列表 */
  permissions: string[];
  /** 权限类型 1-后台 2-商家 */
  type?: number;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
}

/** 管理员角色业务对象 */
interface TAdminRoleBo {
  /** 角色ID */
  id: string;
  /** 角色名称 */
  name: string;
  /** 角色代码 */
  code: string;
  /** 角色描述 */
  description: string;
  /** 首页路径 */
  home: string;
  /** 是否激活 */
  isActive: boolean;
  /** 权限列表 */
  permissions: TAdminPermissionBo[];
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
}

/** 管理员权限业务对象 */
interface TAdminPermissionBo {
  /** 权限ID */
  id: string;
  /** 权限名称 */
  name: string;
  /** 权限代码 */
  code: string;
  /** 权限描述 */
  description: string;
  /** 是否激活 */
  isActive: boolean;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
}

/** 管理员权限对象 */
interface TAdminPermission {
  /** 权限ID */
  id: string;
  /** 权限名称 */
  name: string;
  /** 权限类型 */
  type?: number;
  /** 权限代码 */
  code: string;
  /** 权限描述 */
  description: string;
  /** 是否激活 */
  isActive: boolean;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
}

/** 创建管理员权限请求 */
interface TCreateAdminPermissionRequest {
  /** 权限名称 */
  name: string;
  /** 权限代码 */
  code: string;
  /** 权限描述 */
  description?: string;
}

/** 管理员权限分页结果 */
interface TPageResultAdminPermissionBo {
  /** 每页大小 */
  size: number;
  /** 当前页码 */
  current: number;
  /** 总记录数 */
  total: number;
  /** 记录列表 */
  records: TAdminPermission[];
  /** 偏移量 */
  offset: number;
}

/** 管理员角色分页结果 */
interface TPageResultAdminRoleBo {
  /** 每页大小 */
  size: number;
  /** 当前页码 */
  current: number;
  /** 总记录数 */
  total: number;
  /** 记录列表 */
  records: TAdminRole[];
  /** 偏移量 */
  offset: number;
}

/** 创建管理员角色请求 */
interface TCreateAdminRoleRequest {
  /** 角色名称 */
  name: string;
  /** 角色代码 */
  code: string;
  /** 角色描述 */
  description?: string;
  /** 首页路径 */
  home?: string;
  /** 权限ID列表 */
  permissionIds?: string[];
  /** 权限代码列表 */
  permissionCodes?: string[];
}

interface TCurrency {
  id: string;
  code: string;
  name: string;
  symbol: string;
  exchangeRate: number;
  isDefault: boolean;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface TLanguage {
  id: string;
  code: string;
  name: string;
  nativeName: string;
  isDefault: boolean;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface TTranslation {
  id?: string;
  key: string;
  languageCode: string;
  value: string;
  createdAt?: string;
  updatedAt?: string;
}

interface TTranslationKey {
  translationKey: string;
  key?: string; // 兼容旧数据
  module?: string;
  description?: string;
  translations: Record<string, TTranslationValue>;
}

interface TTranslationValue {
  id?: string;
  value: string;
  languageCode: string;
}

interface TUpdateTranslationsByKeyRequest {
  key: string;
  module?: string;
  description?: string;
  translations: Record<string, string>; // languageCode -> translation value
}

interface TTranslationsByKeyDto {
  key: string;
  module?: string;
  description?: string;
  translations: Record<string, TTranslationValue>;
}

interface TRegion {
  id: string;
  code: string;
  name: string;
  parentCode?: string;
  level: number; // 1: 国家/地区, 2: 省/州, 3: 城市
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface TExchangeRate {
  fromCurrency: string;
  toCurrency: string;
  rate: number;
  isActive: boolean;
  ruleId?: string;
  ruleAppliedRate?: number;
}

interface TSystemExchangeRateRule {
  id: string;
  createdBy?: number;
  createdAt?: string;
  updatedBy?: number;
  updatedAt?: string;
  ver?: number;
  name: string;
  description?: string;
  ruleType: 'FIXED_RATE' | 'PERCENTAGE_MARKUP' | 'PERCENTAGE_DISCOUNT' | 'FIXED_MARKUP' | 'FIXED_DISCOUNT';
  value: number;
  isActive: 'FALSE' | 'TRUE';
  isTemporary: 'FALSE' | 'TRUE';
  referenceCount?: number;
}

interface TExchangeRateRuleBo {
  id: string;
  name: string;
  description?: string;
  ruleType: 'FIXED_RATE' | 'PERCENTAGE_MARKUP' | 'PERCENTAGE_DISCOUNT' | 'FIXED_MARKUP' | 'FIXED_DISCOUNT';
  value: number;
  isTemporary: boolean;
  referenceCount?: number;
}

interface ClientUserBo {
  id: string;
  firstName: string;
  lastName: string;
  username: string;
  password?: string;
  region: string;
  language: string;
  currency: string;
  email: string;
  isActive: boolean;
  isEmailVerified: boolean;
  createdAt: number;
  updatedAt: number;
  permissions?: string[];
  roles?: string[];
}

interface CreateClientUserRequest {
  firstName: string;
  lastName: string;
  username: string;
  password: string;
  email: string;
  region?: string;
  language?: string;
  currency?: string;
}

interface UpdateClientUserRequest {
  firstName: string;
  lastName: string;
  email: string;
  region?: string;
  language?: string;
  currency?: string;
}

interface ResetPasswordRequest {
  token: string;
  newPassword: string;
}

// Wallet Interfaces
interface WalletBo {
  id: string;
  userId: string;
  balance: number;
  frozenAmount: number;
  currency: string;
  isActive: 'TRUE' | 'FALSE';
  type: string;
  lastTransactionTime: number;
  note: string;
  createdAt: number;
  updatedAt: number;
}

interface TransactionBo {
  id: string;
  transactionNo: string;
  userId: string;
  userInfo?: TClientUser;
  walletId: string;
  amount: number;
  balanceBefore: number;
  balanceAfter: number;
  currency: string;
  type: string;
  status: string;
  referenceId: string;
  referenceType: string;
  description: string;
  note: string;
  ipAddress: string;
  deviceInfo: string;
  completedTime: number;
  failureReason: string;
  operatorId: string;
  createdAt: number;
  updatedAt: number;
}

interface RemittanceDepositResponse {
  transactionId: string;
  rejectReason: string;
}

/** 系统设置对象 */
interface TSystemSettingBo {
  /** 系统设置ID */
  id: string;
  /** 系统设置键名 */
  key: string;
  /** 系统设置值 */
  value: string;
  /** 系统设置分组 */
  group: string;
  /** 系统设置描述 */
  description: string;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 创建者ID */
  createdBy: string;
  /** 更新者ID */
  updatedBy: string;
  /** 创建者用户名 */
  createdByUsername: string;
  /** 更新者用户名 */
  updatedByUsername: string;
  /** 是否激活 */
  active: boolean;
  /** 是否系统设置 */
  system: boolean;
  /** 是否加密 */
  encrypted: boolean;
}

/** 系统设置分页结果 */
interface TPageResultSystemSettingBo {
  /** 每页大小 */
  size: number;
  /** 当前页码 */
  current: number;
  /** 总记录数 */
  total: number;
  /** 记录列表 */
  records: TSystemSettingBo[];
  /** 偏移量 */
  offset: number;
}

// 定义文件上传响应结构
interface TFileUploadResponse {
  id: string;
  originalFilename: string;
  storedFilename: string;
  contentType: string;
  size: number;
  extension: string;
  url: string;
  module: string;
  referenceId?: string;
  referenceType?: string;
  temporary: boolean;
  createdAt: string;
  updatedAt: string;
}

/** 标签对象 */
interface TTagBo {
  /** 标签ID */
  id: string;
  /** 标签名称 */
  name: string;
  /** 标签描述 */
  description: string;
  /** 标签类型：0-百分比 1-数值 */
  type: number;
  /** 背景颜色 */
  backgroundColor: string;
  /** 文本颜色 */
  textColor: string;
  /** 排序顺序 */
  sortOrder: number;
  /** 是否激活 */
  isActive: boolean;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
}

/** 标签分页结果 */
interface TPageResultTagBo {
  /** 每页大小 */
  size: number;
  /** 当前页码 */
  current: number;
  /** 总记录数 */
  total: number;
  /** 记录列表 */
  records: TTagBo[];
  /** 偏移量 */
  offset: number;
}

/** 产品标签关联对象 */
interface TProductTagBo {
  /** 关联ID */
  id: string;
  /** 产品ID */
  productId: string;
  /** 产品名称 */
  productName: string;
  /** 标签ID */
  tagId: string;
  /** 标签名称 */
  tagName: string;
  /** 标签类型：PERCENTAGE-百分比 NUMERIC-数值 */
  tagType: 'PERCENTAGE' | 'NUMERIC';
  /** 标签背景颜色 */
  tagBackgroundColor: string;
  /** 标签文本颜色 */
  tagTextColor: string;
  /** 排序顺序 */
  sortOrder: number;
  /** 是否激活 */
  isActive: boolean;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
}

/** 分页查询参数 */
interface TPageQuery {
  /** 过滤条件列表 */
  filters?: TQueryFilter[];
  /** 排序条件列表 */
  sorts?: TQuerySort[];
  /** 是否去重 */
  distinct?: boolean;
  /** 去重字段 */
  distinctField?: string;
  /** 当前页码 */
  current: number;
  /** 每页大小 */
  size: number;
  /** 偏移量 */
  offset?: number;
}

/** 查询排序条件 */
interface TQuerySort {
  /** 排序字段 */
  field: string;
  /** 排序方向 */
  direction: string;
}

/** 查询过滤条件 */
interface TQueryFilter {
  /** 过滤字段 */
  field: string;
  /** 过滤操作符 */
  operator: string;
  /** 过滤值 */
  value: any;
}

/** 品牌业务对象 */
interface TProductBrandBo {
  /** 品牌ID */
  id: string;
  /** 品牌编码 */
  code: string;
  /** 品牌名称 */
  name: string;
  /** 品牌首字母 */
  firstLetter: string;
  /** 品牌描述 */
  description: string;
  /** 品牌Logo URL */
  logo: string;
  /** 品牌官网 */
  website: string;
  /** 所属国家 */
  country: string;
  /** 创建年份 */
  foundedYear: number;
  /** 排序顺序 */
  sortOrder: number;
  /** 是否为推荐品牌 */
  featured: boolean;
  /** 是否激活 */
  isActive: boolean;
  /** 分类对象列表 */
  categories: TProductCategoryBo[];
  /** 分类ID列表 */
  categoryIds: string[];
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
}

/** 分类业务对象 */
interface TProductCategoryBo {
  /** 分类ID */
  id: string;
  /** 分类编码 */
  code: string;
  /** 分类名称 */
  name: string;
  /** 分类描述 */
  description: string;
  /** 分类图标URL */
  icon: string;
  /** 分类Banner图片URL */
  bannerImage: string;
  /** 排序顺序 */
  sortOrder: number;
  /** 分类层级 */
  level: number;
  /** 是否激活 */
  isActive: boolean;
  /** 服务费比例 */
  serviceFeePercentage: number;
  /** 父级分类ID */
  parentId: string;
  /** 父级分类编码 */
  parentCode: string;
  /** 父级分类名称 */
  parentName: string;
  /** 子分类列表 */
  children: TProductCategoryBo[];
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
}

/** 通用返回对象 */
interface TResultObject {
  /** 返回码 */
  code: string;
  /** 返回消息 */
  message: string;
  /** 数据内容 */
  data: object;
}

interface TClientUser {
  /** 用户ID */
  id: string;
  /** 名字 */
  firstName: string;
  /** 姓氏 */
  lastName: string;
  /** 用户名 */
  username: string;
  /** 区域 */
  region: string;
  /** 语言 */
  language: string;
  /** 货币 */
  currency: string;
  /** 邮箱 */
  email: string;
}

/** 通知模板类型 */
type TNotificationType = 'SYSTEM' | 'ORDER' | 'TRANSACTION' | 'PRODUCT' | 'MARKETING' | 'OTHER';

/** 通知模板对象 */
interface TNotificationTemplateBo {
  /** 模板ID */
  id: string;
  /** 模板代码 */
  code: string;
  /** 模板名称 */
  name: string;
  /** 标题模板 */
  titleTemplate: string;
  /** 内容模板 */
  contentTemplate: string;
  /** 模板类型 */
  type: TNotificationType;
  /** 业务类型 */
  businessType?: string;
  /** 是否启用 */
  isEnabled: boolean;
  /** 描述 */
  description?: string;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
}

/** 用户通知对象 */
interface TUserNotificationBo {
  /** 通知ID */
  id: string;
  /** 用户ID */
  userId: string;
  /** 通知标题 */
  title: string;
  /** 通知内容 */
  content: string;
  /** 通知类型 */
  type: TNotificationType;
  /** 业务ID */
  businessId: string;
  /** 业务类型 */
  businessType?: string;
  /** 是否已读 */
  isRead: boolean;
  /** 阅读时间 */
  readTime?: string;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
}

/** 更新模板请求 */
interface TUpdateTemplateRequest {
  /** 模板代码 */
  code: string;
  /** 模板名称 */
  name: string;
  /** 标题模板 */
  titleTemplate: string;
  /** 内容模板 */
  contentTemplate: string;
  /** 模板类型 */
  type: TNotificationType;
  /** 业务类型 */
  businessType?: string;
  /** 描述 */
  description?: string;
}

/** 创建模板请求 */
interface TCreateTemplateRequest {
  /** 模板代码 */
  code: string;
  /** 模板名称 */
  name: string;
  /** 标题模板 */
  titleTemplate: string;
  /** 内容模板 */
  contentTemplate: string;
  /** 模板类型 */
  type: TNotificationType;
  /** 业务类型 */
  businessType?: string;
  /** 描述 */
  description?: string;
}

/** 发送模板通知请求 */
interface TSendTemplateNotificationRequest {
  /** 用户ID */
  userId: string;
  /** 模板代码 */
  templateCode: string;
  /** 变量 */
  variables?: Record<string, any>;
}

/** 批量发送模板通知请求 */
interface TBatchSendTemplateNotificationRequest {
  /** 用户ID列表 */
  userIds: string[];
  /** 模板代码 */
  templateCode: string;
  /** 变量 */
  variables?: Record<string, any>;
}

/** 通知模板分页结果 */
interface TPageResultNotificationTemplateBo {
  /** 每页大小 */
  size: number;
  /** 当前页码 */
  current: number;
  /** 总记录数 */
  total: number;
  /** 记录列表 */
  records: TNotificationTemplateBo[];
  /** 偏移量 */
  offset: number;
}