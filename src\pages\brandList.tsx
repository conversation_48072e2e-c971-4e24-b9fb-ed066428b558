import { Button, Form, Modal, Space, message, TreeSelect } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import I18nText from '@/components/I18nText';
import {
  apiBrandDelete,
  apiBrandGetById,
  apiBrandPageList,
  apiBrandSetActive,
  apiCategoryTree,
  apiSyncBrandCategories,
} from '@/apis/apis.api';
import { useNavigate } from 'react-router-dom';
import { ToolsUtil } from '@/utils/tools';
import { useRef, useState } from 'react';

const BrandList = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const tableRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(true);
  const [categoryModalVisible, setCategoryModalVisible] = useState(false);
  const [categoryLoading, setCategoryLoading] = useState(false);
  const [categoryTree, setCategoryTree] = useState<any[]>([]);
  const [selectedCategoryIds, setSelectedCategoryIds] = useState<string[]>([]);
  const [currentBrandId, setCurrentBrandId] = useState<string | null>(null);

  // Handle editing a brand
  const handleEdit = useMemoizedFn((id?: number) => {
    navigate(`/brand/edit${id ? `/${id}` : ''}`);
  });

  // 删除品牌
  const handleDelete = useMemoizedFn(async (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除该品牌吗？删除后不可恢复。',
      onOk: async () => {
        try {
          await apiBrandDelete(id);
          message.success('删除成功');
          tableRef.current?.reload();
        } catch (error) {
          console.error('删除品牌失败:', error);
          message.error('删除失败');
        }
      },
    });
  });

  // 设置品牌状态（激活/停用）
  const handleSetActive = useMemoizedFn(async (id: string, active: boolean) => {
    try {
      await apiBrandSetActive(id, active);
      message.success(active ? '激活成功' : '停用成功');
      tableRef.current?.reload();
    } catch (error) {
      console.error('设置品牌状态失败:', error);
      message.error(active ? '激活失败' : '停用失败');
    }
  });

  // 打开设置分类模态框
  const handleSetCategory = useMemoizedFn(async (brandId: string) => {
    setCurrentBrandId(brandId);
    setCategoryModalVisible(true);
    setCategoryLoading(true);

    try {
      const brand = await apiBrandGetById(brandId);
      
      setSelectedCategoryIds(brand.categoryIds || []);
      // 获取分类树
      const tree = await apiCategoryTree(true);
      const transformCategoryTree = (tree: API.ProductCategoryBo[]) => {
        return tree.map((item) => {
          let name = item.name;
          try {
            name = JSON.parse(name)['zh-CN'] || JSON.parse(name)['en-US'];
          } catch (e) {
            name = name;
          }
          return {
            id: item.id,
            title: name,
            key: item.id,
            value: item.id,
            children: item.children ? transformCategoryTree(item.children) : null,
          }
        });
      };
      const transformedTree = transformCategoryTree(tree);
      setCategoryTree(transformedTree);
    } catch (e) {
      message.error('加载分类数据失败');
    }
    setCategoryLoading(false);
  });

  // 保存分类
  const handleSaveCategory = useMemoizedFn(async () => {
    if (!currentBrandId) return;
    setCategoryLoading(true);
    try {
      await apiSyncBrandCategories(currentBrandId, selectedCategoryIds);
      message.success('分类设置成功');
      setCategoryModalVisible(false);
      tableRef.current?.reload();
    } catch (e) {
      message.error('分类设置失败');
    }
    setCategoryLoading(false);
  });

  const columns = [
    {
      title: '品牌名称',
      dataIndex: 'name',
      width: 150,
      ellipsis: true,
      render: (name: string) => <I18nText value={name} />,
    },
    {
      title: '编码',
      dataIndex: 'code',
      width: 120,
      ellipsis: true,
    },
    {
      title: '首字母',
      dataIndex: 'firstLetter',
      width: 80,
    },
    {
      title: '排序',
      dataIndex: 'sortOrder',
      width: 80,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      width: 80,
      valueEnum: {
        true: { text: '已激活', status: 'Success' },
        false: { text: '已停用', status: 'Error' },
      },
    },
    {
      title: '推荐',
      dataIndex: 'isFeatured',
      width: 80,
      search: false,
      valueEnum: {
        true: { text: '是', status: 'Success' },
        false: { text: '否', status: 'Default' },
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      valueType: 'option',
      render: (_, record) => (
        <Space>
          <a onClick={() => handleEdit(record.id)}>编辑</a>
          <a onClick={() => handleSetActive(record.id, !record.isActive)}>
            {record.isActive ? '停用' : '激活'}
          </a>
          <a onClick={() => handleDelete(record.id)}>删除</a>
          <a onClick={() => handleSetCategory(record.id)}>设置分类</a>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <ProTable
        actionRef={tableRef}
        headerTitle="品牌管理"
        rowKey="id"
        columns={columns}
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleEdit()}
          >
            新增
          </Button>,
        ]}
        request={async (params, sorter) => {
          setLoading(true);
          const filters: TQueryFilter[] = [];

          // 添加搜索条件
          if (params.name) {
            filters.push({ field: 'name', operator: 'like', value: params.name });
          }
          if (params.code) {
            filters.push({ field: 'code', operator: 'like', value: params.code });
          }
          if (params.firstLetter) {
            filters.push({ field: 'firstLetter', operator: 'eq', value: params.firstLetter });
          }
          if (params.isActive !== undefined) {
            filters.push({ field: 'isActive', operator: 'eq', value: params.isActive });
          }

          let sorts = ToolsUtil.tableSorterToPageListReqSort(sorter);

          // 添加默认排序，激活状态优先，然后是排序值
          if (!sorts || sorts.length === 0) {
            sorts = [{ field: 'isActive', direction: 'DESC' }, { field: 'sortOrder', direction: 'ASC' }];
          }

          try {
            let res;
            res = await apiBrandPageList({
              current: params.current - 1,
              size: params.pageSize,
              filters,
              sorts,
            });

            setLoading(false);
            return {
              total: res.total || 0,
              data: res.records || [],
              success: true,
            };
          } catch (error) {
            console.error('获取品牌列表失败:', error);
            message.error('获取品牌列表失败');
            setLoading(false);
            return {
              total: 0,
              data: [],
              success: false,
            };
          }
        }}
        loading={loading}
      />
      <Modal
        title="设置品牌分类"
        open={categoryModalVisible}
        onCancel={() => setCategoryModalVisible(false)}
        onOk={handleSaveCategory}
        confirmLoading={categoryLoading}
        destroyOnClose
      >
        <TreeSelect
          treeData={categoryTree}
          value={selectedCategoryIds}
          onChange={setSelectedCategoryIds}
          treeCheckable
          showCheckedStrategy={TreeSelect.SHOW_PARENT}
          placeholder="请选择分类"
          style={{ width: '100%' }}
          loading={categoryLoading}
          allowClear
          multiple
          maxTagCount={3}
        />
      </Modal>
    </div>
  );
};

export default BrandList;

BrandList.auth = ['apiBrandDelete', 'apiBrandPageList', 'apiBrandSetActive', 'apiCategoryTree', 'apiSyncBrandCategories'];
