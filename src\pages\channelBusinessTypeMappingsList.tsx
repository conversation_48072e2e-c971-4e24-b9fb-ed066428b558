import { 
   apiListChannelBusinessTypeMappings, 
   apiAssignBusinessType,
   apiUnassignBusinessType,
} from '@/apis/gen.apis';
import { useFormModal } from '@/components/FormilyModal';
import useStaticBusinessTypes from '@/hooks/useStaticBussinessTypes';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { App, Button, Space, Tag, Popconfirm } from 'antd';
import { useRef, useState } from 'react';

type TAssignFormData = {
  channelId: string;
  businessTypeId: number;
};

type TBatchAssignFormData = {
  ids: number[];
  businessTypeId: number;
};

function ChannelBusinessTypeMappingsList() {
  const tableRef = useRef<ActionType>();
  const { message } = App.useApp();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const { staticBusinessTypeOptions, staticBusinessTypeEnum } = useStaticBusinessTypes();


  const { formModalRef: assignFormModalRef, formModalHolder: assignFormModalHolder } =
    useFormModal<TAssignFormData>();

  const { formModalRef: batchAssignFormModalRef, formModalHolder: batchAssignFormModalHolder } =
    useFormModal<TBatchAssignFormData>();

  const showAssignModal = useMemoizedFn((record?: TChannelBusinessTypeMappingDTO) => {
    assignFormModalRef.current?.show({
      modalTitle: '分配业务类型',
      modalWidth: 600,
      onAutoSubmit: async (values: any) => {
        if (record?.id) {
          await  apiAssignBusinessType(record.id.toString(), values.businessTypeId.toString());
        }
        message.success('分配成功');
        tableRef.current?.reload();
      },
      initialValues: record ? { channelId: record.externalCode } : {},
      schema: {
        type: 'object',
        properties: {
          channelId: {
            title: '渠道ID',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              disabled: !!record,
            },
          },
          businessTypeId: {
            title: '业务类型',
            type: 'number',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              options: staticBusinessTypeOptions,
            },
          },
        },
      },
    });
  });

  

  const handleUnassign = useMemoizedFn(async (id: string) => {
    await  apiUnassignBusinessType(id.toString());
    message.success('取消分配成功');
    tableRef.current?.reload();
  });

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  return (
    <div>
      <ProTable<TChannelBusinessTypeMappingDTO>
        actionRef={tableRef}
        headerTitle='渠道业务类型映射管理'
        rowKey='id'
        rowSelection={rowSelection}
     
        search={{
          defaultCollapsed: false,
        }}
        columns={[
          { title: 'ID', dataIndex: 'id', search: false },
          { 
            title: '渠道类型', 
            dataIndex: 'channelType',
            valueEnum: {
              0: '自营',
              1: 'IPNUX',
            },
          },
          { title: '外部代码', dataIndex: 'externalCode' },
          { title: '业务类型', dataIndex: 'businessTypeId', valueEnum: staticBusinessTypeEnum, valueType: 'select' },
       
       
          { 
            title: '创建时间', 
            dataIndex: 'createdAt',
            valueType: 'dateTime',
            search: false,
          },
          {
            title: '操作',
            valueType: 'option',
            key: 'option',
            render: (_text, record) => {
              return (
                <Space>
                  <a onClick={() => showAssignModal(record)}>重新分配</a>
                  <Popconfirm
                    title="确定要取消分配这个业务类型吗？"
                    onConfirm={() => handleUnassign(record.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <a style={{ color: 'red' }}>取消分配</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
        request={async (params) => {
          const filters: any[] = [];

          if (params.channelType) {
            filters.push({ field: 'channelType', operator: 'eq', value: Number(params.channelType) });
          }
          if (params.externalCode) {
            filters.push({ field: 'externalCode', operator: 'like', value: params.externalCode });
          }
          if (params.businessTypeId) {
            filters.push({ field: 'businessTypeId', operator: 'eq', value: params.businessTypeId });
          }

          const res = await  apiListChannelBusinessTypeMappings({
            current: (params.current || 1) - 1,
            size: params.pageSize || 20,
            filters,
            sorts: [],
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}
      />
      {assignFormModalHolder}
      {batchAssignFormModalHolder}
    </div>
  );
}

export default ChannelBusinessTypeMappingsList;

ChannelBusinessTypeMappingsList.auth = [
  ' apiListChannelBusinessTypeMappings', 
  ' apiGetChannelBusinessTypeMappingById',
  ' apiAssignBusinessType',
  ' apiUnassignBusinessType',
  ' apiBatchAssignBusinessType',
  ' apiBatchUnassignBusinessType'
];
