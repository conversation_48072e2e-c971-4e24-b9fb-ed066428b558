import React, { useEffect, useImperativeHandle, forwardRef } from 'react';
import { Button, Divider, Form, Input, InputNumber, message, Modal, Space, Switch, Table, Tag, Typography } from 'antd';
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import I18nField from '@/components/I18nField';
import I18nText from '@/components/I18nText';
import { useModel } from 'foca';
import { languageModel } from '@/stores/languageModel';

interface ProductAttributesAndVariantsProps {
  attributes: any[];
  setAttributes: React.Dispatch<React.SetStateAction<any[]>>;
  variants: any[];
  setVariants: React.Dispatch<React.SetStateAction<any[]>>;
  form: any; // Main product form
  product?: any; // Product data for edit mode
}

interface ProductAttributesAndVariantsRef {
  handleGenerateVariants: () => void;
}

const ProductAttributesAndVariants = forwardRef<
  ProductAttributesAndVariantsRef,
  ProductAttributesAndVariantsProps
>((props, ref) => {
  const { attributes, setAttributes, variants, setVariants, form, product } = props;
  const [variantForm] = Form.useForm();
  const [attributeForm] = Form.useForm();

  // Get languages from the store
  const { languages } = useModel(languageModel);
  const [isVariantModalVisible, setIsVariantModalVisible] = React.useState(false);
  const [isAttributeModalVisible, setIsAttributeModalVisible] = React.useState(false);
  const [editingVariant, setEditingVariant] = React.useState<any>(null);
  const [editingAttribute, setEditingAttribute] = React.useState<any>(null);

  // Process existing variants and extract attributes when component mounts or product changes
  useEffect(() => {
    if (product && product.variants && product.variants.length > 0) {
      processExistingVariants(product.variants);
    }
  }, [product]);

  // Process existing variants and extract attributes
  const processExistingVariants = (existingVariants: any[]) => {
    if (!existingVariants || existingVariants.length === 0) return;

    // Extract unique attributes from variants
    const attributesMap = new Map();

    existingVariants.forEach(variant => {
      if (variant.attributes && variant.attributes.length > 0) {
        variant.attributes.forEach((attr: any) => {
          // Skip attributes that are not variant attributes
          if (attr.variantAttribute === false) return;

          if (!attributesMap.has(attr.attributeCode)) {
            attributesMap.set(attr.attributeCode, {
              id: attr.attributeId,
              code: attr.attributeCode,
              name: attr.attributeName,
              options: [],
              isVariantAttribute: true
            });
          }

          const attrOptions = attributesMap.get(attr.attributeCode).options;
          if (attr.value && !attrOptions.includes(attr.value)) {
            attrOptions.push(attr.value);
          }
        });
      } else if (variant.attributeValues) {
        // Handle attributeValues format
        Object.entries(variant.attributeValues).forEach(([code, value]) => {
          // Skip if we know this is not a variant attribute (if we have that information)
          if (variant.variantAttributes && variant.variantAttributes[code] === false) return;

          if (!attributesMap.has(code)) {
            // Find attribute name from variant attributes if available
            let attrName = code;
            if (variant.attributes) {
              const attrObj = variant.attributes.find((a: any) => a.attributeCode === code);
              if (attrObj) {
                attrName = attrObj.attributeName;
                // Skip if explicitly marked as not a variant attribute
                if (attrObj.variantAttribute === false) return;
              }
            }

            attributesMap.set(code, {
              id: Date.now() + Math.random(), // Generate temporary ID
              code,
              name: attrName,
              options: [],
              isVariantAttribute: true
            });
          }

          const attrOptions = attributesMap.get(code).options;
          if (value && !attrOptions.includes(value)) {
            attrOptions.push(value);
          }
        });
      }
    });

    // Convert map to array
    const extractedAttributes = Array.from(attributesMap.values());

    // Only update if we have new attributes
    if (extractedAttributes.length > 0) {
      setAttributes(extractedAttributes);

      // Process variants to match our expected format
      const processedVariants = existingVariants.map(variant => {
        let variantAttributes: Record<string, any> = {};

        // Process attributes array if available
        if (variant.attributes && variant.attributes.length > 0) {
          variant.attributes.forEach((attr: any) => {
            // Only include variant attributes
            if (attr.variantAttribute !== false) {
              variantAttributes[attr.attributeCode] = attr.value;
            }
          });
        }
        // Use attributeValues if available
        else if (variant.attributeValues) {
          // Filter to only include variant attributes if we have that information
          if (variant.variantAttributes) {
            Object.entries(variant.attributeValues).forEach(([code, value]) => {
              if (variant.variantAttributes[code] !== false) {
                variantAttributes[code] = value;
              }
            });
          } else {
            variantAttributes = variant.attributeValues;
          }
        }

        return {
          id: variant.id,
          sku: variant.sku,
          name: variant.name,
          price: variant.price || 0,
          specialPrice: variant.specialPrice,
          cost: variant.cost,
          isActive: variant.active !== undefined ? variant.active : true,
          attributes: variantAttributes
        };
      });

      setVariants(processedVariants);
      message.success(`已加载 ${processedVariants.length} 个变体和 ${extractedAttributes.length} 个属性`);
    }
  };

  // Generate variants based on attributes
  const handleGenerateVariants = () => {
    // Get all attributes that are variant attributes
    if (attributes.length === 0) {
      message.warning('请先添加属性');
      return;
    }

    // Check if all attributes have options
    const invalidAttributes = attributes.filter(attr =>
      !attr.options || attr.options.length === 0
    );

    if (invalidAttributes.length > 0) {
      message.error(`以下属性缺少属性值: ${invalidAttributes.map(attr => attr.name).join(', ')}`);
      return;
    }

    // Prepare all possible attribute values
    const attributeOptions: Record<string, any[]> = {};

    attributes.forEach(attr => {
      attributeOptions[attr.code] = attr.options || [];
    });

    // Generate all possible combinations
    const generateCombinations = (
      attributes: Record<string, any[]>,
      current: Record<string, any> = {},
      keys: string[] = Object.keys(attributes),
      index: number = 0
    ): Record<string, any>[] => {
      if (index >= keys.length) {
        return [{ ...current }];
      }

      const key = keys[index];
      const values = attributes[key];
      const result: Record<string, any>[] = [];

      values.forEach(value => {
        const newCurrent = { ...current, [key]: value };
        result.push(...generateCombinations(attributes, newCurrent, keys, index + 1));
      });

      return result;
    };

    const combinations = generateCombinations(attributeOptions);
    console.log('所有可能的组合数量:', combinations.length);

    // 创建变体ID映射，用于保留现有变体ID
    const variantMap = new Map();

    // 将现有变体添加到映射中
    variants.forEach(variant => {
      if (!variant.attributes) return;

      // 创建变体属性的签名
      const signature = Object.keys(variant.attributes)
        .sort()
        .map(key => `${key}:${variant.attributes[key]}`)
        .join('|');

      variantMap.set(signature, variant);
    });

    // 记录新创建和保留的变体数量
    let newVariantsCount = 0;
    let preservedVariantsCount = 0;

    // 基于产品名称和代码创建变体
    const productName = form.getFieldValue('name') || '商品';
    const productCode = form.getFieldValue('code') || 'SKU';

    // 为每个组合创建或保留变体
    const newVariants = combinations.map((combination, index) => {
      // 创建此组合的签名
      const signature = Object.keys(combination)
        .sort()
        .map(key => `${key}:${combination[key]}`)
        .join('|');

      // 检查是否有匹配的现有变体
      const existingVariant = variantMap.get(signature);

      if (existingVariant) {
        // 保留现有变体
        preservedVariantsCount++;
        return {
          ...existingVariant,
          attributes: combination
        };
      } else {
        // 生成变体名称
        const variantName = Object.entries(combination)
          .map(([key, value]) => {
            const attr = attributes.find(a => a.code === key);
            return attr ? `${value}` : '';
          })
          .filter(Boolean)
          .join('-');

        // 生成SKU
        const sku = `${productCode}-${index + 1}`;

        // 创建新变体
        newVariantsCount++;
        return {
          id: `new-${Date.now()}-${index}`,
          sku,
          name: `${productName} ${variantName}`,
          price: 0,
          isActive: true,
          attributes: combination
        };
      }
    });

    console.log('生成的变体数量:', newVariants.length);

    // 更新变体列表
    setVariants(newVariants);

    // 提供详细反馈
    if (newVariantsCount > 0 && preservedVariantsCount > 0) {
      message.success(`成功保留 ${preservedVariantsCount} 个已有变体，新增 ${newVariantsCount} 个变体`);
    } else if (newVariantsCount > 0) {
      message.success(`成功生成 ${newVariantsCount} 个新变体`);
    } else if (preservedVariantsCount > 0) {
      message.success(`成功保留 ${preservedVariantsCount} 个已有变体`);
    } else {
      message.success(`所有变体已是最新状态，无需更新`);
    }
  };

  // Generate variants based on attributes
  const generateVariantsWithAttributes = (newAttributes: any[]) => {
    // Get all attributes that are variant attributes
    if (newAttributes.length === 0) {
      message.warning('请先添加属性');
      return;
    }

    // Check if all attributes have options
    const invalidAttributes = newAttributes.filter(attr =>
      !attr.options || attr.options.length === 0
    );

    if (invalidAttributes.length > 0) {
      message.error(`以下属性缺少属性值: ${invalidAttributes.map(attr => attr.name).join(', ')}`);
      return;
    }

    // 如果没有现有变体，则生成所有可能的组合
    if (variants.length === 0) {
      // 准备所有属性的可能值
      const attributeOptions: Record<string, any[]> = {};

      newAttributes.forEach(attr => {
        attributeOptions[attr.code] = attr.options || [];
      });

      // 生成所有可能的组合
      const generateCombinations = (
        attributes: Record<string, any[]>,
        current: Record<string, any> = {},
        keys: string[] = Object.keys(attributes),
        index: number = 0
      ): Record<string, any>[] => {
        if (index >= keys.length) {
          return [{ ...current }];
        }

        const key = keys[index];
        const values = attributes[key];
        const result: Record<string, any>[] = [];

        values.forEach(value => {
          const newCurrent = { ...current, [key]: value };
          result.push(...generateCombinations(attributes, newCurrent, keys, index + 1));
        });

        return result;
      };

      const combinations = generateCombinations(attributeOptions);
      console.log('所有可能的组合数量:', combinations.length);

      // 基于产品名称和代码创建变体
      const productName = form.getFieldValue('name') || '商品';
      const productCode = form.getFieldValue('code') || 'SKU';

      // 为每个组合创建变体
      const newVariants = combinations.map((combination, index) => {
        // 生成变体名称
        const variantName = Object.entries(combination)
          .map(([key, value]) => {
            const attr = newAttributes.find(a => a.code === key);
            return attr ? `${value}` : '';
          })
          .filter(Boolean)
          .join('-');

        // 生成SKU
        const sku = `${productCode}-${index + 1}`;

        // 创建新变体
        return {
          id: `new-${Date.now()}-${index}`,
          sku,
          name: `${productName} ${variantName}`,
          price: 0,
          isActive: true,
          attributes: combination
        };
      });

      // 更新变体列表
      setVariants(newVariants);
      message.success(`成功生成 ${newVariants.length} 个新变体`);
      return;
    }

    // 如果有现有变体，则基于现有变体扩展

    // 找出新添加的属性
    const existingAttributeCodes = new Set();
    variants.forEach(variant => {
      if (variant.attributes) {
        Object.keys(variant.attributes).forEach(code => {
          existingAttributeCodes.add(code);
        });
      }
    });

    const newlyAddedAttributes = newAttributes.filter(attr =>
      !existingAttributeCodes.has(attr.code)
    );

    // 如果没有新添加的属性，则不需要扩展变体
    if (newlyAddedAttributes.length === 0) {
      message.info('没有新添加的属性，无需扩展变体');
      return;
    }

    console.log('新添加的属性:', newlyAddedAttributes);

    // 准备新属性的可能值
    const newAttributeOptions: Record<string, any[]> = {};
    newlyAddedAttributes.forEach(attr => {
      newAttributeOptions[attr.code] = attr.options || [];
    });

    // 生成新属性的所有可能组合
    const generateNewCombinations = (
      attributes: Record<string, any[]>,
      current: Record<string, any> = {},
      keys: string[] = Object.keys(attributes),
      index: number = 0
    ): Record<string, any>[] => {
      if (index >= keys.length) {
        return [{ ...current }];
      }

      const key = keys[index];
      const values = attributes[key];
      const result: Record<string, any>[] = [];

      values.forEach(value => {
        const newCurrent = { ...current, [key]: value };
        result.push(...generateNewCombinations(attributes, newCurrent, keys, index + 1));
      });

      return result;
    };

    const newAttributeCombinations = generateNewCombinations(newAttributeOptions);
    console.log('新属性的组合数量:', newAttributeCombinations.length);

    // 基于现有变体和新属性组合扩展变体
    let expandedVariants: any[] = [];
    let preservedCount = 0;
    let newCount = 0;

    // 创建一个集合来跟踪已使用的SKU
    const usedSkus = new Set<string>();

    // 收集所有现有变体的SKU
    variants.forEach(variant => {
      if (variant.sku) {
        usedSkus.add(variant.sku);
      }
    });

    // 生成唯一的SKU
    const generateUniqueSku = (baseSku: string, index: number): string => {
      let sku = `${baseSku}-${index}`;
      let counter = 1;

      // 如果SKU已存在，则添加递增的计数器直到找到唯一的SKU
      while (usedSkus.has(sku)) {
        sku = `${baseSku}-${index}-${counter}`;
        counter++;
      }

      // 将新SKU添加到已使用集合中
      usedSkus.add(sku);
      return sku;
    };

    // 为每个现有变体创建新的扩展变体
    variants.forEach((variant, variantIndex) => {
      // 保留原始变体
      preservedCount++;
      expandedVariants.push(variant);

      // 为每个新属性组合创建扩展变体
      newAttributeCombinations.forEach((newCombination, index) => {
        if (index === 0) {
          // 第一个组合直接添加到原始变体上
          variant.attributes = {
            ...variant.attributes,
            ...newCombination
          };

          // 更新变体名称，但保留原始SKU
          const variantName = Object.entries(variant.attributes)
            .map(([key, value]) => {
              const attr = newAttributes.find(a => a.code === key);
              return attr ? `${value}` : '';
            })
            .filter(Boolean)
            .join('-');

          variant.name = `${form.getFieldValue('name') || '商品'} ${variantName}`;
        } else {
          // 其他组合创建新的变体
          const productCode = form.getFieldValue('code') || 'SKU';
          const uniqueSku = generateUniqueSku(productCode, variantIndex * 100 + index);

          const expandedVariant = {
            ...variant,
            id: `new-${Date.now()}-${preservedCount}-${index}`,
            sku: uniqueSku, // 使用生成的唯一SKU
            attributes: {
              ...variant.attributes,
              ...newCombination
            }
          };

          // 更新变体名称
          const variantName = Object.entries(expandedVariant.attributes)
            .map(([key, value]) => {
              const attr = newAttributes.find(a => a.code === key);
              return attr ? `${value}` : '';
            })
            .filter(Boolean)
            .join('-');

          expandedVariant.name = `${form.getFieldValue('name') || '商品'} ${variantName}`;

          expandedVariants.push(expandedVariant);
          newCount++;
        }
      });
    });

    // 更新变体列表
    setVariants(expandedVariants);

    message.success(`成功保留 ${preservedCount} 个已有变体，新增 ${newCount} 个变体`);
  };

  const renderVariantModal = () => {
    const [currentAttributes, setCurrentAttributes] = React.useState<Record<string, any>>({});

    React.useEffect(() => {
      if (editingVariant && editingVariant.attributes) {
        setCurrentAttributes({...editingVariant.attributes});
      } else {
        setCurrentAttributes({});
      }
    }, [editingVariant]);

    return (
      <Modal
        title={editingVariant ? '编辑变体' : '添加变体'}
        open={isVariantModalVisible}
        onCancel={() => {
          setIsVariantModalVisible(false);
          setEditingVariant(null);
          variantForm.resetFields();
          setCurrentAttributes({});
        }}
        onOk={() => {
          variantForm.validateFields().then(values => {
            const newVariants = [...variants];
            const variant = {
              id: editingVariant ? editingVariant.id : `new-${Date.now()}`,
              sku: values.sku,
              name: values.name,
              price: values.price,
              isActive: values.isActive,
              attributes: currentAttributes
            };

            if (editingVariant) {
              const index = newVariants.findIndex(v => v.id === editingVariant.id);
              if (index !== -1) {
                newVariants[index] = variant;
              }
            } else {
              newVariants.push(variant);
            }

            setVariants(newVariants);
            setIsVariantModalVisible(false);
            setEditingVariant(null);
            variantForm.resetFields();
            setCurrentAttributes({});
          });
        }}
        width={600}
      >
        <Form
          form={variantForm}
          layout="vertical"
        >
          <Form.Item
            name="sku"
            label="SKU"
            rules={[{ required: true, message: '请输入SKU' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入名称' }]}
          >
            <I18nField>
              <Input placeholder="请输入名称" />
            </I18nField>
          </Form.Item>

          <Form.Item
            name="price"
            label="价格"
            rules={[{ required: true, message: '请输入价格' }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Divider orientation="left">变体属性</Divider>

          {Object.keys(currentAttributes).length > 0 ? (
            <div style={{ marginBottom: 16 }}>
              {Object.entries(currentAttributes).map(([key, value]) => {
                const attr = attributes.find(a => a.code === key);
                return (
                  <div key={key} style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                    <div style={{ width: '40%', paddingRight: 8 }}>
                      <strong>{attr?.name || key}:</strong>
                    </div>
                    <div style={{ flex: 1 }}>
                      {value as string}
                    </div>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => {
                        const updatedAttributes = { ...currentAttributes };
                        delete updatedAttributes[key];

                        setCurrentAttributes(updatedAttributes);

                        message.success(`已从变体中移除 ${attr?.name || key} 属性`);
                      }}
                    />
                  </div>
                );
              })}
            </div>
          ) : (
            <Typography.Text type="secondary">
              此变体没有属性
            </Typography.Text>
          )}
        </Form>
      </Modal>
    );
  };

  // 暴露handleGenerateVariants方法给父组件
  useImperativeHandle(ref, () => ({
    handleGenerateVariants
  }));

  const initAttributeForm = (attribute?: any) => {
    if (attribute) {
      attributeForm.setFieldsValue({
        name: attribute.name,
        code: attribute.code,
        options: attribute.options || []
      });
      setEditingAttribute(attribute);
    } else {
      attributeForm.resetFields();
      attributeForm.setFieldsValue({
        options: []
      });
      setEditingAttribute(null);
    }
  };

  return (
    <>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            initAttributeForm();
            setIsAttributeModalVisible(true);
          }}
        >
          添加属性
        </Button>
      </div>

      <Table
        dataSource={attributes}
        rowKey="id"
        pagination={false}
        columns={[
          {
            title: '属性名称',
            dataIndex: 'name',
            key: 'name',
          },
          {
            title: '属性代码',
            dataIndex: 'code',
            key: 'code',
          },
          {
            title: '属性值',
            dataIndex: 'options',
            key: 'options',
            render: (options) => {
              if (options && options.length > 0) {
                return (
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
                    {options.map((option: string, index: number) => (
                      <Tag key={index} color="blue">{option}</Tag>
                    ))}
                  </div>
                );
              }
              return '-';
            }
          },
          {
            title: '操作',
            key: 'action',
            render: (_, record, index) => (
              <Space size="small">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => {
                    initAttributeForm(record);
                    setIsAttributeModalVisible(true);
                  }}
                >
                  编辑
                </Button>
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => {
                    // 检查是否有变体使用了此属性
                    const isUsedInVariants = variants.some(variant =>
                      variant.attributes && record.code in variant.attributes
                    );

                    if (isUsedInVariants) {
                      message.error('此属性已被用于生成变体，无法删除');
                      return;
                    }

                    const newAttributes = [...attributes];
                    newAttributes.splice(index, 1);
                    setAttributes(newAttributes);
                    message.success('属性删除成功');
                  }}
                >
                  删除
                </Button>
              </Space>
            )
          }
        ]}
      />

      <Modal
        title={editingAttribute ? '编辑属性' : '添加属性'}
        open={isAttributeModalVisible}
        onCancel={() => setIsAttributeModalVisible(false)}
        footer={null}
        destroyOnClose
      >
        <Form
          form={attributeForm}
          layout="vertical"
          onFinish={(values) => {
            // 处理属性值
            const attributeData = {
              ...values,
              isVariantAttribute: true // 所有属性都是变体属性
            };

            let newAttributes = [...attributes];

            if (editingAttribute) {
              // 更新属性
              newAttributes = attributes.map(attr =>
                attr.id === editingAttribute.id ? { ...attr, ...attributeData } : attr
              );
              setAttributes(newAttributes);
              message.success('属性更新成功');
            } else {
              // 添加新属性
              const newAttribute = {
                id: Date.now(),
                ...attributeData
              };
              newAttributes = [...attributes, newAttribute];
              setAttributes(newAttributes);
              message.success('属性添加成功');
            }

            setIsAttributeModalVisible(false);

            // 使用更新后的属性列表直接生成变体
            generateVariantsWithAttributes(newAttributes);
          }}
        >
          <Form.Item
            name="name"
            label="属性名称"
            rules={[{ required: true, message: '请输入属性名称' }]}
          >
            <I18nField>
              <Input placeholder="请输入属性名称，例如：颜色、尺寸" />
            </I18nField>
          </Form.Item>

          <Form.Item
            name="code"
            label="属性代码"
            rules={[{ required: true, message: '请输入属性代码' }]}
          >
            <Input placeholder="请输入属性代码，例如：color、size" />
          </Form.Item>


          <Form.Item
            label="属性值"
            name="options"
            rules={[
              { required: true, message: '请添加至少一个属性值' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || value.length <= 1) {
                    return Promise.resolve();
                  }

                  // 检查是否有重复值
                  const valueSet = new Set(value.filter(Boolean).map((v: string) => v.trim()));
                  if (valueSet.size !== value.filter(Boolean).length) {
                    return Promise.reject(new Error('属性值不能重复'));
                  }

                  return Promise.resolve();
                },
              }),
            ]}
          >
            <Form.List name="options">
              {(fields, { add, remove }) => (
                <>
                  {fields.map((field, index) => (
                    <Form.Item
                      key={field.key}
                      style={{ marginBottom: 8 }}
                    >
                      <Input.Group compact>
                        <Form.Item
                          {...field}
                          noStyle
                          validateTrigger={['onChange', 'onBlur']}
                          rules={[
                            {
                              required: true,
                              whitespace: true,
                              message: "请输入属性值或删除此项",
                            },
                          ]}
                        >
                          <Input
                            placeholder="输入属性值"
                            style={{ width: 'calc(100% - 40px)' }}
                          />
                        </Form.Item>
                        <Button
                          type="text"
                          icon={<DeleteOutlined />}
                          onClick={() => remove(field.name)}
                          style={{ width: 40 }}
                        />
                      </Input.Group>
                    </Form.Item>
                  ))}
                  <Form.Item>
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      block
                      icon={<PlusOutlined />}
                    >
                      添加属性值
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
              <Button onClick={() => setIsAttributeModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      <Divider />

      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography.Title level={5} style={{ margin: 0 }}>商品变体列表</Typography.Title>
        <Button
          type="primary"
          onClick={handleGenerateVariants}
        >
          重新生成变体
        </Button>
      </div>

      <Table
        dataSource={variants}
        rowKey="id"
        pagination={false}
        columns={[
          {
            title: 'SKU',
            dataIndex: 'sku',
            key: 'sku',
          },
          {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            render: (name: string) => <I18nText value={name} showAllLanguages={false} />,
          },
          {
            title: '价格',
            dataIndex: 'price',
            key: 'price',
            render: (price) => `¥${price?.toFixed(2) || '0.00'}`
          },
          {
            title: '属性组合',
            dataIndex: 'attributes',
            key: 'attributes',
            render: (attrs) => (
              <div>
                {Object.entries(attrs || {}).map(([key, value]) => {
                  const attr = attributes.find(a => a.code === key);
                  return attr ? (
                    <Tag key={key} color="blue">
                      {attr.name}: {value}
                    </Tag>
                  ) : null;
                })}
              </div>
            )
          },
          {
            title: '状态',
            dataIndex: 'isActive',
            key: 'isActive',
            render: (isActive, record) => (
              <Switch
                checked={isActive}
                checkedChildren="启用"
                unCheckedChildren="停用"
                onChange={(checked) => {
                  const newVariants = variants.map(v =>
                    v.id === record.id ? { ...v, isActive: checked } : v
                  );
                  setVariants(newVariants);
                }}
              />
            )
          },
          {
            title: '操作',
            key: 'action',
            render: (_, record, index) => (
              <Space size="small">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => {
                    setEditingVariant(record);
                    variantForm.setFieldsValue({
                      ...record,
                      price: record.price || 0
                    });
                    setIsVariantModalVisible(true);
                  }}
                >
                  编辑
                </Button>
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => {
                    Modal.confirm({
                      title: '确认删除',
                      content: '确定要删除此变体吗？删除后无法恢复。',
                      okText: '确认',
                      cancelText: '取消',
                      onOk: () => {
                        const newVariants = [...variants];
                        newVariants.splice(index, 1);
                        setVariants(newVariants);
                        message.success('变体删除成功');
                      }
                    });
                  }}
                >
                  删除
                </Button>
              </Space>
            )
          }
        ]}
      />

      {renderVariantModal()}

      <div style={{ marginTop: 16 }}>
        <Button type="primary" onClick={() => form.submit()} loading={false}>
          保存
        </Button>
      </div>
    </>
  );
});

export default ProductAttributesAndVariants;
