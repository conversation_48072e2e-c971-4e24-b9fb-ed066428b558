import http from '@/utils/http';

/**
 * 使用Elasticsearch分页搜索产品
 */
export function apiProductEsSearch(data: TPageQuery) {
  return http.post<TPageListRes<ProductBo>>('/products/es/search', data);
}

/**
 * 在Elasticsearch中索引产品
 */
export function apiProductEsIndex(productId: number | string) {
  return http.post<ProductBo>(`/products/es/index/${productId}`);
}

/**
 * 从Elasticsearch中删除产品
 */
export function apiProductEsDelete(productId: number | string) {
  return http.delete<any>(`/products/es/index/${productId}`);
}

/**
 * 在Elasticsearch中索引多个产品
 */
export function apiProductEsIndexBatch(productIds: (number | string)[]) {
  return http.post<ProductBo[]>('/products/es/index/batch', productIds);
}

/**
 * 在Elasticsearch中索引所有产品
 */
export function apiProductEsIndexAll() {
  return http.post<any>('/products/es/index/all');
}

/**
 * 使用Elasticsearch高级搜索产品
 */
export function apiProductEsAdvancedSearch(data: any, page: number = 1, size: number = 10) {
  return http.post<TPageListRes<ProductBo>>(`/products/es/advanced-search?page=${page}&size=${size}`, data);
}

/**
 * 获取Elasticsearch中的产品数量
 */
export function apiProductEsCount() {
  return http.get<any>('/products/es/count');
}

