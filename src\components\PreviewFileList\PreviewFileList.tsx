import { useMemo, useState } from "react"
import { Image } from "antd"
import { FileUtils } from "@/utils/file";
import { FileOutlined } from "@ant-design/icons";
import { ToolsUtil } from "@/utils/tools";

export interface TPreviewFileListProps {
  type?: 'album' | 'list' | 'button' | 'custom';
  urls: string[];
  width?: number;
  height?: number;
  children?: React.ReactNode;
}

function PreviewFileList(props: TPreviewFileListProps) {
  const [visible, setVisible] = useState(false);
  const [previewIndex, setPreviewIndex] = useState(0)

  const type = useMemo(() => props.type || 'custom', [props.type])
  const width = useMemo(() => props.width || 60, [props.width])
  const height = useMemo(() => props.height || 60, [props.height])

  const files = useMemo(() => FileUtils.urlsToPreviewFiles(props.urls).filter((file) => file.url), [props.urls])


  return (
    <>
      <Image.PreviewGroup
        items={props.urls || []}
        preview={{
          current: previewIndex,
          visible,
          onVisibleChange: (vis) => setVisible(vis),
          onChange: (current) => setPreviewIndex(current),
          imageRender: (node, info) => {
            const index = info.current;
            const file = files[index];
            if (!file) {
              return <div className="text-white">无预览文件</div>
            }
            if (file.type === 'image') {
              return node
            }
            if (file.type === 'video') {
              return <video src={file.url} controls className="max-w-[80vw] max-h-80vh]" />
            }
            return <div className="text-white">此格式无法预览</div>
          },
          toolbarRender: () => null,
        }}></Image.PreviewGroup>
      {files.length === 0 && !props.children ? '-' : null}
      {files.length > 0 && !props.children && type === 'album' ? (
        <div className="flex flex-wrap gap-1">
          {files.map((file, index) => (<div
            key={index}
            className="p-1 bg-white rounded-md flex items-center justify-center"
            style={{
              border: '1px solid #ccc',
              width,
              height,
            }}
            onClick={() => {
              setPreviewIndex(index)
              setVisible(true)
            }}
          >
            {['image', 'video'].includes(file.type) ? <img
              src={file.previewUrl}
              className="max-w-full max-h-full"
            /> : <FileOutlined />}
          </div>))}
        </div>
      ) : null}
      {files.length > 0 && !props.children && type === 'list' ? (
        <div className="w-full max-w-[320px]">
          {files.map((file, index) => (<div
            className="text-[12px] flex w-full py-1 px-2 gap-1 rounded-1 hover:bg-gray-100"
            key={index}
          >
            <div className="flex-1 text-gray-600">{file.name}</div>
            <a className="flex-shrink-0"
              onClick={() => {
                setPreviewIndex(index)
                setVisible(true)
              }}>预览</a>
            <a className="flex-shrink-0" onClick={() => ToolsUtil.downloadFile(file.url)}>下载</a>
          </div>))}
        </div>
      ) : null}
      {files.length > 0 && !props.children && type === 'button' ? (
        <a onClick={() => setVisible(true)}>查看({files.length})</a>
      ) : null}
      <div onClick={() => setVisible(true)}>
        {props.children ? props.children : null}
      </div>

    </>

  )
}

export default PreviewFileList
