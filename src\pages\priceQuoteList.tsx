import React, { useRef, useState } from 'react';
import { Form, Modal, message, Space, Select, Tooltip, Tag, Descriptions } from 'antd';
import { EyeOutlined, CheckOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import {
  apiPriceQuoteGetAll,
  apiPriceQuoteGetDetails,
  apiPriceQuoteUpdateStatus,
} from '@/apis/apis.api';
import { ToolsUtil } from '@/utils/tools';
import dayjs from 'dayjs';
import ClientUserObj from '@/components/ClientUserObj';

// Quote type options
const QUOTE_TYPE_OPTIONS = [
  { label: '买家报价', value: 'BUYER_QUOTE' },
  { label: '卖家报价', value: 'SELLER_OFFER' }
];

// Quote status options
const QUOTE_STATUS_OPTIONS = [
  { label: '活跃', value: 'ACTIVE' },
  { label: '已过期', value: 'EXPIRED' },
  { label: '已接受', value: 'ACCEPTED' },
  { label: '已拒绝', value: 'REJECTED' },
  { label: '已还价', value: 'COUNTERED' },
  { label: '已取消', value: 'CANCELLED' }
];

// Quote status tag colors
const QUOTE_STATUS_COLORS = {
  'ACTIVE': 'green',
  'EXPIRED': 'orange',
  'ACCEPTED': 'blue',
  'REJECTED': 'red',
  'COUNTERED': 'purple',
  'CANCELLED': 'gray'
};

// Price Quote Management Component
const PriceQuoteList: React.FC & { auth?: string[] } = () => {
  const [statusForm] = Form.useForm();
  const tableRef = useRef<ActionType>();

  // State for modals
  const [isQuoteDetailModalVisible, setIsQuoteDetailModalVisible] = useState(false);
  const [isStatusModalVisible, setIsStatusModalVisible] = useState(false);

  // Current quote being viewed/edited
  const [currentQuote, setCurrentQuote] = useState<PriceQuote | null>(null);

  // Handle viewing quote details
  const handleViewDetails = useMemoizedFn(async (quoteId: number) => {
    try {
      const quoteDetails = await apiPriceQuoteGetDetails(quoteId);
      setCurrentQuote(quoteDetails);
      setIsQuoteDetailModalVisible(true);
    } catch (error) {
      console.error('获取报价详情失败:', error);
      message.error('获取报价详情失败');
    }
  });

  // Open modal for updating quote status
  const handleOpenStatusModal = useMemoizedFn((record: PriceQuote) => {
    setCurrentQuote(record);
    statusForm.setFieldsValue({
      status: record.status
    });
    setIsStatusModalVisible(true);
  });

  // Handle updating quote status
  const handleUpdateStatus = useMemoizedFn(async () => {
    try {
      if (!currentQuote) return;

      const values = await statusForm.validateFields();
      await apiPriceQuoteUpdateStatus(currentQuote.id, values.status);

      message.success('报价状态更新成功');
      setIsStatusModalVisible(false);
      tableRef.current?.reload();
    } catch (error) {
      console.error('更新报价状态失败:', error);
      message.error('更新报价状态失败');
    }
  });



  // Check if a quote is expired
  const isQuoteExpired = (expirationTime: string) => {
    return dayjs(expirationTime).isBefore(dayjs());
  };

  // Table columns
  const columns: ProColumns<PriceQuote>[] = [
    {
      title: '报价类型',
      dataIndex: 'quoteType',
      key: 'quoteType',
      valueEnum: QUOTE_TYPE_OPTIONS.reduce((acc, option) => {
        acc[option.value] = { text: option.label };
        return acc;
      }, {} as Record<string, { text: string }>),
      render: (_, record) => (
        <Tag color={record.quoteType === 'BUYER_QUOTE' ? 'blue' : 'green'}>
          {QUOTE_TYPE_OPTIONS.find(option => option.value === record.quoteType)?.label || record.quoteType}
        </Tag>
      ),
    },
    {
      title: '用户信息',
      dataIndex: 'userId',
      key: 'userId',
      render: (_text, record) => record.userInfo ? <ClientUserObj user={record.userInfo} /> : record.userId
    },
    {
      title: '产品ID',
      dataIndex: 'productId',
      key: 'productId',
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      hideInSearch: true,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      hideInSearch: true,
      render: (_, record) => `${record.price} ${record.currencyCode}`,
    },
    {
      title: '美元价格',
      dataIndex: 'usdPrice',
      key: 'usdPrice',
      hideInSearch: true,
      render: (_, record) => `$${record.usdPrice}`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      valueEnum: QUOTE_STATUS_OPTIONS.reduce((acc, option) => {
        acc[option.value] = { text: option.label };
        return acc;
      }, {} as Record<string, { text: string }>),
      render: (_, record) => (
        <Tag color={QUOTE_STATUS_COLORS[record.status] || 'default'}>
          {QUOTE_STATUS_OPTIONS.find(option => option.value === record.status)?.label || record.status}
        </Tag>
      ),
    },
    {
      title: '过期时间',
      dataIndex: 'expirationTime',
      key: 'expirationTime',
      valueType: 'dateTime',
      hideInSearch: true,
      render: (_, record) => {
        const expired = isQuoteExpired(record.expirationTime);
        return (
          <span style={{ color: expired ? 'red' : 'inherit' }}>
            {dayjs(record.expirationTime).format('YYYY-MM-DD HH:mm:ss')}
            {expired && ' (已过期)'}
          </span>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
    },
    {
      title: '操作',
      key: 'actions',
      valueType: 'option',
      render: (_, record) => {

        return (
          <Space size="middle">
            <a onClick={() => handleViewDetails(record.id)}>
              <Tooltip title="查看详情">
                <EyeOutlined />
              </Tooltip>
            </a>
            <a onClick={() => handleOpenStatusModal(record)}>更新状态</a>
            
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <ProTable<PriceQuote>
        actionRef={tableRef}
        headerTitle="报价管理"
        rowKey="id"
        columns={columns}
        search={{
          defaultCollapsed: false,
        }}

        request={async (params, sorter) => {
          const filters: any[] = [];

          // 添加搜索条件
          if (params.quoteType) {
            filters.push({ field: 'quoteType', operator: 'eq', value: params.quoteType });
          }
          if (params.userId) {
            filters.push({ field: 'userId', operator: 'eq', value: params.userId });
          }
          if (params.productId) {
            filters.push({ field: 'productId', operator: 'eq', value: params.productId });
          }
          if (params.status) {
            filters.push({ field: 'status', operator: 'eq', value: params.status });
          }

          let sorts = ToolsUtil.tableSorterToPageListReqSort(sorter);

          // 添加默认排序，创建时间倒序
          if (!sorts || sorts.length === 0) {
            sorts = [{ field: 'createdAt', direction: 'DESC' }];
          }

          try {
            const res = await apiPriceQuoteGetAll({
              current: params.current - 1,
              size: params.pageSize,
              filters,
              sorts,
            });

            return {
              total: res.total || 0,
              data: res.records || [],
              success: true,
            };
          } catch (error) {
            console.error('获取报价列表失败:', error);
            message.error('获取报价列表失败');
            return {
              total: 0,
              data: [],
              success: false,
            };
          }
        }}
      />

      {/* Quote Detail Modal */}
      <Modal
        title="报价详情"
        open={isQuoteDetailModalVisible}
        onCancel={() => setIsQuoteDetailModalVisible(false)}
        width={800}
        footer={null}
      >
        {currentQuote && (
          <Descriptions bordered column={2}>
            <Descriptions.Item label="报价类型">
              <Tag color={currentQuote.quoteType === 'BUYER_QUOTE' ? 'blue' : 'green'}>
                {QUOTE_TYPE_OPTIONS.find(option => option.value === currentQuote.quoteType)?.label || currentQuote.quoteType}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="用户信息">
              {currentQuote.userInfo ?
                <ClientUserObj user={currentQuote.userInfo} /> :
                currentQuote.userId
              }
            </Descriptions.Item>
            <Descriptions.Item label="产品ID">{currentQuote.productId}</Descriptions.Item>
            <Descriptions.Item label="产品变体ID">{currentQuote.productVariantId}</Descriptions.Item>
            <Descriptions.Item label="数量">{currentQuote.quantity}</Descriptions.Item>
            <Descriptions.Item label="价格">{`${currentQuote.price} ${currentQuote.currencyCode}`}</Descriptions.Item>
            <Descriptions.Item label="美元价格">{`$${currentQuote.usdPrice}`}</Descriptions.Item>
            <Descriptions.Item label="状态">
              <Tag color={QUOTE_STATUS_COLORS[currentQuote.status] || 'default'}>
                {QUOTE_STATUS_OPTIONS.find(option => option.value === currentQuote.status)?.label || currentQuote.status}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="过期时间">
              {dayjs(currentQuote.expirationTime).format('YYYY-MM-DD HH:mm:ss')}
              {isQuoteExpired(currentQuote.expirationTime) && ' (已过期)'}
            </Descriptions.Item>
            <Descriptions.Item label="订单ID">{currentQuote.orderId || '无'}</Descriptions.Item>
            <Descriptions.Item label="关联报价ID">{currentQuote.relatedQuoteId || '无'}</Descriptions.Item>
            <Descriptions.Item label="创建者">{currentQuote.createdBy}</Descriptions.Item>
            <Descriptions.Item label="创建时间">{dayjs(currentQuote.createdAt).format('YYYY-MM-DD HH:mm:ss')}</Descriptions.Item>
            <Descriptions.Item label="更新者">{currentQuote.updatedBy}</Descriptions.Item>
            <Descriptions.Item label="更新时间">{dayjs(currentQuote.updatedAt).format('YYYY-MM-DD HH:mm:ss')}</Descriptions.Item>
          </Descriptions>
        )}
      </Modal>

      {/* Update Quote Status Modal */}
      <Modal
        title="更新报价状态"
        open={isStatusModalVisible}
        onOk={handleUpdateStatus}
        onCancel={() => setIsStatusModalVisible(false)}
      >
        <Form
          form={statusForm}
          layout="vertical"
        >
          <Form.Item
            name="status"
            label="报价状态"
            rules={[{ required: true, message: '请选择报价状态' }]}
          >
            <Select
              placeholder="请选择报价状态"
              options={QUOTE_STATUS_OPTIONS}
            />
          </Form.Item>
        </Form>
      </Modal>


    </>
  );
};

export default PriceQuoteList;

PriceQuoteList.auth = ['apiPriceQuoteGetAll', 'apiPriceQuoteGetDetails', 'apiPriceQuoteUpdateStatus'];
