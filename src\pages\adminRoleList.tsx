import { apiAdminRoleCreate, apiAdminRolePageList, apiAdminRolePermissions, apiAdminRoleUpdate, apiAssignPermissionsToRoleByCodes } from '@/apis/apis.api';
import AdminPermissionModal from '@/components/AdminPermissionModal';
import { useFormModal } from '@/components/FormilyModal';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { App, Button, Space } from 'antd';
import { useRef, useState } from 'react';

type TEditFormData = {
  name: string;
  code: string;
  description: string;
};

function AdminRoleList() {
  const tableRef = useRef<ActionType>();
  const { message } = App.useApp();

  const [permissionModalValue, setPermissionModalValue] = useState<string[]>([]);
  const [permissionModalId, setPermissionModalId] = useState<string>();
  const [permissionModalOpen, setPermissionModalOpen] = useState(false);

  const showEditPermisssionsModal = useMemoizedFn(async (id: string) => {
    const res = await apiAdminRolePermissions(id);
    setPermissionModalValue(res);
    setPermissionModalOpen(true);
    setPermissionModalId(id);
  });

  const submitPermission = useMemoizedFn(async (permissionCodes: string[]) => {
    await apiAssignPermissionsToRoleByCodes(permissionModalId, permissionCodes);
    setPermissionModalOpen(false);
    message.success('提交成功');
  });

  const { formModalRef: editFormModalRef, formModalHolder: editFormModalHolder } =
    useFormModal<TEditFormData>();

  const showEditModal = useMemoizedFn((record?: TAdminRole) => {
    editFormModalRef.current?.show({
      modalTitle: '修改',
      modalWidth: 1000,
      onAutoSubmit: async (values) => {

        if(record.id) {
          await apiAdminRoleUpdate(record.id, values);
        } else {
          await apiAdminRoleCreate(values);
        }

        message.success('提交成功');
        tableRef.current?.reload();
      },
      initialValues: record
        ? {
            ...record,
          }
        : {},
      schema: {
        type: 'object',
        properties: {
          name: {
            title: '名称',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          code: {
            title: '编码',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          home: {
            title: '首页',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          description: {
            title: '描述',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
          },
        },
      },
    });
  });

  return (
    <div>
      <ProTable<TAdminRole>
        actionRef={tableRef}
        headerTitle='后台角色列表'
        rowKey='id'
        toolBarRender={() => [
          <Button key='add' type='primary' onClick={() => showEditModal()}>
            新增
          </Button>,
        ]}
        search={{
          defaultCollapsed: false,
        }}
        columns={[
          { title: '名称', dataIndex: 'name' },
          { title: '编码', dataIndex: 'code' },
          { title: '首页', dataIndex: 'home' },
          {
            title: '操作',
            valueType: 'option',
            key: 'option',
            render: (_text, record) => {
              return (
                <Space>
                  <a onClick={() => showEditModal(record)}>修改</a>
                  <a onClick={() => showEditPermisssionsModal(record.id)}>权限</a>
                </Space>
              );
            },
          },
        ]}
        request={async (params, sorter) => {
          const filters: TQueryFilter[] = [];

          if (params.name) {
            filters.push({ field: 'name', operator: 'like', value: params.name });
          }
          if (params.code) {
            filters.push({ field: 'code', operator: 'eq', value: params.code });
          }


          const res = await apiAdminRolePageList({
            current: params.current - 1,
            size: params.pageSize,
            filters,
            sorts: [],
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}
      />
      {editFormModalHolder}
      <AdminPermissionModal
        open={permissionModalOpen}
        onClose={() => setPermissionModalOpen(false)}
        value={permissionModalValue}
        onChange={submitPermission}
      />
    </div>
  );
}

export default AdminRoleList;

AdminRoleList.auth = ['apiAdminRoleCreate', 'apiAdminRolePageList', 'apiAdminRolePermissions', 'apiAdminRoleUpdate', 'apiAssignPermissionsToRoleByCodes'];
