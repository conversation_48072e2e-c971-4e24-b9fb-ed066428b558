// 此文件由 genApis 脚本自动生成，请勿手动修改
// 生成时间: 2025-08-03T05:03:02.068Z

/**
 * @description AdminPermissionBo
 */
interface TAdminPermissionBo {
  /**  */
  id: string;
  /**  */
  name: string;
  /**  */
  code: string;
  /**  */
  description: string;
  /**  */
  enabled: boolean;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
}

/**
 * @description AdminRoleBo
 */
interface TAdminRoleBo {
  /**  */
  id: string;
  /**  */
  name: string;
  /**  */
  code: string;
  /**  */
  description: string;
  /**  */
  home: string;
  /**  */
  enabled: boolean;
  /**  */
  permissions: TAdminPermissionBo[];
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
}

/**
 * @description AdminUserBo
 */
interface TAdminUserBo {
  /**  */
  id: string;
  /**  */
  username: string;
  /**  */
  realname: string;
  /**  */
  email: string;
  /**  */
  phone: string;
  /**  */
  password: string;
  /**  */
  avatar: string;
  /**  */
  nickname: string;
  /**  */
  lastLoginAt: TLocalDateTime;
  /**  */
  lastLoginIp: string;
  /**  */
  roles: string[];
  /**  */
  permissions: string[];
  /**  */
  enabled: boolean;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
}

/**
 * @description ApiApplicationBO
 */
interface TApiApplicationBO {
  /**  */
  id: string;
  /**  */
  appId: string;
  /**  */
  appSecret: string;
  /**  */
  userId: string;
  /**  */
  appName: string;
  /**  */
  description: string;
  /**  */
  ipWhitelist: string;
  /**  */
  expiresAt: TLocalDateTime;
  /**  */
  remarks: string;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
}

/**
 * @description ApiKeyResponse
 */
interface TApiKeyResponse {
  /**  */
  id: string;
  /**  */
  appId: string;
  /**  */
  appSecret: string;
  /**  */
  appName: string;
  /**  */
  description: string;
  /**  */
  ipWhitelist: string;
  /**  */
  expiresAt: TLocalDateTime;
  /**  */
  remarks: string;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
}

/**
 * @description ApiV1Request
 */
interface TApiV1Request {
  /**  */
  version: string;
  /**  */
  appId: string;
  /**  */
  nonce: string;
  /**  */
  signature: string;
  /**  */
  timestamp: string;
  /**  */
  data: string;
}

type TArticleTypeEnum = "0" | "1" | "2" | "3" | "4" | "5" | "6";

/**
 * @description BatchAssignBusinessTypeBO
 */
interface TBatchAssignBusinessTypeBO {
  /**  */
  ids: string[];
  /**  */
  businessTypeId: string;
}

/**
 * @description BatchAssignLocationBO
 */
interface TBatchAssignLocationBO {
  /**  */
  ids: string[];
  /**  */
  locationId: string;
}

/**
 * @description BatchOperationResultDTO
 */
interface TBatchOperationResultDTO {
  /**  */
  success: boolean;
  /**  */
  message: string;
  /**  */
  updatedCount: number;
}

/**
 * @description BatchToggleEnabledBO
 */
interface TBatchToggleEnabledBO {
  /**  */
  ids: string[];
  /**  */
  enabled: TIsEnum;
}

/**
 * @description BatchUnassignBO
 */
interface TBatchUnassignBO {
  /**  */
  ids: string[];
}

/**
 * @description ChangePasswordRequest
 */
interface TChangePasswordRequest {
  /**  */
  oldPassword: string;
  /**  */
  newPassword: string;
}

/**
 * @description ChannelBusinessTypeMappingDTO
 */
interface TChannelBusinessTypeMappingDTO {
  /**  */
  id: string;
  /**  */
  channelType: TChannelType;
  /**  */
  externalCode: string;
  /**  */
  businessTypeId: string;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
  /**  */
  businessType: TStaticBusinessTypeDTO;
}

/**
 * @description ChannelDTO
 */
interface TChannelDTO {
  /**  */
  id: string;
  /**  */
  code: string;
  /**  */
  name: string;
  /**  */
  description: string;
  /**  */
  sourceType: TChannelType;
  /**  */
  baseUrl: string;
  /**  */
  configJson: string;
  /**  */
  enabled: boolean;
  /**  */
  priority: number;
  /**  */
  dailyRequestLimit: string;
  /**  */
  currentRequestCount: string;
  /**  */
  supportedProxyTypes: string;
  /**  */
  extra: string;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
}

/**
 * @description ChannelInfoBO
 */
interface TChannelInfoBO {
  /**  */
  code: string;
  /**  */
  name: string;
  /**  */
  description: string;
  /**  */
  sourceType: TChannelType;
  /**  */
  baseUrl: string;
  /**  */
  configJson: string;
  /**  */
  enabled: boolean;
  /**  */
  priority: number;
  /**  */
  dailyRequestLimit: string;
  /**  */
  currentRequestCount: string;
  /**  */
  supportedProxyTypes: string;
  /**  */
  extra: string;
}

/**
 * @description ChannelLocationMappingDTO
 */
interface TChannelLocationMappingDTO {
  /**  */
  id: string;
  /**  */
  channelType: TChannelType;
  /**  */
  externalCode: string;
  /**  */
  locationId: string;
  /**  */
  externalData: string;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
  /**  */
  location: TLocationDTO;
}

type TChannelType = "0" | "1";

/**
 * @description ClientLoginRequest
 */
interface TClientLoginRequest {
  /**  */
  username: string;
  /**  */
  password: string;
}

/**
 * @description ClientRegisterRequest
 */
interface TClientRegisterRequest {
  /**  */
  username: string;
  /**  */
  password: string;
  /**  */
  email: string;
  /**  */
  verificationCode: string;
  /**  */
  phone: string;
  /**  */
  nickname: string;
}

/**
 * @description ClientUserBO
 */
interface TClientUserBO {
  /**  */
  roles: string[];
  /**  */
  permissions: string[];
  /**  */
  id: string;
  /**  */
  username: string;
  /**  */
  email: string;
  /**  */
  password: string;
  /**  */
  nickname: string;
  /**  */
  avatar: string;
  /**  */
  lastLoginAt: TLocalDateTime;
  /**  */
  lastLoginIp: string;
  /**  */
  phoneVerifiedAt: TLocalDateTime;
  /**  */
  remarks: string;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
}

/**
 * @description ClientUserTransactionBO
 */
interface TClientUserTransactionBO {
  /**  */
  id: string;
  /**  */
  transactionNo: string;
  /**  */
  externalTransactionNo: string;
  /**  */
  userId: string;
  /**  */
  transactionType: string;
  /**  */
  status: string;
  /**  */
  amount: number;
  /**  */
  currency: string;
  /**  */
  balanceBefore: number;
  /**  */
  balanceAfter: number;
  /**  */
  transactionTime: TLocalDateTime;
  /**  */
  completedAt: TLocalDateTime;
  /**  */
  failedAt: TLocalDateTime;
  /**  */
  relatedOrderId: string;
  /**  */
  relatedOrderNo: string;
  /**  */
  paymentMethod: string;
  /**  */
  paymentChannel: string;
  /**  */
  paymentTransactionNo: string;
  /**  */
  description: string;
  /**  */
  transactionDetails: string;
  /**  */
  fee: number;
  /**  */
  feeCurrency: string;
  /**  */
  exchangeRate: number;
  /**  */
  originalCurrency: string;
  /**  */
  originalAmount: number;
  /**  */
  failureReason: string;
  /**  */
  retryCount: number;
  /**  */
  maxRetryCount: number;
  /**  */
  nextRetryAt: TLocalDateTime;
  /**  */
  clientIp: string;
  /**  */
  userAgent: string;
  /**  */
  deviceId: string;
  /**  */
  sessionId: string;
  /**  */
  needsApproval: boolean;
  /**  */
  approvalStatus: string;
  /**  */
  approvedBy: string;
  /**  */
  approvedAt: TLocalDateTime;
  /**  */
  approvalRemarks: string;
  /**  */
  remarks: string;
  /**  */
  extraData: string;
  /**  */
  isTest: boolean;
  /**  */
  source: string;
  /**  */
  operatorId: string;
  /**  */
  operatorName: string;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
}

/**
 * @description ClientUserWalletBO
 */
interface TClientUserWalletBO {
  /**  */
  id: string;
  /**  */
  userId: string;
  /**  */
  balance: number;
  /**  */
  frozenAmount: number;
  /**  */
  currency: string;
  /**  */
  enabled: TIsEnum;
  /**  */
  type: string;
  /**  */
  lastTransactionTime: string;
  /**  */
  note: string;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
  /**  */
  availableBalance: number;
}

/**
 * @description ClientUserWalletDTO
 */
interface TClientUserWalletDTO {
  /**  */
  id: string;
  /**  */
  userId: string;
  /**  */
  balance: number;
  /**  */
  frozenAmount: number;
  /**  */
  currency: string;
  /**  */
  enabled: TIsEnum;
  /**  */
  type: string;
  /**  */
  lastTransactionTime: string;
  /**  */
  note: string;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
  /**  */
  availableBalance: number;
}

/**
 * @description CreateAdminPermissionRequest
 */
interface TCreateAdminPermissionRequest {
  /**  */
  name: string;
  /**  */
  code: string;
  /**  */
  description: string;
}

/**
 * @description CreateAdminRoleRequest
 */
interface TCreateAdminRoleRequest {
  /**  */
  name: string;
  /**  */
  code: string;
  /**  */
  description: string;
  /**  */
  home: string;
  /**  */
  permissionIds: string[];
  /**  */
  permissionCodes: string[];
}

/**
 * @description CreateAdminUserRequest
 */
interface TCreateAdminUserRequest {
  /**  */
  username: string;
  /**  */
  password: string;
  /**  */
  email: string;
  /**  */
  phone: string;
  /**  */
  nickname: string;
  /**  */
  roleIds: string[];
  /**  */
  roleCodes: string[];
}

/**
 * @description CreateApiApplicationRequest
 */
interface TCreateApiApplicationRequest {
  /**  */
  userId: string;
  /**  */
  appName: string;
  /**  */
  ipWhitelist: string;
  /**  */
  expiresAt: TLocalDateTime;
  /**  */
  remarks: string;
}

/**
 * @description CreateApiKeyRequest
 */
interface TCreateApiKeyRequest {
  /**  */
  appName: string;
  /**  */
  description: string;
  /**  */
  ipWhitelist: string;
  /**  */
  expiresAt: TLocalDateTime;
  /**  */
  remarks: string;
}

/**
 * @description CreateClientUserRequest
 */
interface TCreateClientUserRequest {
  /**  */
  username: string;
  /**  */
  password: string;
  /**  */
  email: string;
  /**  */
  region: string;
  /**  */
  language: string;
  /**  */
  currency: string;
  /**  */
  phone: string;
  /**  */
  nickname: string;
  /**  */
  avatar: string;
  /**  */
  remarks: string;
}

/**
 * @description CreateClientUserTransactionRequest
 */
interface TCreateClientUserTransactionRequest {
  /**  */
  transactionNo: string;
  /**  */
  externalTransactionNo: string;
  /**  */
  userId: string;
  /**  */
  transactionType: string;
  /**  */
  status: string;
  /**  */
  amount: number;
  /**  */
  currency: string;
  /**  */
  balanceBefore: number;
  /**  */
  balanceAfter: number;
  /**  */
  transactionTime: TLocalDateTime;
  /**  */
  relatedOrderId: string;
  /**  */
  relatedOrderNo: string;
  /**  */
  paymentMethod: string;
  /**  */
  paymentChannel: string;
  /**  */
  paymentTransactionNo: string;
  /**  */
  description: string;
  /**  */
  transactionDetails: string;
  /**  */
  fee: number;
  /**  */
  feeCurrency: string;
  /**  */
  exchangeRate: number;
  /**  */
  originalCurrency: string;
  /**  */
  originalAmount: number;
  /**  */
  clientIp: string;
  /**  */
  userAgent: string;
  /**  */
  deviceId: string;
  /**  */
  sessionId: string;
  /**  */
  needsApproval: boolean;
  /**  */
  remarks: string;
  /**  */
  extraData: string;
  /**  */
  isTest: boolean;
  /**  */
  source: string;
  /**  */
  operatorId: string;
  /**  */
  operatorName: string;
}

/**
 * @description CreateClientUserWalletRequest
 */
interface TCreateClientUserWalletRequest {
  /**  */
  userId: string;
  /**  */
  balance: number;
  /**  */
  frozenAmount: number;
  /**  */
  currency: string;
  /**  */
  type: string;
  /**  */
  note: string;
}

/**
 * @description CreateOrderDetailRequest
 */
interface TCreateOrderDetailRequest {
  /**  */
  locationId: string;
  /**  */
  businessTypeId: string;
  /**  */
  tagId: string;
  /**  */
  ispType: TIspTypeEnum;
  /**  */
  quantity: number;
  /**  */
  duration: number;
  /**  */
  remarks: string;
}

/**
 * @description CreateOrderRequest
 */
interface TCreateOrderRequest {
  /**  */
  userId: string;
  /**  */
  orderType: TOrderTypeEnum;
  /**  */
  orderDetails: TCreateOrderDetailRequest[];
  /**  */
  discountCode: string;
  /**  */
  autoRenewal: boolean;
  /**  */
  renewalDaysBefore: number;
  /**  */
  remarks: string;
  /** 支付方式 */
  paymentType: TPaymentTypeEnum;
}

/**
 * @description CreateOrderResponse
 */
interface TCreateOrderResponse {
  /**  */
  orderId: string;
  /**  */
  orderNo: string;
  /**  */
  userId: string;
  /**  */
  orderType: TOrderTypeEnum;
  /**  */
  status: TOrderStatusEnum;
  /**  */
  totalPrice: number;
  /**  */
  discountAmount: number;
  /**  */
  payAmount: number;
  /**  */
  currency: string;
  /**  */
  orderTime: TLocalDateTime;
  /**  */
  instances: TStaticInstanceDTO[];
}

/**
 * @description CreateStaticInstanceRequest
 */
interface TCreateStaticInstanceRequest {
  /**  */
  lineId: string;
  /**  */
  proxyId: string;
  /**  */
  businessTypeId: string;
  /**  */
  orderNo: string;
  /**  */
  userId: string;
  /**  */
  ip: string;
  /**  */
  port: number;
  /**  */
  username: string;
  /**  */
  password: string;
  /**  */
  expiresAt: TLocalDateTime;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  activatedAt: TLocalDateTime;
  /**  */
  status: TStaticProxyInstanceStatusEnum;
  /**  */
  channelId: string;
  /**  */
  thirdProxyId: string;
  /**  */
  config: string;
  /**  */
  remarks: string;
  /**  */
  trafficLimit: string;
  /**  */
  concurrentLimit: number;
}

/**
 * @description CreateStaticOrderRequest
 */
interface TCreateStaticOrderRequest {
  /**  */
  userId: string;
  /**  */
  orderNo: string;
  /**  */
  thirdOrderNo: string;
  /**  */
  orderType: TOrderTypeEnum;
  /**  */
  quantity: number;
  /**  */
  duration: number;
  /**  */
  unitPrice: number;
  /**  */
  totalPrice: number;
  /**  */
  currency: string;
  /**  */
  proxyType: string;
  /**  */
  businessName: string;
  /**  */
  orderTime: TLocalDateTime;
  /**  */
  proxyStartTime: TLocalDateTime;
  /**  */
  proxyEndTime: TLocalDateTime;
  /**  */
  orderDetails: string;
  /**  */
  purchaseParams: string;
  /**  */
  remarks: string;
  /**  */
  autoRenewal: boolean;
  /**  */
  renewalDaysBefore: number;
  /**  */
  discountAmount: number;
  /**  */
  discountCode: string;
  /**  */
  actualPayAmount: number;
  /**  */
  paymentMethod: string;
}

type TIsEnum = "FALSE" | "TRUE";

type TIspTypeEnum = "0" | "1" | "2";

/**
 * @description LineHealthDTO
 */
interface TLineHealthDTO {
  /**  */
  id: string;
  /**  */
  usageRate: number;
  /**  */
  healthStatus: string;
  /**  */
  enabled: boolean;
  /**  */
  status: TStaticProxyLineStatusEnum;
  /**  */
  totalQuantity: number;
  /**  */
  usedQuantity: number;
  /**  */
  reservedQuantity: number;
  /**  */
  availableQuantity: number;
}

/**
 * @description LocationBO
 */
interface TLocationBO {
  /**  */
  id: string;
  /**  */
  parentId: string;
  /**  */
  code: string;
  /**  */
  name: string;
  /**  */
  level: TLocationLevelEnum;
  /**  */
  iso2Code: string;
  /**  */
  iso3Code: string;
  /**  */
  numericCode: string;
  /**  */
  sortOrder: number;
}

/**
 * @description LocationDTO
 */
interface TLocationDTO {
  /**  */
  id: string;
  /**  */
  parentId: string;
  /**  */
  code: string;
  /**  */
  name: string;
  /**  */
  level: TLocationLevelEnum;
  /**  */
  sortOrder: number;
}

type TLocationLevelEnum = "CONTINENT" | "COUNTRY" | "STATE_PROVINCE" | "CITY";

/**
 * @description 地区查询请求参数
 */
interface TLocationQueryRequest {
  /** 地区级别 */
  level: TLocationLevelEnum;
  /** 父级地区ID */
  parentId: string;
}

/**
 * @description LocationTreeNodeDTO
 */
interface TLocationTreeNodeDTO {
  /**  */
  id: string;
  /**  */
  code: string;
  /**  */
  name: string;
  /**  */
  level: TLocationLevelEnum;
  /**  */
  parentId: string;
  /**  */
  sortOrder: number;
  /**  */
  availableCount: number;
  /**  */
  minPrice: number;
  /**  */
  maxPrice: number;
  /**  */
  avgPrice: number;
  /**  */
  currency: string;
  /**  */
  children: TLocationTreeNodeDTO[];
  /**  */
  hasChildren: boolean;
  /**  */
  depth: number;
}

/**
 * @description OrderDetailTrialRequest
 */
interface TOrderDetailTrialRequest {
  /**  */
  locationId: string;
  /**  */
  businessTypeId: string;
  /**  */
  tagId: string;
  /**  */
  ispType: TIspTypeEnum;
  /**  */
  quantity: number;
  /**  */
  duration: number;
}

/**
 * @description OrderDetailTrialResult
 */
interface TOrderDetailTrialResult {
  /**  */
  locationId: string;
  /**  */
  locationName: string;
  /**  */
  businessTypeId: string;
  /**  */
  businessTypeName: string;
  /**  */
  tagId: string;
  /**  */
  tagName: string;
  /**  */
  ispType: TIspTypeEnum;
  /**  */
  quantity: number;
  /**  */
  duration: number;
  /**  */
  unitPrice: number;
  /**  */
  subtotal: number;
  /**  */
  currency: string;
  /**  */
  available: boolean;
  /**  */
  unavailableReason: string;
}

type TOrderStatusEnum = "PENDING" | "PAID" | "PROCESSING" | "COMPLETED" | "FAILED" | "CANCELLED" | "REFUNDED";

/**
 * @description OrderTrialCalculationRequest
 */
interface TOrderTrialCalculationRequest {
  /**  */
  userId: string;
  /**  */
  orderDetails: TOrderDetailTrialRequest[];
  /**  */
  discountCode: string;
}

/**
 * @description OrderTrialCalculationResponse
 */
interface TOrderTrialCalculationResponse {
  /**  */
  userId: string;
  /**  */
  orderDetails: TOrderDetailTrialResult[];
  /**  */
  subtotalAmount: number;
  /**  */
  discountAmount: number;
  /**  */
  totalAmount: number;
  /**  */
  currency: string;
  /**  */
  discountCode: string;
  /**  */
  discountDescription: string;
}

type TOrderTypeEnum = "NONE" | "PURCHASE" | "RENEWAL";

/**
 * @description PageQuery
 */
interface TPageQuery {
  /**  */
  filters: TQueryFilter[];
  /**  */
  sorts: TQuerySort[];
  /**  */
  distinct: boolean;
  /**  */
  distinctField: string;
  /**  */
  current: number;
  /**  */
  size: number;
  /**  */
  offset: number;
}

/**
 * @description PageResultAdminPermissionBo
 */
interface TPageResultAdminPermissionBo {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TAdminPermissionBo[];
  /**  */
  offset: number;
}

/**
 * @description PageResultAdminRoleBo
 */
interface TPageResultAdminRoleBo {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TAdminRoleBo[];
  /**  */
  offset: number;
}

/**
 * @description PageResultAdminUserBo
 */
interface TPageResultAdminUserBo {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TAdminUserBo[];
  /**  */
  offset: number;
}

/**
 * @description PageResultApiApplicationBO
 */
interface TPageResultApiApplicationBO {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TApiApplicationBO[];
  /**  */
  offset: number;
}

/**
 * @description PageResultChannelBusinessTypeMappingDTO
 */
interface TPageResultChannelBusinessTypeMappingDTO {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TChannelBusinessTypeMappingDTO[];
  /**  */
  offset: number;
}

/**
 * @description PageResultChannelDTO
 */
interface TPageResultChannelDTO {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TChannelDTO[];
  /**  */
  offset: number;
}

/**
 * @description PageResultChannelLocationMappingDTO
 */
interface TPageResultChannelLocationMappingDTO {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TChannelLocationMappingDTO[];
  /**  */
  offset: number;
}

/**
 * @description PageResultClientUserBO
 */
interface TPageResultClientUserBO {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TClientUserBO[];
  /**  */
  offset: number;
}

/**
 * @description PageResultClientUserTransactionBO
 */
interface TPageResultClientUserTransactionBO {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TClientUserTransactionBO[];
  /**  */
  offset: number;
}

/**
 * @description PageResultClientUserWalletBO
 */
interface TPageResultClientUserWalletBO {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TClientUserWalletBO[];
  /**  */
  offset: number;
}

/**
 * @description PageResultLocationDTO
 */
interface TPageResultLocationDTO {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TLocationDTO[];
  /**  */
  offset: number;
}

/**
 * @description PageResultStaticBusinessTypeDTO
 */
interface TPageResultStaticBusinessTypeDTO {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TStaticBusinessTypeDTO[];
  /**  */
  offset: number;
}

/**
 * @description PageResultStaticInstanceBO
 */
interface TPageResultStaticInstanceBO {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TStaticInstanceBO[];
  /**  */
  offset: number;
}

/**
 * @description PageResultStaticInstanceDTO
 */
interface TPageResultStaticInstanceDTO {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TStaticInstanceDTO[];
  /**  */
  offset: number;
}

/**
 * @description PageResultStaticLineDTO
 */
interface TPageResultStaticLineDTO {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TStaticLineDTO[];
  /**  */
  offset: number;
}

/**
 * @description PageResultStaticOrderBO
 */
interface TPageResultStaticOrderBO {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TStaticOrderBO[];
  /**  */
  offset: number;
}

/**
 * @description PageResultStaticOrderDTO
 */
interface TPageResultStaticOrderDTO {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TStaticOrderDTO[];
  /**  */
  offset: number;
}

/**
 * @description PageResultStaticPriceDTO
 */
interface TPageResultStaticPriceDTO {
  /**  */
  size: number;
  /**  */
  current: number;
  /**  */
  total: number;
  /**  */
  records: TStaticPriceDTO[];
  /**  */
  offset: number;
}

type TPaymentTypeEnum = "WALLET";

/**
 * @description ProcessResult
 */
interface TProcessResult {
  /**  */
  success: boolean;
  /**  */
  message: string;
  /**  */
  count: number;
}

/**
 * @description QueryFilter
 */
interface TQueryFilter {
  /**  */
  field: string;
  /**  */
  operator: string;
  /**  */
  value: any;
  /**  */
  children?: TQueryFilter[];
}

/**
 * @description QuerySort
 */
interface TQuerySort {
  /**  */
  field: string;
  /**  */
  direction: string;
}

/**
 * @description RefundOrderRequest
 */
interface TRefundOrderRequest {
  /**  */
  refundAmount: number;
  /**  */
  refundReason: string;
  /**  */
  refundTransactionNo: string;
}

/**
 * @description SendVerificationCodeRequest
 */
interface TSendVerificationCodeRequest {
  /**  */
  email: string;
  /**  */
  type: number;
}

/**
 * @description SessionInfoLongAdminUserBo
 */
interface TSessionInfoLongAdminUserBo {
  /**  */
  name: string;
  /**  */
  token: string;
  /**  */
  expiredAt: TLocalDateTime;
  /**  */
  user: TAdminUserBo;
  /**  */
  order: string;
  /**  */
  kicked: boolean;
  /**  */
  permissions: string[];
  /**  */
  roles: string[];
  /**  */
  userId: string;
}

/**
 * @description SessionInfoLongClientUserBO
 */
interface TSessionInfoLongClientUserBO {
  /**  */
  name: string;
  /**  */
  token: string;
  /**  */
  expiredAt: TLocalDateTime;
  /**  */
  user: TClientUserBO;
  /**  */
  order: string;
  /**  */
  kicked: boolean;
  /**  */
  permissions: string[];
  /**  */
  roles: string[];
  /**  */
  userId: string;
}

/**
 * @description StaticBusinessTypeBO
 */
interface TStaticBusinessTypeBO {
  /**  */
  id: string;
  /**  */
  code: string;
  /**  */
  name: string;
  /**  */
  description: string;
  /**  */
  enabled: boolean;
  /**  */
  config: string;
  /**  */
  popular: boolean;
}

/**
 * @description StaticBusinessTypeDTO
 */
interface TStaticBusinessTypeDTO {
  /**  */
  id: string;
  /**  */
  code: string;
  /**  */
  name: string;
  /**  */
  description: string;
  /**  */
  enabled: boolean;
  /**  */
  config: string;
  /**  */
  popular: boolean;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
  /**  */
  tags: string[];
}

/**
 * @description StaticInstanceBO
 */
interface TStaticInstanceBO {
  /**  */
  id: string;
  /**  */
  lineId: string;
  /**  */
  proxyId: string;
  /**  */
  businessTypeId: string;
  /**  */
  orderNo: string;
  /**  */
  userId: string;
  /**  */
  locationName: string;
  /**  */
  businessTypeName: string;
  /**  */
  ip: string;
  /**  */
  port: number;
  /**  */
  username: string;
  /**  */
  password: string;
  /**  */
  expiresAt: TLocalDateTime;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  activatedAt: TLocalDateTime;
  /**  */
  status: TStaticProxyInstanceStatusEnum;
  /**  */
  channelId: string;
  /**  */
  thirdProxyId: string;
  /**  */
  config: string;
  /**  */
  remarks: string;
  /**  */
  lastUsedAt: TLocalDateTime;
  /**  */
  usageCount: string;
  /**  */
  trafficUsed: string;
  /**  */
  trafficLimit: string;
  /**  */
  concurrentLimit: number;
  /**  */
  updatedAt: TLocalDateTime;
}

/**
 * @description StaticInstanceDTO
 */
interface TStaticInstanceDTO {
  /**  */
  id: string;
  /**  */
  lineId: string;
  /**  */
  proxyId: string;
  /**  */
  businessTypeId: string;
  /**  */
  orderNo: string;
  /**  */
  userId: string;
  /**  */
  locationName: string;
  /**  */
  businessTypeName: string;
  /**  */
  ip: string;
  /**  */
  port: number;
  /**  */
  username: string;
  /**  */
  password: string;
  /**  */
  expiresAt: TLocalDateTime;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  activatedAt: TLocalDateTime;
  /**  */
  status: TStaticProxyInstanceStatusEnum;
  /**  */
  channelId: string;
  /**  */
  thirdProxyId: string;
  /**  */
  config: string;
  /**  */
  remarks: string;
  /**  */
  lastUsedAt: TLocalDateTime;
  /**  */
  usageCount: string;
  /**  */
  trafficUsed: string;
  /**  */
  trafficLimit: string;
  /**  */
  concurrentLimit: number;
  /**  */
  updatedAt: TLocalDateTime;
}

/**
 * @description 静态实例查询请求参数
 */
interface TStaticInstanceQueryRequest {
  /** 当前页，从0开始 */
  current: number;
  /** 每页大小 */
  size: number;
  /** 订单编号 */
  orderNo: string;
  /** 代理ID */
  proxyId: string;
  /** 业务类型ID */
  businessTypeId: string;
  /** IP地址 */
  ip: string;
  /** 端口 */
  port: number;
  /** 用户名 */
  username: string;
  /** 实例状态 */
  status: TStaticProxyInstanceStatusEnum;
  /** 渠道ID */
  channelId: string;
  /** 过期时间开始 */
  expiresAtStart: TLocalDateTime;
  /** 过期时间结束 */
  expiresAtEnd: TLocalDateTime;
  /** 创建时间开始 */
  createdAtStart: TLocalDateTime;
  /** 创建时间结束 */
  createdAtEnd: TLocalDateTime;
  /** 排序字段 */
  sortField: string;
  /** 排序方向，asc或desc */
  sortDirection: string;
}

/**
 * @description StaticLineBO
 */
interface TStaticLineBO {
  /**  */
  id: string;
  /**  */
  channelId: string;
  /**  */
  locationId: string;
  /**  */
  businessTypeId: string;
  /**  */
  ispType: TIspTypeEnum;
  /**  */
  amount: number;
  /**  */
  name: string;
  /**  */
  description: string;
  /**  */
  enabled: boolean;
  /**  */
  quantity: number;
  /**  */
  usedQuantity: number;
  /**  */
  reservedQuantity: number;
  /**  */
  protocols: number;
  /**  */
  status: TStaticProxyLineStatusEnum;
  /**  */
  priority: number;
  /**  */
  qualityScore: number;
  /**  */
  successRate: number;
  /**  */
  avgResponseTime: number;
  /**  */
  maxConcurrentConnections: number;
  /**  */
  config: string;
  /**  */
  remarks: string;
}

/**
 * @description StaticLineDTO
 */
interface TStaticLineDTO {
  /**  */
  id: string;
  /**  */
  channelId: string;
  /**  */
  channelObj: TChannelDTO;
  /**  */
  locationId: string;
  /**  */
  locationObj: TLocationDTO;
  /**  */
  businessTypeId: string;
  /**  */
  ispType: TIspTypeEnum;
  /**  */
  amount: number;
  /**  */
  name: string;
  /**  */
  description: string;
  /**  */
  enabled: boolean;
  /**  */
  quantity: number;
  /**  */
  usedQuantity: number;
  /**  */
  reservedQuantity: number;
  /**  */
  protocols: number;
  /**  */
  status: TStaticProxyLineStatusEnum;
  /**  */
  priority: number;
  /**  */
  qualityScore: number;
  /**  */
  successRate: number;
  /**  */
  avgResponseTime: number;
  /**  */
  maxConcurrentConnections: number;
  /**  */
  config: string;
  /**  */
  remarks: string;
  /**  */
  minPurchaseDays: number;
  /**  */
  maxPurchaseDays: number;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
}

/**
 * @description StaticOrderBO
 */
interface TStaticOrderBO {
  /**  */
  id: string;
  /**  */
  userId: string;
  /**  */
  orderNo: string;
  /**  */
  thirdOrderNo: string;
  /**  */
  status: TOrderStatusEnum;
  /**  */
  orderType: TOrderTypeEnum;
  /**  */
  quantity: number;
  /**  */
  duration: number;
  /**  */
  unitPrice: number;
  /**  */
  totalPrice: number;
  /**  */
  currency: string;
  /**  */
  proxyType: string;
  /**  */
  businessName: string;
  /**  */
  orderTime: TLocalDateTime;
  /**  */
  completedAt: TLocalDateTime;
  /**  */
  failedAt: TLocalDateTime;
  /**  */
  cancelledAt: TLocalDateTime;
  /**  */
  userObj: TClientUserBO;
  /**  */
  proxyStartTime: TLocalDateTime;
  /**  */
  proxyEndTime: TLocalDateTime;
  /**  */
  orderDetails: string;
  /**  */
  purchaseParams: string;
  /**  */
  externalResponse: string;
  /**  */
  failureReason: string;
  /**  */
  retryCount: number;
  /**  */
  maxRetryCount: number;
  /**  */
  nextRetryAt: TLocalDateTime;
  /**  */
  remarks: string;
  /**  */
  autoRenewal: boolean;
  /**  */
  renewalDaysBefore: number;
  /**  */
  discountAmount: number;
  /**  */
  discountCode: string;
  /**  */
  actualPayAmount: number;
  /**  */
  paymentMethod: string;
  /**  */
  paymentTransactionNo: string;
  /**  */
  paymentTime: TLocalDateTime;
  /**  */
  refundAmount: number;
  /**  */
  refundReason: string;
  /**  */
  refundTime: TLocalDateTime;
  /**  */
  refundTransactionNo: string;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
}

/**
 * @description StaticOrderDTO
 */
interface TStaticOrderDTO {
  /**  */
  id: string;
  /**  */
  userId: string;
  /**  */
  orderNo: string;
  /**  */
  thirdOrderNo: string;
  /**  */
  status: TOrderStatusEnum;
  /**  */
  orderType: TOrderTypeEnum;
  /**  */
  quantity: number;
  /**  */
  duration: number;
  /**  */
  unitPrice: number;
  /**  */
  totalPrice: number;
  /**  */
  currency: string;
  /**  */
  proxyType: string;
  /**  */
  businessName: string;
  /**  */
  orderTime: TLocalDateTime;
  /**  */
  completedAt: TLocalDateTime;
  /**  */
  failedAt: TLocalDateTime;
  /**  */
  cancelledAt: TLocalDateTime;
  /**  */
  proxyStartTime: TLocalDateTime;
  /**  */
  proxyEndTime: TLocalDateTime;
  /**  */
  orderDetails: string;
  /**  */
  purchaseParams: string;
  /**  */
  externalResponse: string;
  /**  */
  failureReason: string;
  /**  */
  retryCount: number;
  /**  */
  maxRetryCount: number;
  /**  */
  nextRetryAt: TLocalDateTime;
  /**  */
  remarks: string;
  /**  */
  autoRenewal: boolean;
  /**  */
  renewalDaysBefore: number;
  /**  */
  discountAmount: number;
  /**  */
  discountCode: string;
  /**  */
  actualPayAmount: number;
  /**  */
  paymentMethod: string;
  /**  */
  paymentTransactionNo: string;
  /**  */
  paymentTime: TLocalDateTime;
  /**  */
  refundAmount: number;
  /**  */
  refundReason: string;
  /**  */
  refundTime: TLocalDateTime;
  /**  */
  refundTransactionNo: string;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
}

/**
 * @description 静态订单查询请求参数
 */
interface TStaticOrderQueryRequest {
  /** 当前页，从0开始 */
  current: number;
  /** 每页大小 */
  size: number;
  /** 订单号 */
  orderNo: string;
  /** 订单状态 */
  status: TOrderStatusEnum;
  /** 订单类型 */
  orderType: TOrderTypeEnum;
  /** 货币 */
  currency: string;
  /** 代理开始时间开始 */
  createdAtStart: TLocalDateTime;
  /** 代理开始时间结束 */
  createdAtEnd: TLocalDateTime;
  /** 排序字段 */
  sortField: string;
  /** 排序方向，asc或desc */
  sortDirection: string;
}

/**
 * @description StaticPriceBO
 */
interface TStaticPriceBO {
  /**  */
  userId: string;
  /**  */
  locationId: string;
  /**  */
  businessTypeId: string;
  /**  */
  ispType: TIspTypeEnum;
  /**  */
  tagId: string;
  /**  */
  unitPrice: number;
  /**  */
  currency: string;
  /**  */
  effectiveAt: TLocalDateTime;
  /**  */
  expiredAt: TLocalDateTime;
  /**  */
  weight: number;
}

/**
 * @description StaticPriceDTO
 */
interface TStaticPriceDTO {
  /**  */
  id: string;
  /**  */
  userId: string;
  /**  */
  locationId: string;
  /**  */
  businessTypeId: string;
  /**  */
  ispType: TIspTypeEnum;
  /**  */
  tagId: string;
  /**  */
  unitPrice: number;
  /**  */
  currency: string;
  /**  */
  effectiveAt: TLocalDateTime;
  /**  */
  expiredAt: TLocalDateTime;
  /**  */
  weight: number;
  /**  */
  createdAt: TLocalDateTime;
  /**  */
  updatedAt: TLocalDateTime;
}

/**
 * @description 静态价格查询请求参数
 */
interface TStaticPriceQueryRequest {
  /** 当前页，从0开始 */
  current: number;
  /** 每页大小 */
  size: number;
  /** 地区ID */
  locationId: string;
  /** 业务类型ID */
  businessTypeId: string;
  /** ISP类型 */
  ispType: TIspTypeEnum;
  /** 标签ID */
  tagId: string;
  /** 价格最小值 */
  priceMin: number;
  /** 价格最大值 */
  priceMax: number;
  /** 货币 */
  currency: string;
  /** 权重最小值 */
  weightMin: number;
  /** 权重最大值 */
  weightMax: number;
  /** 生效时间开始 */
  effectiveAtStart: TLocalDateTime;
  /** 生效时间结束 */
  effectiveAtEnd: TLocalDateTime;
  /** 过期时间开始 */
  expiresAtStart: TLocalDateTime;
  /** 过期时间结束 */
  expiresAtEnd: TLocalDateTime;
  /** 是否只查询有效价格 */
  onlyValid: boolean;
  /** 排序字段 */
  sortField: string;
  /** 排序方向，asc或desc */
  sortDirection: string;
}

type TStaticProxyInstanceStatusEnum = "PENDING" | "PAID" | "SUCCESS" | "EXPIRED" | "CANCELLED" | "FAILED";

type TStaticProxyLineStatusEnum = "ACTIVE" | "INACTIVE" | "MAINTENANCE";

type TagTypeEnum = "0" | "1" | "2";

/**
 * @description ThirdPartyOrderStatus
 */
interface ThirdPartyOrderStatus {
  /**  */
  thirdOrderNo: string;
  /**  */
  status: string;
  /**  */
  message: string;
  /**  */
  orderDetails: any;
}

/**
 * @description TransactionApprovalRequest
 */
interface TransactionApprovalRequest {
  /**  */
  approvalStatus: string;
  /**  */
  approvalRemarks: string;
  /**  */
  approvedBy: string;
}

/**
 * @description UpdateAdminUserRequest
 */
interface TUpdateAdminUserRequest {
  /**  */
  email: string;
  /**  */
  phone: string;
  /**  */
  nickname: string;
  /**  */
  avatar: string;
  /**  */
  password: string;
}

/**
 * @description UpdateApiApplicationRequest
 */
interface TUpdateApiApplicationRequest {
  /**  */
  appName: string;
  /**  */
  description: string;
  /**  */
  ipWhitelist: string;
  /**  */
  expiresAt: TLocalDateTime;
  /**  */
  remarks: string;
}

/**
 * @description UpdateClientUserPasswordRequest
 */
interface TUpdateClientUserPasswordRequest {
  /**  */
  newPassword: string;
  /**  */
  remarks: string;
}

/**
 * @description UpdateClientUserRequest
 */
interface TUpdateClientUserRequest {
  /**  */
  email: string;
  /**  */
  region: string;
  /**  */
  language: string;
  /**  */
  currency: string;
  /**  */
  phone: string;
  /**  */
  nickname: string;
  /**  */
  avatar: string;
  /**  */
  remarks: string;
}

/**
 * @description UpdateClientUserTransactionRequest
 */
interface TUpdateClientUserTransactionRequest {
  /**  */
  status: string;
  /**  */
  balanceAfter: number;
  /**  */
  completedAt: TLocalDateTime;
  /**  */
  failedAt: TLocalDateTime;
  /**  */
  paymentChannel: string;
  /**  */
  paymentTransactionNo: string;
  /**  */
  description: string;
  /**  */
  transactionDetails: string;
  /**  */
  failureReason: string;
  /**  */
  retryCount: number;
  /**  */
  nextRetryAt: TLocalDateTime;
  /**  */
  approvalStatus: string;
  /**  */
  approvedBy: string;
  /**  */
  approvedAt: TLocalDateTime;
  /**  */
  approvalRemarks: string;
  /**  */
  remarks: string;
  /**  */
  extraData: string;
  /**  */
  operatorId: string;
  /**  */
  operatorName: string;
}

/**
 * @description UpdateClientUserWalletRequest
 */
interface TUpdateClientUserWalletRequest {
  /**  */
  balance: number;
  /**  */
  frozenAmount: number;
  /**  */
  note: string;
}

/**
 * @description UpdatePasswordRequest
 */
interface TUpdatePasswordRequest {
  /**  */
  oldPassword: string;
  /**  */
  newPassword: string;
  /**  */
  confirmPassword: string;
}

/**
 * @description UpdateStaticInstanceRequest
 */
interface TUpdateStaticInstanceRequest {
  /**  */
  username: string;
  /**  */
  password: string;
  /**  */
  expiresAt: TLocalDateTime;
  /**  */
  activatedAt: TLocalDateTime;
  /**  */
  status: TStaticProxyInstanceStatusEnum;
  /**  */
  thirdProxyId: string;
  /**  */
  config: string;
  /**  */
  remarks: string;
  /**  */
  lastUsedAt: TLocalDateTime;
  /**  */
  usageCount: string;
  /**  */
  trafficUsed: string;
  /**  */
  trafficLimit: string;
  /**  */
  concurrentLimit: number;
}

/**
 * @description UpdateStaticOrderRequest
 */
interface TUpdateStaticOrderRequest {
  /**  */
  thirdOrderNo: string;
  /**  */
  status: TOrderStatusEnum;
  /**  */
  completedAt: TLocalDateTime;
  /**  */
  failedAt: TLocalDateTime;
  /**  */
  cancelledAt: TLocalDateTime;
  /**  */
  proxyStartTime: TLocalDateTime;
  /**  */
  proxyEndTime: TLocalDateTime;
  /**  */
  detailJson: string;
  /**  */
  externalResponse: string;
  /**  */
  failureReason: string;
  /**  */
  retryCount: number;
  /**  */
  nextRetryAt: TLocalDateTime;
  /**  */
  remarks: string;
  /**  */
  autoRenewal: boolean;
  /**  */
  renewalDaysBefore: number;
  /**  */
  discountAmount: number;
  /**  */
  discountCode: string;
  /**  */
  actualPayAmount: number;
  /**  */
  paymentMethod: string;
  /**  */
  paymentTransactionNo: string;
  /**  */
  paymentTime: TLocalDateTime;
}

/**
 * @description UsernameLoginRequest
 */
interface TUsernameLoginRequest {
  /**  */
  username: string;
  /**  */
  password: string;
}

/**
 * @description WalletBalanceOperationRequest
 */
interface TWalletBalanceOperationRequest {
  /**  */
  amount: number;
  /**  */
  reason: string;
  /**  */
  operatorName: string;
}

