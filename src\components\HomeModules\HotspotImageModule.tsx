import React, { useState, useRef, useEffect } from 'react';
import { Button, App, Space, Spin, Input } from 'antd';
import { THotspotImageModule, THotspot } from './types';
import BaseModuleWrapper from './BaseModuleWrapper';
import { nanoid } from 'nanoid';
import { useCreation, useMemoizedFn, useSize } from 'ahooks';
import HttpUtils from '@/utils/http';
import { Stage, Layer, Rect, Transformer, Image } from 'react-konva';
import clsx from 'clsx';

interface HotspotImageModuleProps {
  module: THotspotImageModule;
  isFirst: boolean;
  isLast: boolean;
  onUpdate: (updatedModule: THotspotImageModule) => void;
  onDelete: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onInsertBefore: () => void;
  isEditing?: boolean;
}

const HotspotImageModuleComponent: React.FC<HotspotImageModuleProps> = ({
  module,
  isFirst,
  isLast,
  onUpdate,
  onDelete,
  onMoveUp,
  onMoveDown,
  onInsertBefore,
  isEditing: initialIsEditing,
}) => {

  const [isEditing, setIsEditing] = useState(initialIsEditing || false);
  const [image, setImage] = useState<HTMLImageElement>();
  const [imageSize, setImageSize] = useState<{ width: number; height: number } | null>(null);
  const [uploading, setUploading] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);
  const [newHotspot, setNewHotspot] = useState<THotspot | null>(null);
  const stageRef = useRef<any>(null);
  const transformerRef = useRef<any>(null);
  const hotspotRectsRef = useRef<any[]>([]);
  const { message } = App.useApp();
  const sizeBoxRef = useRef<HTMLDivElement>(null);
  const domSize = useSize(sizeBoxRef);
  const [overRectIndex, setOverRectIndex] = useState(-1);

  const [selectedHotspotIndex, setSelectedHotspotIndex] = useState<number | null>(null);

  const toggleSelectedHotspot = useMemoizedFn((index: number) => {
    setSelectedHotspotIndex((prev) => (prev === index ? null : index));
  });

  useEffect(() => {
    if (selectedHotspotIndex !== null && hotspotRectsRef.current && transformerRef.current) {
      transformerRef.current.nodes([hotspotRectsRef.current[selectedHotspotIndex]]);
    } else if (transformerRef.current) {
      transformerRef.current.nodes([]);
    }
  }, [selectedHotspotIndex]);

  const handleUpload = useMemoizedFn(async () => {
    setUploading(true);
    try {
      const url = await HttpUtils.uploadFile();
      onUpdate({ ...module, imageUrl: url });
    } catch (error) {
      message.error('Upload failed');
    } finally {
      setUploading(false);
    }
  });

  const handleLinkChange = useMemoizedFn((index: number, link: string) => {
    const updatedHotspots = [...module.hotspots];
    updatedHotspots[index] = { ...updatedHotspots[index], link };
    onUpdate({ ...module, hotspots: updatedHotspots });
  });

  useEffect(() => {
    if (module.imageUrl) {
      const img = new window.Image();
      img.src = module.imageUrl;
      img.onload = () => {
        setImage(img);
        setImageSize({
          width: img.width,
          height: img.height,
        })
        setImageLoading(false);
      };
      img.onerror = () => {
        message.error('Failed to load image');
        setImageLoading(false);
      };
      setImageLoading(true);
    }
  }, [module.imageUrl]);

  const stageSize = useCreation(() => {
    if (!imageSize || !domSize) return {
      width: 0,
      height: 0,
    };
    return {
      width: domSize.width,
      height: imageSize.height * (domSize.width / imageSize.width),
    }
  }, [imageSize, domSize]);



  const handleMouseDown = (e: any) => {
    if (!isEditing) return;
    const pos = e.target.getStage().getPointerPosition();
    setNewHotspot({
      id: nanoid(),
      x: pos.x / stageSize.width,
      y: pos.y / stageSize.height,
      width: 0,
      height: 0,
      link: '',
    });
  };

  const handleMouseMove = (e: any) => {
    if (!isEditing || !newHotspot) return;
    const pos = e.target.getStage().getPointerPosition();
    setNewHotspot({
      ...newHotspot,
      width: (pos.x / stageSize.width) - newHotspot.x,
      height: (pos.y / stageSize.height) - newHotspot.y,
    });
  };

  const handleMouseUp = () => {
    if (!isEditing || !newHotspot) return;
    const width = newHotspot.width * stageSize.width;
    const height = newHotspot.height * stageSize.height;
    if (width > 20 || height > 20) {
      setSelectedHotspotIndex(module.hotspots.length);
      onUpdate({ ...module, hotspots: [...module.hotspots, newHotspot] });
    }

    setNewHotspot(null);
  };

  const handleDeleteHotspot = (index: number) => {
    const updatedHotspots = [...module.hotspots];
    updatedHotspots.splice(index, 1);
    onUpdate({ ...module, hotspots: updatedHotspots });
    setSelectedHotspotIndex(null);
  };

  const renderEditContent = () => (
    <div>
      <div className="flex justify-between mb-4">
        <div className="text-lg font-medium">热区图模块</div>
        <Space>
          <Button type="primary" onClick={handleUpload} loading={uploading}>
            上传图片
          </Button>
        </Space>
      </div>
      {imageLoading && <div className="flex justify-center items-center h-64"><Spin /></div>}
      <Stage
        width={stageSize.width}
        height={stageSize.height}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        ref={stageRef}
      >
        <Layer>
          <Image image={image} width={stageSize.width} height={stageSize.height} />
          {module.hotspots.map((hotspot, index) => (
            <React.Fragment key={index}>
              <Rect
                x={hotspot.x * stageSize.width}
                y={hotspot.y * stageSize.height}
                width={hotspot.width * stageSize.width}
                height={hotspot.height * stageSize.height}
                ref={(r) => hotspotRectsRef.current[index] = r}
                stroke="red"
                fill="#ff000022"
                strokeWidth={2}
                draggable
                onMouseDown={() => toggleSelectedHotspot(index)}
                onDragEnd={(e) => {
                  const node = e.target;
                  const updatedHotspots = [...module.hotspots];
                  updatedHotspots[index] = {
                    ...updatedHotspots[index],
                    x: node.x() / stageSize.width,
                    y: node.y() / stageSize.height,
                  };
                  onUpdate({ ...module, hotspots: updatedHotspots });
                }}
                onTransformEnd={(e) => {
                  setTimeout(() => {
                    const node = e.target;
                    const updatedHotspots = [...module.hotspots];
                    updatedHotspots[index] = {
                      ...updatedHotspots[index],
                      x: node.x() / stageSize.width,
                      y: node.y() / stageSize.height,
                      width: node.width() * node.scaleX() / stageSize.width,
                      height: node.height() * node.scaleY() / stageSize.height,
                    };
                    node.scaleX(1);
                    node.scaleY(1);
                    onUpdate({ ...module, hotspots: updatedHotspots });
                  }, 100);
                }}
              />

            </React.Fragment>
          ))}
          <Transformer
            ref={transformerRef}
            flipEnabled={false}
            boundBoxFunc={(_, newBox) => {
              newBox.rotation = 0; // 禁用旋转
              return newBox;
            }}
          />
          {newHotspot && (
            <Rect
              x={newHotspot.x * stageSize.width}
              y={newHotspot.y * stageSize.height}
              width={newHotspot.width * stageSize.width}
              height={newHotspot.height * stageSize.height}
              stroke="red"
              fill="#ff000022"
              strokeWidth={1}
            />
          )}
        </Layer>
      </Stage>
      <div className='flex flex-col gap-2 py-2'>
        {module.hotspots.map((hotspot, index) => (
          <Space.Compact key={index}>
            <Input className={clsx({
              'bg-blue-200': index === selectedHotspotIndex
            })} addonBefore="链接" value={hotspot.link} onChange={(e) => handleLinkChange(index, e.target.value)} />
            <Button type="primary" danger onClick={() => handleDeleteHotspot(index)}>删除</Button>
          </Space.Compact>
        ))}
        {module.imageUrl && module.hotspots.length === 0 && (
          <div>
            请在图上添加热区
          </div>
        )}
      </div>
    </div>
  );

  const renderPreviewContent = () => (
    <div>
      {imageLoading && <div className="flex justify-center items-center h-64"><Spin /></div>}
      <Stage width={stageSize.width} height={stageSize.height} ref={stageRef}>
        <Layer>
          <Image image={image} width={stageSize.width} height={stageSize.height} />
          {module.hotspots.map((hotspot, index) => (
            <Rect
              key={index}
              x={hotspot.x * stageSize.width}
              y={hotspot.y * stageSize.height}
              width={hotspot.width * stageSize.width}
              height={hotspot.height * stageSize.height}
              stroke="blue"
              fill="#0000ff22"
              strokeWidth={1}
              onMouseEnter={() => {
                if (hotspot.link) {
                  stageRef.current.container().style.cursor = 'pointer';
                  setOverRectIndex(index);
                }
              }}
              onMouseLeave={() => {
                if (hotspot.link) {
                  stageRef.current.container().style.cursor = 'default';
                  setOverRectIndex(-1);
                }
              }}
            />
          ))}
        </Layer>
      </Stage>
      <div className="flex justify-center items-center min-h-8 text-blue-500">
        {overRectIndex !== -1 && (module.hotspots[overRectIndex].link)}
      </div>
    </div>

  );

  return (
    <BaseModuleWrapper
      isFirst={isFirst}
      isLast={isLast}
      onDelete={onDelete}
      onMoveUp={onMoveUp}
      onMoveDown={onMoveDown}
      onInsertBefore={onInsertBefore}
      isEditing={isEditing}
      onEditingChange={setIsEditing}
    >
      <div ref={sizeBoxRef}></div>
      {isEditing ? renderEditContent() : renderPreviewContent()}
    </BaseModuleWrapper>
  );
};

export default HotspotImageModuleComponent;
