// import { apiShopAllTags } from "@/apis/apis.api";
import { useRequest } from "ahooks";
import { apiListChannels } from "@/apis/gen.apis";

export default function useChannels() {
  const { data: channels = [] } = useRequest(async () => {
    return (await apiListChannels({
      current: 0,
      size: 10000,
    })).records;
  });

  return {
    channels,
    channelOptions: channels.map((item) => ({ label: item.name, value: item.id })),
    channelEnum: channels.reduce((prev, cur) => ({ ...prev, [cur.id]: cur.name }), {}),
  };
}
