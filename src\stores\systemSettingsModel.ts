import { defineModel } from 'foca';
import { apiSystemSettingGetAll, apiSystemSettingGetByKey } from '@/apis/apis.api';

// System setting keys
export const WEBSITE_NAME_KEY = 'client.website.name';
export const WEBSITE_LOGO_KEY = 'client.website.logo';
export const PAYMENT_ACCOUNT_NAME_KEY = 'client.payment.account.name';
export const PAYMENT_ACCOUNT_NUMBER_KEY = 'client.payment.account.number';
export const PAYMENT_BANK_NAME_KEY = 'client.payment.bank.name';
export const BID_DEPOSIT_PERCENTAGE_KEY = 'client.transaction.bid_deposit_percentage';
export const PLATFORM_FEE_PERCENTAGE_KEY = 'client.transaction.platform_fee_percentage';
export const ORDER_AUTO_CANCEL_TIMEOUT_KEY = 'client.order.auto_cancel_unpaid';

export interface SystemSettingsModelState {
  websiteName: string;
  websiteLogo: string;
  paymentAccountName: string;
  paymentAccountNumber: string;
  paymentBankName: string;
  bidDepositPercentage: string;
  platformFeePercentage: string;
  orderAutoCancelTimeout: string;
  loading: boolean;
  initialized: boolean;
}

const initialState: SystemSettingsModelState = {
  websiteName: '',
  websiteLogo: '',
  paymentAccountName: '',
  paymentAccountNumber: '',
  paymentBankName: '',
  bidDepositPercentage: '20',
  platformFeePercentage: '5',
  orderAutoCancelTimeout: '24',
  loading: false,
  initialized: false
};

export const systemSettingsModel = defineModel('systemSettings', {
  initialState,
  reducers: {
    setSettings(state, settings: Partial<SystemSettingsModelState>) {
      Object.assign(state, settings);
    },
    setLoading(state, loading: boolean) {
      state.loading = loading;
    },
    setInitialized(state, initialized: boolean) {
      state.initialized = initialized;
    }
  },
  methods: {
    async loadSettings() {
      // If already loaded, don't fetch again
      if (this.state.initialized) {
        return;
      }

      this.setLoading(true);
      try {
        // Get all settings at once
        const allSettings = await apiSystemSettingGetAll();

        // Create a map for easier access
        const settingsMap = allSettings.reduce((map, setting) => {
          map[setting.key] = setting;
          return map;
        }, {} as Record<string, TSystemSettingBo>);

        // Extract values for our settings
        const settings = {
          websiteName: settingsMap[WEBSITE_NAME_KEY]?.value || '',
          paymentAccountName: settingsMap[PAYMENT_ACCOUNT_NAME_KEY]?.value || '',
          paymentAccountNumber: settingsMap[PAYMENT_ACCOUNT_NUMBER_KEY]?.value || '',
          paymentBankName: settingsMap[PAYMENT_BANK_NAME_KEY]?.value || '',
          bidDepositPercentage: settingsMap[BID_DEPOSIT_PERCENTAGE_KEY]?.value || '20',
          platformFeePercentage: settingsMap[PLATFORM_FEE_PERCENTAGE_KEY]?.value || '5',
          orderAutoCancelTimeout: settingsMap[ORDER_AUTO_CANCEL_TIMEOUT_KEY]?.value || '24'
        };

        // Handle logo URL separately to clean any extra quotes
        if (settingsMap[WEBSITE_LOGO_KEY]?.value) {
          try {
            // Try to parse the value in case it's a JSON string with quotes
            const logoValue = settingsMap[WEBSITE_LOGO_KEY].value;
            // Remove any extra quotes that might be present
            const cleanedValue = logoValue.replace(/^"|"$/g, '');
            settings.websiteLogo = cleanedValue;
          } catch (e) {
            // If parsing fails, use the value as is
            settings.websiteLogo = settingsMap[WEBSITE_LOGO_KEY].value;
          }
        }

        this.setSettings(settings);
        this.setInitialized(true);
      } catch (error) {
        console.error('Failed to load system settings:', error);
      } finally {
        this.setLoading(false);
      }
    },

    async loadWebsiteLogo() {
      try {
        const logoSetting = await apiSystemSettingGetByKey(WEBSITE_LOGO_KEY);
        if (logoSetting?.value) {
          // Clean the logo URL
          const cleanedValue = logoSetting.value.replace(/^"|"$/g, '');
          this.setSettings({ websiteLogo: cleanedValue });
        }
      } catch (error) {
        console.error('Failed to load website logo:', error);
      }
    }
  }
});
