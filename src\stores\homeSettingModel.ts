import { defineModel } from 'foca';
import { apiProductPageList, apiArticlePageList, apiBrandPageList } from '@/apis/apis.api';


export interface HomeSettingModelState {
  productMap: Record<string, ProductBo>;
  articleMap: Record<string, TArticleBo>;
  brandMap: Record<string, ProductBrandBo>;
}

const initialState: HomeSettingModelState = {
  productMap: {},
  articleMap: {},
  brandMap: {},
};

export const homeSettingModel = defineModel('homeSettingModel', {
  initialState,
  computed: {},
  reducers: {
    updateProductMapByProducts(state, payload: ProductBo[]) {
      if (!payload || payload.length === 0) return;
      state.productMap = {
        ...state.productMap,
        ...payload.reduce((acc, item) => {
          acc[item.id] = item;
          return acc;
        }, {} as Record<string, ProductBo>)
      }
    },
    updateArticleMapByArticles(state, payload: TArticleBo[]) {
      if (!payload || payload.length === 0) return;
      state.articleMap = {
        ...state.articleMap,
        ...payload.reduce((acc, item) => {
          acc[item.id] = item;
          return acc;
        }, {} as Record<string, TArticleBo>)
      }
    },
    updateBrandMapByBrands(state, payload: ProductBrandBo[]) {
      if (!payload || payload.length === 0) return;
      state.brandMap = {
        ...state.brandMap,
        ...payload.reduce((acc, item) => {
          acc[item.id] = item;
          return acc;
        }, {} as Record<string, ProductBrandBo>)
      }
    },
  },
  methods: {
    async loadProductByIds(ids: string[]) {
      const needLoadIds = ids.filter(id => !this.state.productMap[id]);
      if (needLoadIds.length === 0) return;
      const res = await apiProductPageList({
        current: 0,
        size: needLoadIds.length,
        filters: [{
          field: 'id',
          operator: 'in',
          value: needLoadIds
        }],
        sorts: [],
      });
      this.updateProductMapByProducts(res.records);
    },
    async loadArticleByIds(ids: string[]) {
      const needLoadIds = ids.filter(id => !this.state.articleMap[id]);
      if (needLoadIds.length === 0) return;
      const res = await apiArticlePageList({
        current: 0,
        size: needLoadIds.length,
        filters: [{
          field: 'id',
          operator: 'in',
          value: needLoadIds
        }],
        sorts: [],
      });
      this.updateArticleMapByArticles(res.records);
    },
    async loadBrandByIds(ids: string[]) {
      const needLoadIds = ids.filter(id => !this.state.brandMap[id]);
      if (needLoadIds.length === 0) return;
      const res = await apiBrandPageList({
        current: 0,
        size: needLoadIds.length,
        filters: [{
          field: 'id',
          operator: 'in',
          value: needLoadIds
        }],
        sorts: [],
      });
      this.updateBrandMapByBrands(res.records);
    },
  },
});