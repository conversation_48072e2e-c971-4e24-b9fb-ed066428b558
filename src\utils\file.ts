import { message, UploadFile } from 'antd';

export class FileUtils {
  /**
   * 上传文件列表转换为ids
   * @param items
   */
  static uploadFilesToUrls(items?: UploadFile[]): string[] {
    return (items || []).map((item) => {
      const url = item.response?.url || ''
      if (!url) return ''
      return url.startsWith('/api') ? url : `/api${url}`
    }).filter((item) => !!item);
  }

  static urlToUploadFile(sourceUrl: string): UploadFile {
    let url = sourceUrl.split('?')[0];
    if (!url.startsWith('/api')) {
      url = `/api${url}`
    }
    const ext = url.split('.').pop();
    const name = url.split('/').pop();
    const type =
      {
        jpg: 'image/jpeg',
        jpeg: 'image/jpeg',
        png: 'image/png',
        gif: 'image/gif',
        mp4: 'video/mp4',
        avi: 'video/x-msvideo',
        mov: 'video/quicktime',
        mp3: 'audio/mpeg',
        wav: 'audio/wav',
        pdf: 'application/pdf',
        doc: 'application/msword',
        docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        xls: 'application/vnd.ms-excel',
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ppt: 'application/vnd.ms-powerpoint',
        pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        zip: 'application/zip',
        rar: 'application/x-rar-compressed',
        txt: 'text/plain',
      }[ext] || 'application/octet-stream';
    return {
      uid: name,
      name,
      type,
      status: 'done',
      response: {
        url,
      },
      url,
    };
  }

  /**
   * 服务端的文件列表转换为上传文件列表
   * @param item
   */
  static urlsToUploadFiles(items?: string[]): UploadFile[] {
    return (items || []).filter(item => !!item).map((sourceUrl) => {
      return this.urlToUploadFile(sourceUrl);
    });
  }

  /**
   * 资源地址转换成可以预览的文件
   * @param url
   */
  static urlToPreviewFile(url: string): TPreviewFile {
    url = url || ''
    const ext = url.split('.').pop().toLowerCase();
    const name = decodeURIComponent(url.split('/').pop());
    const type =
      {
        jpg: 'image',
        jpeg: 'image',
        png: 'image',
        gif: 'image',
        webp: 'image',
        mp4: 'video',
        mov: 'video',
        avi: 'video',
        webm: 'video',
        ogg: 'video',
      }[ext] || 'unknown';
    return { previewUrl: url, type: type, url, name } as TPreviewFile;
  }

  /** 资源地址转换成可以预览的文件（数组） */
  static urlsToPreviewFiles(urls?: string[]): TPreviewFile[] {
    return (urls || []).filter(item => !!item).map((url) => this.urlToPreviewFile(url));
  }

  /**
   * 上传前处理
   * @param file 
   * @returns 
   */
  static beforeUpload = async (file: any) => {
    return new Promise<boolean>((resolve, reject) => {
      const isImage = (file.type || '').startsWith('image/');
      if (isImage) {
        const isGt20M = file.size / 1024 / 1024 > 20;
        if (isGt20M) {
          message.error('图片不能超过20MB');
          return reject(false)
        }
        const image = new window.Image();
        image.src = URL.createObjectURL(file);
        image.onload = () => {
          const width = image.width;
          const height = image.height;
          if (width > 3000 || height > 3000) {
            message.error('图片不能超过3000*3000');
            return reject(false)
          }
        }
      }
      return resolve(true)
    })
  };
}
