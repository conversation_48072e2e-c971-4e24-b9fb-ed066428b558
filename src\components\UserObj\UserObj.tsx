import { CopyOutlined } from "@ant-design/icons";
import { App, Dropdown } from "antd";

export interface TUserObjProps {
  userObj?: TClientUserBO;
  children?: React.ReactNode;
  extraItems?: { icon: React.ReactNode, label: string, key: string, hide?: boolean, value?: string }[]
}

function UserObj(props: TUserObjProps) {
  const { message } = App.useApp();
  return (
    props.userObj ? (
      <Dropdown menu={{
        items: [
          { icon: <CopyOutlined />, label: `ID: ${props.userObj.id || ''}`, key: 'id', hide: false },
          { icon: <CopyOutlined />, label: `用户名: ${props.userObj.username || ''}`, key: 'username', hide: false },
          { icon: <CopyOutlined />, label: `昵称: ${props.userObj.nickname || ''}`, key: 'nickname', hide: !props.userObj.nickname },
          { icon: <CopyOutlined />, label: `邮箱: ${props.userObj.email || ''}`, key: 'email', hide: !props.userObj.email },
          { icon: <CopyOutlined />, label: `手机: ${props.userObj.phone || ''}`, key: 'phone', hide: !props.userObj.phone },
          { icon: <CopyOutlined />, label: `备注: ${props.userObj.remarks || ''}`, key: 'remarks', hide: !props.userObj.remarks },
          ...(props.extraItems || []),
        ].filter(item => !item.hide),
        onClick: (item: any) => {
          const text = props.userObj[item.key] || (props.extraItems || []).find(i => i.key === item.key)?.value || ''
          if (text) {
            navigator.clipboard.writeText(text)
            message.success('复制成功')
          }

        }
      }}
        arrow
      >
        <div className="cursor-pointer">
          {props.children ? props.children : <div className="text-blue-500">{props.userObj.nickname || props.userObj.username}</div>}
        </div>

      </Dropdown>
    ) : null

  )
}

export default UserObj
