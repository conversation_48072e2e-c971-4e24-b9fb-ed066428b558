#!/usr/bin/env ts-node
import fs from 'fs';
import path from 'path';

// 脚本入口：遍历 src/pages 目录，为每个页面生成 auth 静态属性
function main() {
  const pagesDir = path.resolve( './src/pages');
  const files = walkDir(pagesDir);
  files.forEach(processFile);
}

// 递归读取目录下所有 .ts/.tsx 文件
function walkDir(dir: string): string[] {
  const result: string[] = [];
  for (const name of fs.readdirSync(dir)) {
    const full = path.join(dir, name);
    const stat = fs.statSync(full);
    if (stat.isDirectory()) {
      result.push(...walkDir(full));
    } else if (/\.(ts|tsx)$/.test(name)) {
      result.push(full);
    }
  }
  return result;
}

// 处理单个页面文件：提取 api 调用、组件名，更新 auth 属性
function processFile(file: string) {
  let content = fs.readFileSync(file, 'utf8');

  // 提取默认导出组件名
  const match = content.match(/export\s+default\s+(\w+)/);
  if (!match) return;
  const comp = match[1];

  // 提取 import 自 '@/apis/apis.api' 的 api 名称
  const importMatch = content.match(/import\s*{([^}]+)}\s*from ['"]@\/apis\/apis\.api['"]/);
  let apis: string[] = [];
  if (importMatch) {
    apis = importMatch[1]
      .split(',')
      .map(s => s.trim())
      .filter(s => s.startsWith('api'));
  }
  // 如果没从 import 中获取到，则全文件匹配 api 函数调用
  if (apis.length === 0) {
    const all = content.match(/api[A-Za-z0-9_]+/g) || [];
    apis = Array.from(new Set(all));
  }

  // 移除已有的 auth 定义（匹配整行，支持多行删除）
  const authRegex = new RegExp(`^\\s*${comp}\\.auth\\s*=\\s*\\[.*?\\];?\\s*$`, 'gm');
  content = content.replace(authRegex, '');

  // 生成新的 auth 行
  const authLine = `${comp}.auth = [${apis.map(a => `'${a}'`).join(', ')}];`;

  // 将 auth 插入到 export default 之后，如果没有则追加到末尾
  const exportRegex = new RegExp(`export\\s+default\\s+${comp};?`);
  if (exportRegex.test(content)) {
    content = content.replace(exportRegex, (m) => `${m}\n\n${authLine}`);
  } else {
    content = content.trimEnd() + '\n\n' + authLine + '\n';
  }

  fs.writeFileSync(file, content, 'utf8');
}

main();