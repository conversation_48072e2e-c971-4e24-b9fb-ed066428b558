
import { ToolsUtil } from '@/utils/tools';
import {
  connect,
  mapReadPretty,
} from '@formily/react'
import { InputNumber } from 'antd';


const MonetCent = (props: any) => {
  return (
    <InputNumber
      value={ToolsUtil.mathStrip(props.value / 100)}
      onChange={(newValue) => props.onChange(ToolsUtil.mathStrip(newValue * 100))}
      formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
      parser={(value) => value?.replace(/\$\s?|(,*)/g, '') as unknown as number}
      addonAfter='元'
      className='w-full'
    />
  );
};

export default connect(
  MonetCent,
  mapReadPretty(({value}) => <div className='text-bold'>
    {ToolsUtil.moneyCentFormat(value)}
  </div>),
);
