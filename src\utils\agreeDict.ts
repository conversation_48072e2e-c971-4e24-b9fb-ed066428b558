import { ToolsUtil } from "./tools";

export const baseAgreeDicts = {
  User_gender: {
    name: '性别',
    options: [
      { value: '0', label: '男', data: { color: '#A9A9A9' } },
      { value: '1', label: '女', data: { color: '#FF4D4D' } },
    ],
  },
  ClientWallet_type: {
    name: '钱包类型',
    options: [
      { value: 'MAIN', label: '主钱包' },
      { value: 'SAVINGS', label: '储蓄钱包' },
      { value: 'TRADING', label: '交易钱包' },
      { value: 'FROZEN', label: '冻结钱包' },
    ],
  },
  ClientWallet_currency: {
    name: '货币',
    options: [
      { value: 'CNY', label: '人民币 (CNY)' },
      { value: 'USD', label: '美元 (USD)' },
      { value: 'EUR', label: '欧元 (EUR)' },
      { value: 'JPY', label: '日元 (JPY)' },
      { value: 'KRW', label: '韩元 (KRW)' },
      { value: 'BTC', label: '比特币 (BTC)' },
      { value: 'ETH', label: '以太坊 (ETH)' },
    ],
  },
};

export const AgreeDicts = ToolsUtil.baseAgreeDicts2AgreeDicts(baseAgreeDicts);


