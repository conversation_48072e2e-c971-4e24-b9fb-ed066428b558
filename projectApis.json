[{"name": "apiGetUserById", "method": "GET", "url": "/users/_p_", "docUrl": "/admin/users/:id", "apiUrl": "/users/${id}", "comment": "Admin User Management/根据ID获取管理员用户"}, {"name": "apiUpdateUser", "method": "PUT", "url": "/users/_p_", "docUrl": "/admin/users/:id", "apiUrl": "/users/${id}", "comment": "Admin User Management/更新管理员用户"}, {"name": "apiUpdatePassword", "method": "PUT", "url": "/users/_p_/password", "docUrl": "/admin/users/:id/password", "apiUrl": "/users/${id}/password", "comment": "Admin User Management/更新管理员用户密码"}, {"name": "apiCreateUser", "method": "POST", "url": "/users", "docUrl": "/admin/users", "apiUrl": "/users", "comment": "Admin User Management/创建新的管理员用户"}, {"name": "apiAddRoleToUser", "method": "POST", "url": "/users/_p_/roles/_p_", "docUrl": "/admin/users/:userId/roles/:roleId", "apiUrl": "/users/${userId}/roles/${roleId}", "comment": "Admin User Management/为管理员用户添加角色（通过角色ID）"}, {"name": "apiRemoveRoleFromUser", "method": "DELETE", "url": "/users/_p_/roles/_p_", "docUrl": "/admin/users/:userId/roles/:roleId", "apiUrl": "/users/${userId}/roles/${roleId}", "comment": "Admin User Management/从管理员用户中移除角色（通过角色ID）"}, {"name": "apiAddRoleToUserByCode", "method": "POST", "url": "/users/_p_/roles/code/_p_", "docUrl": "/admin/users/:userId/roles/code/:roleCode", "apiUrl": "/users/${userId}/roles/code/${roleCode}", "comment": "Admin User Management/为管理员用户添加角色（通过角色代码）"}, {"name": "apiRemoveRoleFromUserByCode", "method": "DELETE", "url": "/users/_p_/roles/code/_p_", "docUrl": "/admin/users/:userId/roles/code/:roleCode", "apiUrl": "/users/${userId}/roles/code/${roleCode}", "comment": "Admin User Management/从管理员用户中移除角色（通过角色代码）"}, {"name": "apiAssignRolesToUser", "method": "POST", "url": "/users/_p_/roles", "docUrl": "/admin/users/:id/roles", "apiUrl": "/users/${id}/roles", "comment": "Admin User Management/为管理员用户分配角色（通过角色ID）"}, {"name": "apiAssignRolesToUserByCodes", "method": "POST", "url": "/users/_p_/roles/codes", "docUrl": "/admin/users/:id/roles/codes", "apiUrl": "/users/${id}/roles/codes", "comment": "Admin User Management/为管理员用户分配角色（通过角色代码）"}, {"name": "apiGetUsers", "method": "POST", "url": "/users/page", "docUrl": "/admin/users/page", "apiUrl": "/users/page", "comment": "Admin User Management/分页获取管理员用户"}, {"name": "apiSetUserActive", "method": "PATCH", "url": "/users/_p_/active", "docUrl": "/admin/users/:id/active", "apiUrl": "/users/${id}/active?active=${active}", "comment": "Admin User Management/设置管理员用户活动状态"}, {"name": "apiAdminUserPageList", "method": "POST", "url": "/users/page", "docUrl": "/admin/users/page", "apiUrl": "/users/page", "comment": "Admin User Management/分页获取管理员用户"}, {"name": "apiGetRoles", "method": "POST", "url": "/roles/page", "docUrl": "/admin/roles/page", "apiUrl": "/roles/page", "comment": "管理员角色管理/分页获取管理员角色"}, {"name": "apiGetRoleById", "method": "GET", "url": "/roles/_p_", "docUrl": "/admin/roles/:id", "apiUrl": "/roles/${id}", "comment": "管理员角色管理/通过ID获取管理员角色"}, {"name": "apiUpdateRole", "method": "PUT", "url": "/roles/_p_", "docUrl": "/admin/roles/:id", "apiUrl": "/roles/${id}", "comment": "管理员角色管理/更新管理员角色"}, {"name": "apiGetAllRoles", "method": "GET", "url": "/roles", "docUrl": "/admin/roles", "apiUrl": "/roles", "comment": "管理员角色管理/获取所有管理员角色"}, {"name": "apiCreateRole", "method": "POST", "url": "/roles", "docUrl": "/admin/roles", "apiUrl": "/roles", "comment": "管理员角色管理/创建新的管理员角色"}, {"name": "apiAddPermissionToRole", "method": "POST", "url": "/roles/_p_/permissions/_p_", "docUrl": "/admin/roles/:roleId/permissions/:permissionId", "apiUrl": "/roles/${roleId}/permissions/${permissionId}", "comment": "管理员角色管理/为管理员角色添加权限（通过权限ID）"}, {"name": "apiRemovePermissionFromRole", "method": "DELETE", "url": "/roles/_p_/permissions/_p_", "docUrl": "/admin/roles/:roleId/permissions/:permissionId", "apiUrl": "/roles/${roleId}/permissions/${permissionId}", "comment": "管理员角色管理/从管理员角色中移除权限（通过权限ID）"}, {"name": "apiAddPermissionToRoleByCode", "method": "POST", "url": "/roles/_p_/permissions/code/_p_", "docUrl": "/admin/roles/:roleId/permissions/code/:permissionCode", "apiUrl": "/roles/${roleId}/permissions/code/${permissionCode}", "comment": "管理员角色管理/为管理员角色添加权限（通过权限代码）"}, {"name": "apiRemovePermissionFromRoleByCode", "method": "DELETE", "url": "/roles/_p_/permissions/code/_p_", "docUrl": "/admin/roles/:roleId/permissions/code/:permissionCode", "apiUrl": "/roles/${roleId}/permissions/code/${permissionCode}", "comment": "管理员角色管理/从管理员角色中移除权限（通过权限代码）"}, {"name": "apiGetRolePermissions", "method": "GET", "url": "/roles/_p_/permissions", "docUrl": "/admin/roles/:id/permissions", "apiUrl": "/roles/${id}/permissions", "comment": "管理员角色管理/通过ID获取管理员角色权限"}, {"name": "apiAssignPermissionsToRole", "method": "POST", "url": "/roles/_p_/permissions", "docUrl": "/admin/roles/:id/permissions", "apiUrl": "/roles/${id}/permissions", "comment": "管理员角色管理/为管理员角色分配权限（通过权限ID）"}, {"name": "apiAssignPermissionsToRoleByCodes", "method": "POST", "url": "/roles/_p_/permissions/codes", "docUrl": "/admin/roles/:id/permissions/codes", "apiUrl": "/roles/${id}/permissions/codes", "comment": "管理员角色管理/为管理员角色分配权限（通过权限代码）"}, {"name": "apiSetRoleActive", "method": "PATCH", "url": "/roles/_p_/active", "docUrl": "/admin/roles/:id/active", "apiUrl": "/roles/${id}/active?active=${active}", "comment": "管理员角色管理/设置管理员角色激活状态"}, {"name": "apiGetRoleByCode", "method": "GET", "url": "/roles/code/_p_", "docUrl": "/admin/roles/code/:code", "apiUrl": "/roles/code/${code}", "comment": "管理员角色管理/通过代码获取管理员角色"}, {"name": "apiAdminRolePageList", "method": "POST", "url": "/roles/page", "docUrl": "/admin/roles/page", "apiUrl": "/roles/page", "comment": "管理员角色管理/分页获取管理员角色"}, {"name": "apiAdminRoleGetById", "method": "GET", "url": "/roles/_p_", "docUrl": "/admin/roles/:id", "apiUrl": "/roles/${id}", "comment": "管理员角色管理/通过ID获取管理员角色"}, {"name": "apiAdminRoleUpdate", "method": "PUT", "url": "/roles/_p_", "docUrl": "/admin/roles/:id", "apiUrl": "/roles/${id}", "comment": "管理员角色管理/更新管理员角色"}, {"name": "apiAdminRoleGetAll", "method": "GET", "url": "/roles", "docUrl": "/admin/roles", "apiUrl": "/roles", "comment": "管理员角色管理/获取所有管理员角色"}, {"name": "apiAdminRoleCreate", "method": "POST", "url": "/roles", "docUrl": "/admin/roles", "apiUrl": "/roles", "comment": "管理员角色管理/创建新的管理员角色"}, {"name": "apiAdminRoleAddPermission", "method": "POST", "url": "/roles/_p_/permissions/_p_", "docUrl": "/admin/roles/:roleId/permissions/:permissionId", "apiUrl": "/roles/${roleId}/permissions/${permissionId}", "comment": "管理员角色管理/为管理员角色添加权限（通过权限ID）"}, {"name": "apiAdminRoleRemovePermission", "method": "DELETE", "url": "/roles/_p_/permissions/_p_", "docUrl": "/admin/roles/:roleId/permissions/:permissionId", "apiUrl": "/roles/${roleId}/permissions/${permissionId}", "comment": "管理员角色管理/从管理员角色中移除权限（通过权限ID）"}, {"name": "apiAdminRolePermissions", "method": "GET", "url": "/roles/_p_/permissions", "docUrl": "/admin/roles/:id/permissions", "apiUrl": "/roles/${id}/permissions", "comment": "管理员角色管理/通过ID获取管理员角色权限"}, {"name": "apiAdminRoleAssignPermissions", "method": "POST", "url": "/roles/_p_/permissions", "docUrl": "/admin/roles/:id/permissions", "apiUrl": "/roles/${id}/permissions", "comment": "管理员角色管理/为管理员角色分配权限（通过权限ID）"}, {"name": "apiAdminRoleSetActive", "method": "PATCH", "url": "/roles/_p_/active", "docUrl": "/admin/roles/:id/active", "apiUrl": "/roles/${id}/active?active=${active}", "comment": "管理员角色管理/设置管理员角色激活状态"}, {"name": "apiAdminRoleGetByCode", "method": "GET", "url": "/roles/code/_p_", "docUrl": "/admin/roles/code/:code", "apiUrl": "/roles/code/${code}", "comment": "管理员角色管理/通过代码获取管理员角色"}, {"name": "apiAdminPermissionList", "method": "GET", "url": "/permissions", "docUrl": "/admin/permissions", "apiUrl": "/permissions", "comment": "管理员权限管理/获取所有管理员权限"}, {"name": "apiAdminPermissionPageList", "method": "POST", "url": "/permissions/page", "docUrl": "/admin/permissions/page", "apiUrl": "/permissions/page", "comment": "管理员权限管理/分页获取管理员权限"}, {"name": "apiAdminPermissionCreate", "method": "POST", "url": "/permissions", "docUrl": "/admin/permissions", "apiUrl": "/permissions", "comment": "管理员权限管理/创建新的管理员权限"}, {"name": "apiAdminPermissionUpdate", "method": "PUT", "url": "/permissions/_p_", "docUrl": "/admin/permissions/:id", "apiUrl": "/permissions/${id}", "comment": "管理员权限管理/更新管理员权限"}, {"name": "apiAdminPermissionGetById", "method": "GET", "url": "/permissions/_p_", "docUrl": "/admin/permissions/:id", "apiUrl": "/permissions/${id}", "comment": "管理员权限管理/通过ID获取管理员权限"}, {"name": "apiAdminPermissionSetActive", "method": "PATCH", "url": "/permissions/_p_/active", "docUrl": "/admin/permissions/:id/active", "apiUrl": "/permissions/${id}/active?active=${active}", "comment": "管理员权限管理/设置管理员权限的激活状态"}, {"name": "apiAdminPermissionGetByCode", "method": "GET", "url": "/permissions/code/_p_", "docUrl": "/admin/permissions/code/:code", "apiUrl": "/permissions/code/${code}", "comment": "管理员权限管理/通过代码获取管理员权限"}, {"name": "apiCurrencyPageList", "method": "POST", "url": "/currencies/page", "docUrl": "/admin/currencies/page", "apiUrl": "/currencies/page", "comment": "管理员货币管理/分页获取所有货币"}, {"name": "apiCurrencyCreate", "method": "POST", "url": "/currencies", "docUrl": "/admin/currencies", "apiUrl": "/currencies", "comment": "管理员货币管理/创建新货币"}, {"name": "apiCurrencyUpdate", "method": "PUT", "url": "/currencies/_p_", "docUrl": "/admin/currencies/:code", "apiUrl": "/currencies/${code}", "comment": "管理员货币管理/更新现有货币"}, {"name": "apiCurrencySetDefault", "method": "PATCH", "url": "/currencies/_p_/default", "docUrl": "/admin/currencies/:code/default", "apiUrl": "/currencies/${code}/default", "comment": "管理员货币管理/将货币设置为默认货币"}, {"name": "apiCurrencySetActive", "method": "PATCH", "url": "/currencies/_p_/active", "docUrl": "/admin/currencies/:code/active", "apiUrl": "/currencies/${code}/active?active=${active}", "comment": "管理员货币管理/激活或停用货币"}, {"name": "apiLanguagePageList", "method": "POST", "url": "/languages/page", "docUrl": "/admin/languages/page", "apiUrl": "/languages/page", "comment": "管理员语言管理/分页获取所有语言"}, {"name": "apiLanguageCreate", "method": "POST", "url": "/languages", "docUrl": "/admin/languages", "apiUrl": "/languages", "comment": "管理员语言管理/创建新语言"}, {"name": "apiLanguageUpdate", "method": "PUT", "url": "/languages/_p_", "docUrl": "/admin/languages/:code", "apiUrl": "/languages/${code}", "comment": "管理员语言管理/更新现有语言"}, {"name": "apiLanguageSetDefault", "method": "PATCH", "url": "/languages/_p_/default", "docUrl": "/admin/languages/:code/default", "apiUrl": "/languages/${code}/default", "comment": "管理员语言管理/设置默认语言"}, {"name": "apiLanguageSetActive", "method": "PATCH", "url": "/languages/_p_/active", "docUrl": "/admin/languages/:code/active", "apiUrl": "/languages/${code}/active?active=${active}", "comment": "管理员语言管理/激活或停用语言"}, {"name": "apiRegionPageList", "method": "POST", "url": "/regions/page", "docUrl": "/admin/regions/page", "apiUrl": "/regions/page", "comment": "管理员区域管理/分页获取所有区域"}, {"name": "apiRegionCreate", "method": "POST", "url": "/regions", "docUrl": "/admin/regions", "apiUrl": "/regions", "comment": "管理员区域管理/创建新区域"}, {"name": "apiRegionUpdate", "method": "PUT", "url": "/regions/_p_", "docUrl": "/admin/regions/:id", "apiUrl": "/regions/${code}", "comment": "管理员区域管理/更新区域"}, {"name": "apiRegionSetActive", "method": "PATCH", "url": "/regions/_p_/active", "docUrl": "/admin/regions/:id/active", "apiUrl": "/regions/${id}/active?active=${active}", "comment": "管理员区域管理/设置区域的激活状态"}, {"name": "apiTranslationPageList", "method": "POST", "url": "/translations/page", "docUrl": "/admin/translations/page", "apiUrl": "/translations/page", "comment": "管理员翻译管理/获取所有翻译（分页）"}, {"name": "apiTranslationCreate", "method": "POST", "url": "/translations/create", "docUrl": "/admin/translations/create", "apiUrl": "/translations/create", "comment": "管理员翻译管理/为多种语言创建新的翻译键"}, {"name": "apiTranslationUpdate", "method": "POST", "url": "/translations/update", "docUrl": "/admin/translations/update", "apiUrl": "/translations/update", "comment": "管理员翻译管理/更新或创建特定键在多种语言中的翻译"}, {"name": "apiTranslationsByKey", "method": "GET", "url": "/translations/by-key/_p_", "docUrl": "/admin/translations/by-key/:key", "apiUrl": "/translations/by-key/${key}", "comment": "管理员翻译管理/获取特定键在所有语言中的翻译"}, {"name": "apiArticlePageList", "method": "POST", "url": "/articles/page", "docUrl": "/admin/articles/page", "apiUrl": "/articles/page", "comment": "管理员文章管理/分页获取所有文章"}, {"name": "apiArticleGetById", "method": "GET", "url": "/articles/_p_", "docUrl": "/admin/articles/:id", "apiUrl": "/articles/${id}", "comment": "管理员文章管理/通过ID获取文章"}, {"name": "apiArticleCreate", "method": "POST", "url": "/articles", "docUrl": "/admin/articles", "apiUrl": "/articles", "comment": "管理员文章管理/创建新文章"}, {"name": "apiArticleUpdate", "method": "PUT", "url": "/articles/_p_", "docUrl": "/admin/articles/:id", "apiUrl": "/articles/${id}", "comment": "管理员文章管理/更新文章"}, {"name": "apiBrandPageList", "method": "POST", "url": "/brands/page", "docUrl": "/admin/brands/page", "apiUrl": "/brands/page", "comment": "管理员产品品牌管理/分页获取所有品牌"}, {"name": "apiBrandGetById", "method": "GET", "url": "/brands/_p_", "docUrl": "/admin/brands/:id", "apiUrl": "/brands/${id}", "comment": "管理员产品品牌管理/根据ID获取品牌"}, {"name": "apiBrandCreate", "method": "POST", "url": "/brands", "docUrl": "/admin/brands", "apiUrl": "/brands", "comment": "管理员产品品牌管理/创建新品牌"}, {"name": "apiBrandUpdate", "method": "PUT", "url": "/brands/_p_", "docUrl": "/admin/brands/:id", "apiUrl": "/brands/${id}", "comment": "管理员产品品牌管理/更新品牌"}, {"name": "apiBrandDelete", "method": "DELETE", "url": "/brands/_p_", "docUrl": "/admin/brands/:id", "apiUrl": "/brands/${id}", "comment": "管理员产品品牌管理/删除品牌"}, {"name": "apiBrandSetActive", "method": "PATCH", "url": "/brands/_p_/active", "docUrl": "/admin/brands/:id/active", "apiUrl": "/brands/${id}/active?active=${active}", "comment": "管理员产品品牌管理/设置品牌激活状态"}, {"name": "apiBrandSetFeatured", "method": "PATCH", "url": "/brands/_p_/featured", "docUrl": "/admin/brands/:id/featured", "apiUrl": "/brands/${id}/featured?featured=${featured}", "comment": "管理员产品品牌管理/设置品牌推荐状态"}, {"name": "apiBrandGetByCategory", "method": "POST", "url": "/brands/category/_p_/page", "docUrl": "/admin/brands/category/:categoryId/page", "apiUrl": "/brands/category/${categoryId}/page", "comment": "管理员产品品牌管理/分页获取分类下的品牌"}, {"name": "apiBrandAddCategory", "method": "POST", "url": "/brands/_p_/categories/_p_", "docUrl": "/admin/brands/:brandId/categories/:categoryId", "apiUrl": "/brands/${brandId}/categories/${categoryId}", "comment": "管理员产品品牌管理/将分类添加到品牌"}, {"name": "apiBrandRemoveCategory", "method": "DELETE", "url": "/brands/_p_/categories/_p_", "docUrl": "/admin/brands/:brandId/categories/:categoryId", "apiUrl": "/brands/${brandId}/categories/${categoryId}", "comment": "管理员产品品牌管理/从品牌中移除分类"}, {"name": "apiCategoryPageList", "method": "POST", "url": "/categories/page", "docUrl": "/admin/categories/page", "apiUrl": "/categories/page", "comment": "产品分类管理/分页获取所有分类"}, {"name": "apiCategoryGetById", "method": "GET", "url": "/categories/_p_", "docUrl": "/admin/categories/:id", "apiUrl": "/categories/${id}", "comment": "产品分类管理/根据ID获取分类"}, {"name": "apiCategoryCreate", "method": "POST", "url": "/categories", "docUrl": "/admin/categories", "apiUrl": "/categories", "comment": "产品分类管理/创建新分类"}, {"name": "apiCategoryUpdate", "method": "PUT", "url": "/categories/_p_", "docUrl": "/admin/categories/:id", "apiUrl": "/categories/${id}", "comment": "产品分类管理/更新分类"}, {"name": "apiCategoryDelete", "method": "DELETE", "url": "/categories/_p_", "docUrl": "/admin/categories/:id", "apiUrl": "/categories/${id}", "comment": "产品分类管理/删除分类"}, {"name": "apiCategoryTree", "method": "GET", "url": "/categories/tree", "docUrl": "/admin/categories/tree", "apiUrl": "/categories/tree?includeInactive=${includeInactive}", "comment": "产品分类管理/获取完整分类树"}, {"name": "apiCategorySubtree", "method": "GET", "url": "/categories/tree/_p_", "docUrl": "/admin/categories/tree/:rootId", "apiUrl": "/categories/tree/${rootId}?includeInactive=${includeInactive}", "comment": "产品分类管理/获取分类子树"}, {"name": "apiCategoryGetByCode", "method": "GET", "url": "/categories/code/_p_", "docUrl": "/admin/categories/code/:code", "apiUrl": "/categories/code/${code}", "comment": "产品分类管理/根据编码获取分类"}, {"name": "apiCategoryRootsPage", "method": "POST", "url": "/categories/roots/page", "docUrl": "/admin/categories/roots/page", "apiUrl": "/categories/roots/page", "comment": "产品分类管理/分页获取根分类"}, {"name": "apiCategoryByParentId", "method": "POST", "url": "/categories/parent/_p_/page", "docUrl": "/admin/categories/parent/:parentId/page", "apiUrl": "/categories/parent/${parentId}/page", "comment": "产品分类管理/根据父级ID分页获取分类"}, {"name": "apiCategoryByLevel", "method": "POST", "url": "/categories/level/_p_/page", "docUrl": "/admin/categories/level/:level/page", "apiUrl": "/categories/level/${level}/page", "comment": "产品分类管理/根据层级分页获取分类"}, {"name": "apiCategorySetActive", "method": "PATCH", "url": "/categories/_p_/active", "docUrl": "/admin/categories/:id/active", "apiUrl": "/categories/${id}/active?isActive=${isActive}", "comment": "产品分类管理/设置分类激活状态"}, {"name": "apiExchangeRatesByCurrency", "method": "GET", "url": "/currencies/_p_/exchange-rates", "docUrl": "/admin/currencies/:code/exchange-rates", "apiUrl": "/currencies/${currencyCode}/exchange-rates", "comment": "管理员货币管理/获取特定货币的汇率"}, {"name": "apiExchangeRateAddRule", "method": "POST", "url": "/currencies/exchange-rates/_p_/_p_/rules", "docUrl": "/admin/currencies/exchange-rates/:fromCurrency/:toCurrency/rules", "apiUrl": "/currencies/exchange-rates/${fromCurrency}/${toCurrency}/rules", "comment": "管理员货币管理/为汇率添加规则"}, {"name": "apiExchangeRateApplyRule", "method": "PUT", "url": "/currencies/exchange-rates/_p_/_p_/rules/_p_", "docUrl": "/admin/currencies/exchange-rates/:fromCurrency/:toCurrency/rules/:ruleId", "apiUrl": "/currencies/exchange-rates/${fromCurrency}/${toCurrency}/rules/${ruleId}", "comment": "管理员货币管理/将已有规则应用到汇率上"}, {"name": "apiProductGetById", "method": "GET", "url": "/products/_p_", "docUrl": "/admin/products/:id", "apiUrl": "/products/${id}", "comment": "产品管理/通过ID获取产品"}, {"name": "apiProductGetDetails", "method": "GET", "url": "/products/_p_", "docUrl": "/admin/products/:id", "apiUrl": "/products/${id}", "comment": "产品管理/通过ID获取产品"}, {"name": "apiProductUpdate", "method": "PUT", "url": "/products/_p_", "docUrl": "/admin/products/:id", "apiUrl": "/products/${id}", "comment": "产品管理/更新产品"}, {"name": "apiProductDelete", "method": "DELETE", "url": "/products/_p_", "docUrl": "/admin/products/:id", "apiUrl": "/products/${id}", "comment": "产品管理/删除产品"}, {"name": "apiProductSetFeatured", "method": "PUT", "url": "/products/_p_/featured/_p_", "docUrl": "/admin/products/:id/featured/:isFeatured", "apiUrl": "/products/${id}/featured/${isFeatured}", "comment": "产品管理/设置产品精选状态"}, {"name": "apiProductSetActive", "method": "PUT", "url": "/products/_p_/active/_p_", "docUrl": "/admin/products/:id/active/:isActive", "apiUrl": "/products/${id}/active/${isActive}", "comment": "产品管理/设置产品活动状态"}, {"name": "apiProductCreate", "method": "POST", "url": "/products", "docUrl": "/admin/products", "apiUrl": "/products", "comment": "产品管理/创建新产品"}, {"name": "apiProductPageList", "method": "POST", "url": "/products/page", "docUrl": "/admin/products/page", "apiUrl": "/products/page", "comment": "产品管理/分页获取所有产品"}, {"name": "apiClientUserPageList", "method": "POST", "url": "/client-users/page", "docUrl": "/admin/client-users/page", "apiUrl": "/client-users/page", "comment": "客户用户管理/分页获取客户用户"}, {"name": "apiClientUserGetAll", "method": "GET", "url": "/client-users", "docUrl": "/admin/client-users", "apiUrl": "/client-users", "comment": "客户用户管理/获取所有客户用户"}, {"name": "apiClientUserGetAllActive", "method": "GET", "url": "/client-users/active", "docUrl": "/admin/client-users/active", "apiUrl": "/client-users/active", "comment": "客户用户管理/获取所有活跃客户用户"}, {"name": "apiClientUserGetById", "method": "GET", "url": "/client-users/_p_", "docUrl": "/admin/client-users/:id", "apiUrl": "/client-users/${id}", "comment": "客户用户管理/根据ID获取客户用户"}, {"name": "apiClientUserCreate", "method": "POST", "url": "/client-users", "docUrl": "/admin/client-users", "apiUrl": "/client-users", "comment": "客户用户管理/创建新客户用户"}, {"name": "apiClientUserUpdate", "method": "PUT", "url": "/client-users/_p_", "docUrl": "/admin/client-users/:id", "apiUrl": "/client-users/${id}", "comment": "客户用户管理/更新客户用户信息"}, {"name": "apiClientUserResetPassword", "method": "POST", "url": "/client-users/_p_/reset-password", "docUrl": "/admin/client-users/:id/reset-password", "apiUrl": "/client-users/${id}/reset-password", "comment": "客户用户管理/重置客户用户密码"}, {"name": "apiClientUserSetActive", "method": "PATCH", "url": "/client-users/_p_/active", "docUrl": "/admin/client-users/:id/active", "apiUrl": "/client-users/${id}/active?active=${active}", "comment": "客户用户管理/设置客户用户活跃状态"}, {"name": "apiWalletGetUserWallets", "method": "GET", "url": "/wallets/user/_p_", "docUrl": "/admin/wallets/user/:userId", "apiUrl": "/wallets/user/${userId}", "comment": "管理员钱包管理/获取用户的所有钱包"}, {"name": "apiWalletGetUserActiveWallets", "method": "GET", "url": "/wallets/user/_p_/active", "docUrl": "/admin/wallets/user/:userId/active", "apiUrl": "/wallets/user/${userId}/active", "comment": "管理员钱包管理/获取用户的激活钱包"}, {"name": "apiWalletGetUserMainWallet", "method": "GET", "url": "/wallets/user/_p_/main", "docUrl": "/admin/wallets/user/:userId/main", "apiUrl": "/wallets/user/${userId}/main", "comment": "管理员钱包管理/获取用户的主钱包"}, {"name": "apiWalletGetUserWalletByCurrency", "method": "GET", "url": "/wallets/user/_p_/currency/_p_", "docUrl": "/admin/wallets/user/:userId/currency/:currency", "apiUrl": "/wallets/user/${userId}/currency/${currency}", "comment": "管理员钱包管理/获取用户的特定货币钱包"}, {"name": "apiWalletCreateDefaultWallet", "method": "POST", "url": "/wallets/user/_p_/default", "docUrl": "/admin/wallets/user/:userId/default", "apiUrl": "/wallets/user/${userId}/default?currency=${currency}", "comment": "管理员钱包管理/为用户创建默认钱包"}, {"name": "apiWalletDeposit", "method": "POST", "url": "/wallets/_p_/deposit", "docUrl": "/admin/wallets/:id/deposit", "apiUrl": "/wallets/${id}/deposit?amount=${amount}&referenceId=${referenceId}", "comment": "管理员钱包管理/钱包充值"}, {"name": "apiWalletWithdraw", "method": "POST", "url": "/wallets/_p_/withdraw", "docUrl": "/admin/wallets/:id/withdraw", "apiUrl": "/wallets/${id}/withdraw?amount=${amount}&referenceId=${referenceId}", "comment": "管理员钱包管理/钱包提现"}, {"name": "apiWalletGetTransactionsPage", "method": "GET", "url": "/wallets/_p_/transactions/page", "docUrl": "/admin/wallets/:id/transactions/page", "apiUrl": "/wallets/${walletId}/transactions/page", "comment": "管理员钱包管理/分页获取钱包交易记录"}, {"name": "apiWalletGetPendingDeposits", "method": "GET", "url": "/wallets/user/_p_/pending-deposits", "docUrl": "/admin/wallets/user/:userId/pending-deposits", "apiUrl": "/wallets/user/${userId}/pending-deposits", "comment": "管理员钱包管理/获取用户的待审批存款申请"}, {"name": "apiOrderGetAllOrders", "method": "POST", "url": "/orders/page", "docUrl": "/admin/orders/page", "apiUrl": "/orders/page", "comment": "管理员订单管理/分页获取所有订单"}, {"name": "apiOrderGetOrderDetails", "method": "GET", "url": "/orders/_p_", "docUrl": "/admin/orders/:orderId", "apiUrl": "/orders/${orderId}", "comment": "管理员订单管理/获取订单详情"}, {"name": "apiOrderGetOrderByOrderNumber", "method": "GET", "url": "/orders/number/_p_", "docUrl": "/admin/orders/number/:orderNumber", "apiUrl": "/orders/number/${orderNumber}", "comment": "管理员订单管理/根据订单号获取订单"}, {"name": "apiOrderUpdateOrderStatus", "method": "PUT", "url": "/orders/_p_/status", "docUrl": "/admin/orders/:orderId/status", "apiUrl": "/orders/${orderId}/status?status=${status}", "comment": "管理员订单管理/更新订单状态"}, {"name": "apiOrderUpdatePaymentStatus", "method": "PUT", "url": "/orders/_p_/payment-status", "docUrl": "/admin/orders/:orderId/payment-status", "apiUrl": "/orders/${orderId}/payment-status?status=${status}", "comment": "管理员订单管理/更新订单支付状态"}, {"name": "apiOrderAddAdminNotes", "method": "PUT", "url": "/orders/_p_/admin-notes", "docUrl": "/admin/orders/:orderId/admin-notes", "apiUrl": "/orders/${orderId}/admin-notes", "comment": "管理员订单管理/添加管理员备注"}, {"name": "apiOrderPlatformReceive", "method": "POST", "url": "/orders/_p_/platform-receive", "docUrl": "/admin/orders/:orderId/platform-receive", "apiUrl": "/orders/${orderId}/platform-receive", "comment": "管理员订单管理/平台收货"}, {"name": "apiOrderPlatformShip", "method": "POST", "url": "/orders/_p_/platform-ship", "docUrl": "/admin/orders/:orderId/platform-ship", "apiUrl": "/orders/${orderId}/platform-ship", "comment": "管理员订单管理/平台发货到买家"}, {"name": "apiOrderPlatformVerify", "method": "POST", "url": "/orders/_p_/platform-verify", "docUrl": "/admin/orders/:orderId/platform-verify", "apiUrl": "/orders/${orderId}/platform-verify", "comment": "管理员订单管理/平台验证完成"}, {"name": "apiOrderPlatformReceiveReturn", "method": "POST", "url": "/orders/_p_/platform-return-to-seller", "docUrl": "/admin/orders/:orderId/platform-return-to-seller", "apiUrl": "/orders/${orderId}/platform-return-to-seller", "comment": "管理员订单管理/平台确认退货并退回卖家"}, {"name": "apiPriceQuoteGetAll", "method": "POST", "url": "/price-quotes/page", "docUrl": "/admin/price-quotes/page", "apiUrl": "/price-quotes/page", "comment": "报价管理/分页获取所有报价"}, {"name": "apiPriceQuoteGetDetails", "method": "GET", "url": "/price-quotes/_p_", "docUrl": "/admin/price-quotes/:quoteId", "apiUrl": "/price-quotes/${quoteId}", "comment": "报价管理/获取报价详情"}, {"name": "apiPriceQuoteUpdateStatus", "method": "PUT", "url": "/price-quotes/_p_/status", "docUrl": "/admin/price-quotes/:quoteId/status", "apiUrl": "/price-quotes/${quoteId}/status?status=${status}", "comment": "报价管理/更新报价状态"}, {"name": "apiPriceQuoteGetPage", "method": "POST", "url": "/price-quotes/page", "docUrl": "/admin/price-quotes/page", "apiUrl": "/price-quotes/page", "comment": "报价管理/分页获取所有报价"}, {"name": "apiRemittanceGetPendingDepositsPage", "method": "POST", "url": "/wallets/remittance-deposits/pending/page", "docUrl": "/admin/wallets/remittance-deposits/pending/page", "apiUrl": "/wallets/remittance-deposits/pending/page", "comment": "管理员钱包管理/分页获取所有待审核的汇款充值记录"}, {"name": "apiRemittanceApproveDeposit", "method": "POST", "url": "/wallets/transactions/_p_/approve", "docUrl": "/admin/wallets/transactions/:transactionId/approve", "apiUrl": "/wallets/transactions/${transactionId}/approve", "comment": "管理员钱包管理/批准存款申请"}, {"name": "apiRemittanceRejectDeposit", "method": "POST", "url": "/wallets/transactions/_p_/reject", "docUrl": "/admin/wallets/transactions/:transactionId/reject", "apiUrl": "/wallets/transactions/${transactionId}/reject", "comment": "管理员钱包管理/拒绝存款申请"}, {"name": "apiSystemSettingGetById", "method": "GET", "url": "/system/settings/_p_", "docUrl": "/admin/system/settings/:id", "apiUrl": "/system/settings/${id}", "comment": "系统设置管理/根据ID获取系统设置"}, {"name": "apiSystemSettingUpdate", "method": "PUT", "url": "/system/settings/_p_", "docUrl": "/admin/system/settings/:id", "apiUrl": "/system/settings/${id}", "comment": "系统设置管理/更新现有系统设置"}, {"name": "apiSystemSettingDelete", "method": "DELETE", "url": "/system/settings/_p_", "docUrl": "/admin/system/settings/:id", "apiUrl": "/system/settings/${id}", "comment": "系统设置管理/删除系统设置"}, {"name": "apiSystemSettingGetByKey", "method": "GET", "url": "/system/settings/key/_p_", "docUrl": "/admin/system/settings/key/:key", "apiUrl": "/system/settings/key/${key}", "comment": "系统设置管理/根据键获取系统设置"}, {"name": "apiSystemSettingGetAll", "method": "GET", "url": "/system/settings", "docUrl": "/admin/system/settings", "apiUrl": "/system/settings", "comment": "系统设置管理/获取所有系统设置"}, {"name": "apiSystemSettingCreate", "method": "POST", "url": "/system/settings", "docUrl": "/admin/system/settings", "apiUrl": "/system/settings", "comment": "系统设置管理/创建新的系统设置"}, {"name": "apiSystemSettingPageList", "method": "POST", "url": "/system/settings/page", "docUrl": "/admin/system/settings/page", "apiUrl": "/system/settings/page", "comment": "系统设置管理/分页获取系统设置"}, {"name": "apiSystemSettingSetActive", "method": "PATCH", "url": "/system/settings/_p_/active", "docUrl": "/admin/system/settings/:id/active", "apiUrl": "/system/settings/${id}/active?active=${active}", "comment": "系统设置管理/设置系统设置激活状态"}, {"name": "apiSystemSettingGetByGroup", "method": "GET", "url": "/system/settings/group/_p_", "docUrl": "/admin/system/settings/group/:group", "apiUrl": "/system/settings/group/${group}", "comment": "系统设置管理/根据分组获取系统设置"}, {"name": "apiGetTagDetails", "method": "GET", "url": "/tags/_p_", "docUrl": "/admin/tags/:tagId", "apiUrl": "/tags/${tagId}", "comment": "管理员标签管理/获取标签详情"}, {"name": "apiUpdateTag", "method": "PUT", "url": "/tags/_p_", "docUrl": "/admin/tags/:tagId", "apiUrl": "/tags/${tagId}", "comment": "管理员标签管理/更新标签"}, {"name": "apiCreateTag", "method": "POST", "url": "/tags", "docUrl": "/admin/tags", "apiUrl": "/tags", "comment": "管理员标签管理/创建新标签"}, {"name": "apiGetAllTags", "method": "POST", "url": "/tags/page", "docUrl": "/admin/tags/page", "apiUrl": "/tags/page", "comment": "管理员标签管理/分页获取所有标签"}, {"name": "apiGetProductsByTagId", "method": "GET", "url": "/tags/_p_/products", "docUrl": "/admin/tags/:tagId/products", "apiUrl": "/tags/${tagId}/products", "comment": "管理员标签管理/获取标签关联的所有产品"}, {"name": "apiUpdateProductsForTag", "method": "PUT", "url": "/tags/_p_/products", "docUrl": "/admin/tags/:tagId/products", "apiUrl": "/tags/${tagId}/products", "comment": "管理员标签管理/更新标签关联的产品"}, {"name": "apiExchangeRateRuleCreate", "method": "POST", "url": "/currencies/exchange-rate-rules", "docUrl": "/admin/currencies/exchange-rate-rules", "apiUrl": "/currencies/exchange-rate-rules", "comment": "管理员货币管理/创建新的汇率规则"}, {"name": "apiExchangeRateRuleUpdate", "method": "PUT", "url": "/currencies/exchange-rate-rules/_p_", "docUrl": "/admin/currencies/exchange-rate-rules/:id", "apiUrl": "/currencies/exchange-rate-rules/${id}", "comment": "管理员货币管理/更新现有汇率规则"}, {"name": "apiExchangeRateRulePageList", "method": "POST", "url": "/currencies/exchange-rate-rules/page", "docUrl": "/admin/currencies/exchange-rate-rules/page", "apiUrl": "/currencies/exchange-rate-rules/page", "comment": "管理员货币管理/分页获取汇率规则"}, {"name": "apiNotificationTemplateGet", "method": "GET", "url": "/notification-templates/_p_", "docUrl": "/admin/notification-templates/:id", "apiUrl": "/notification-templates/${id}", "comment": "通知模板管理/获取通知模板详情"}, {"name": "apiNotificationTemplateUpdate", "method": "PUT", "url": "/notification-templates/_p_", "docUrl": "/admin/notification-templates/:id", "apiUrl": "/notification-templates/${id}", "comment": "通知模板管理/更新通知模板"}, {"name": "apiNotificationTemplateEnable", "method": "PUT", "url": "/notification-templates/_p_/enable", "docUrl": "/admin/notification-templates/:id/enable", "apiUrl": "/notification-templates/${id}/enable", "comment": "通知模板管理/启用通知模板"}, {"name": "apiNotificationTemplateDisable", "method": "PUT", "url": "/notification-templates/_p_/disable", "docUrl": "/admin/notification-templates/:id/disable", "apiUrl": "/notification-templates/${id}/disable", "comment": "通知模板管理/禁用通知模板"}, {"name": "apiNotificationTemplatePage", "method": "POST", "url": "/notification-templates/page", "docUrl": "/admin/notification-templates/page", "apiUrl": "/notification-templates/page", "comment": "通知模板管理/获取通知模板列表"}, {"name": "apiNotificationTemplateCreate", "method": "POST", "url": "/notification-templates", "docUrl": "/admin/notification-templates", "apiUrl": "/notification-templates", "comment": "通知模板管理/创建通知模板"}, {"name": "apiNotificationTemplateSend", "method": "POST", "url": "/notification-templates/send", "docUrl": "/admin/notification-templates/send", "apiUrl": "/notification-templates/send", "comment": "通知模板管理/使用模板发送通知"}, {"name": "apiNotificationTemplateBatchSend", "method": "POST", "url": "/notification-templates/batch-send", "docUrl": "/admin/notification-templates/batch-send", "apiUrl": "/notification-templates/batch-send", "comment": "通知模板管理/批量使用模板发送通知"}, {"name": "apiDeleteTranslationsByKey", "method": "DELETE", "url": "/translations/by-key/_p_", "docUrl": "/admin/translations/by-key/:key", "apiUrl": "/translations/by-key/${key}", "comment": "管理员翻译管理/删除特定键的所有翻译"}, {"name": "apiArticleDelete", "method": "DELETE", "url": "/articles/_p_", "docUrl": "/admin/articles/:id", "apiUrl": "/articles/${id}", "comment": "管理员文章管理/删除文章"}, {"name": "apiWalletGetTransactions", "method": "GET", "url": "/wallets/_p_/transactions", "docUrl": "/admin/wallets/:id/transactions", "apiUrl": "/wallets/${walletId}/transactions", "comment": "管理员钱包管理/获取钱包交易记录"}, {"name": "apiWalletApproveDeposit", "method": "POST", "url": "/wallets/transactions/_p_/approve", "docUrl": "/admin/wallets/transactions/:transactionId/approve", "apiUrl": "/wallets/transactions/${transactionId}/approve", "comment": "管理员钱包管理/批准存款申请"}, {"name": "apiWalletRejectDeposit", "method": "POST", "url": "/wallets/transactions/_p_/reject", "docUrl": "/admin/wallets/transactions/:transactionId/reject", "apiUrl": "/wallets/transactions/${transactionId}/reject", "comment": "管理员钱包管理/拒绝存款申请"}, {"name": "apiDeleteTag", "method": "DELETE", "url": "/tags/_p_", "docUrl": "/admin/tags/:tagId", "apiUrl": "/tags/${tagId}", "comment": "管理员标签管理/删除标签"}, {"name": "apiExchangeRateRuleDelete", "method": "DELETE", "url": "/currencies/exchange-rate-rules/_p_", "docUrl": "/admin/currencies/exchange-rate-rules/:id", "apiUrl": "/currencies/exchange-rate-rules/${id}", "comment": "管理员货币管理/删除汇率规则"}, {"name": "apiNotificationTemplateDelete", "method": "DELETE", "url": "/notification-templates/_p_", "docUrl": "/admin/notification-templates/:id", "apiUrl": "/notification-templates/${id}", "comment": "通知模板管理/删除通知模板"}]