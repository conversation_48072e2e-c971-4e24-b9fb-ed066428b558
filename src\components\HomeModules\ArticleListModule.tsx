import React, { useState, useEffect } from 'react';
import { Button, Modal, Form, Input, List, Space, Image, Card, Row, Col, message } from 'antd';
import { DeleteOutlined, RightOutlined } from '@ant-design/icons';
import { TArticleListModule } from './types';
import BaseModuleWrapper from './BaseModuleWrapper';
import { apiArticlePageList } from '@/apis/apis.api';
import { useModel } from 'foca';
import { homeSettingModel } from '@/stores/homeSettingModel';
import I18nField from '../I18nField';

interface ArticleListModuleProps {
  module: TArticleListModule;
  isFirst: boolean;
  isLast: boolean;
  onUpdate: (updatedModule: TArticleListModule) => void;
  onDelete: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onInsertBefore: () => void;
  isEditing?: boolean;
}

const ArticleListModuleComponent: React.FC<ArticleListModuleProps> = ({
  module,
  isFirst,
  isLast,
  onUpdate,
  onDelete,
  onMoveUp,
  onMoveDown,
  onInsertBefore,
  isEditing: initialIsEditing
}) => {
  const homeSettingModalState = useModel(homeSettingModel);
  const [isEditing, setIsEditing] = useState(initialIsEditing || false);
  const [isModuleSettingVisible, setIsModuleSettingVisible] = useState(false);
  const [moduleForm] = Form.useForm();
  const [articleSelectVisible, setArticleSelectVisible] = useState(false);
  const [articleList, setArticleList] = useState<any[]>([]);
  const [articleLoading, setArticleLoading] = useState(false);
  const [selectedArticleIds, setSelectedArticleIds] = useState<string[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  useEffect(() => {
    homeSettingModel.loadArticleByIds(module.articles);
  }, [module.articles])


  const handleModuleSettings = () => {
    moduleForm.setFieldsValue({
      title: module.title,
      moreText: module.moreText,
      moreLink: module.moreLink
    });
    setIsModuleSettingVisible(true);
  };

  const handleModuleSettingsSubmit = () => {
    moduleForm.validateFields().then(values => {
      onUpdate({
        ...module,
        title: values.title,
        moreText: values.moreText,
        moreLink: values.moreLink
      });
      setIsModuleSettingVisible(false);
    });
  };

  const loadArticles = async (options?: {
    page?: number, pageSize?: number, keyword?: string
  }) => {
    const { page = 1, pageSize = 6, keyword } = options || {};
    setArticleLoading(true);
    try {

      const res = await apiArticlePageList({
        current: page - 1,
        size: pageSize,
        filters: keyword ? [{ field: 'name', operator: 'like', value: keyword }] : [],
        sorts: [],
      });
      setArticleList(res.records || []);
      setPagination({
        current: page,
        pageSize,
        total: res.total || 0
      });
      homeSettingModel.updateArticleMapByArticles(res.records || []);
    } catch (error) {
      console.error('加载文章列表失败', error);
      message.error('加载文章列表失败');
    } finally {
      setArticleLoading(false);
    }
  };

  const handleOpenArticleSelector = () => {
    setArticleSelectVisible(true);
    loadArticles();
    setSelectedArticleIds([...module.articles]);
  };

  const handleArticleSelect = (articleId: string) => {
    if (selectedArticleIds.includes(articleId)) {
      setSelectedArticleIds(selectedArticleIds.filter(id => id !== articleId));
    } else {
      setSelectedArticleIds([...selectedArticleIds, articleId]);
    }
  };

  const handleConfirmArticleSelection = () => {
    onUpdate({
      ...module,
      articles: Array.from(new Set([...selectedArticleIds]))
    });

    setArticleSelectVisible(false);
    setSelectedArticleIds([]);
    console.log("handleConfirmArticleSelection 12", isEditing);
  };

  useEffect(() => {
    console.log("isEditing", isEditing);
  }, [isEditing]);

  const handleDeleteItem = (itemId: string) => {
    const updatedItems = module.articles.filter(item => item !== itemId);
    onUpdate({
      ...module,
      articles: updatedItems
    });
  };

  const handleSearch = (key: string) => {
    loadArticles({ keyword: key });
  };

  const renderArticleListContent = () => {
    return (
      <div>
        <div className="flex justify-between mb-4">
          <div className="text-lg font-medium"  >{module.title['zh-CN'] || '文章列表'}</div>
          {module.moreText && (
            <a href={module.moreLink} className="text-blue-500 flex items-center">
              {module.moreText['zh-CN']} <RightOutlined />
            </a>
          )}
        </div>

        {module.articles.length === 0 ? (
          <div className="h-40 flex items-center justify-center bg-gray-100">
            <p className="text-gray-500">暂无文章</p>
          </div>
        ) : (
          <Row gutter={[16, 16]}>
            {module.articles.map(articleId => {
              const article = homeSettingModalState.articleMap[articleId];
              return (
                article ? <Col span={4} key={articleId}>
                  <Card
                    hoverable
                    cover={
                      <Image
                        alt={article.title}
                        src={article.headerImage}
                        className="h-40 object-cover"
                        preview={false}
                      />
                    }
                  >
                    <Card.Meta
                      title={article.title}
                    />
                  </Card>
                </Col> : null
              );
            })}
          </Row>
        )}
      </div>
    );
  };

  const renderEditContent = () => {
    return (
      <div>
        <div className="flex justify-between mb-4">
          <div className="text-lg font-medium">文章列表模块</div>
          <Space>
            <Button onClick={handleModuleSettings}>模块设置</Button>
            <Button type="primary" onClick={handleOpenArticleSelector}>
              从文章库选择
            </Button>
            <Button type="primary" onClick={() => setIsEditing(false)}>
              完成
            </Button>
          </Space>
        </div>

        <List
          itemLayout="horizontal"
          dataSource={module.articles}
          renderItem={item => {
            const article = homeSettingModel.state.articleMap[item];
            return article ? (
              <List.Item
                actions={[
                  <Button danger icon={<DeleteOutlined />} onClick={() => handleDeleteItem(item)}>删除</Button>
                ]}
              >
                <List.Item.Meta
                  avatar={<Image src={article.headerImage} width={80} height={80} />}
                  title={article.title}
                />
              </List.Item>
            ) : null;
          }}
        />

        <Modal
          title="选择文章"
          open={articleSelectVisible}
          onOk={handleConfirmArticleSelection}
          onCancel={() => setArticleSelectVisible(false)}
          width={800}
          okButtonProps={{ disabled: selectedArticleIds.length === 0 }}
        >
          <div className="mb-4 flex justify-between">

            <div>已选择 {selectedArticleIds.length} 个文章</div>
            <Input.Search
              className="w-1/2"
              placeholder="搜索文章"
              onSearch={handleSearch}
            />
          </div>
          <List
            itemLayout="horizontal"
            dataSource={articleList}
            loading={articleLoading}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              onChange: (page) => loadArticles({ page })
            }}
            renderItem={item => (
              <List.Item
                actions={[
                  <Button
                    type={selectedArticleIds.includes(item.id) ? "primary" : "default"}
                    onClick={() => handleArticleSelect(item.id)}
                  >
                    {selectedArticleIds.includes(item.id) ? "已选择" : "选择"}
                  </Button>
                ]}
              >
                <List.Item.Meta
                  avatar={<Image src={item.mainImage} width={60} height={60} />}
                  title={item.name}
                  description={`价格: ¥${item.price?.toFixed(2) || '0.00'} | 库存: ${item.stock || 0}`}
                />
              </List.Item>
            )}
          />
        </Modal>

        <Modal
          title="模块设置"
          open={isModuleSettingVisible}
          onOk={handleModuleSettingsSubmit}
          onCancel={() => setIsModuleSettingVisible(false)}
        >
          <Form form={moduleForm} layout="vertical">
            <Form.Item
              name="title"
              label="模块标题"
              rules={[{ required: true, message: '请输入模块标题' }]}
            >
              <I18nField>
                <Input placeholder="请输入模块标题" />
              </I18nField>

            </Form.Item>
            <Form.Item
              name="moreText"
              label="更多按钮文本"
            >
              <I18nField>
                <Input placeholder="请输入更多按钮文本" />
              </I18nField>

            </Form.Item>
            <Form.Item
              name="moreLink"
              label="更多按钮链接"
            >
              <Input placeholder="请输入更多按钮链接" />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    );
  };

  return (
    <BaseModuleWrapper
      isFirst={isFirst}
      isLast={isLast}
      onDelete={onDelete}
      onMoveUp={onMoveUp}
      onMoveDown={onMoveDown}
      onInsertBefore={onInsertBefore}
      isEditing={isEditing}
      onEditingChange={setIsEditing}
    >
      {isEditing ? renderEditContent() : renderArticleListContent()}
    </BaseModuleWrapper>
  );
};

export default ArticleListModuleComponent;
