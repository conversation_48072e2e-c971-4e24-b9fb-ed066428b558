import http from '@/utils/http';

// 此文件由 genApis 脚本自动生成，请勿手动修改
// 生成时间: 2025-08-03T05:03:02.071Z

/** 批量分配业务类型 */
export function apiBatchAssignBusinessType(data: TBatchAssignBusinessTypeBO) {
  return http.put<TBatchOperationResultDTO>(`/channel/mapping/business-types/batch/assign`, data);
}

/** 批量取消业务类型分配 */
export function apiBatchUnassignBusinessType(data: TBatchUnassignBO) {
  return http.put<TBatchOperationResultDTO>(`/channel/mapping/business-types/batch/unassign`, data);
}

/** 分页查询渠道业务类型映射 */
export function apiListChannelBusinessTypeMappings(data: TPageQuery) {
  return http.post<TPageResultChannelBusinessTypeMappingDTO>(`/channel/mapping/business-types/page`, data);
}

/** 根据ID获取渠道业务类型映射详情 */
export function apiGetChannelBusinessTypeMappingById(id: string) {
  return http.get<TChannelBusinessTypeMappingDTO>(`/channel/mapping/business-types/${id}`);
}

/** 为渠道分配业务类型 */
export function apiAssignBusinessType(id: string, businessTypeId: string) {
  return http.put<TChannelBusinessTypeMappingDTO>(`/channel/mapping/business-types/${id}/assign/${businessTypeId}`);
}

/** 取消渠道业务类型分配 */
export function apiUnassignBusinessType(id: string) {
  return http.put<TChannelBusinessTypeMappingDTO>(`/channel/mapping/business-types/${id}/unassign`);
}

/** 批量分配地理位置 */
export function apiBatchAssignLocation(data: TBatchAssignLocationBO) {
  return http.put<TBatchOperationResultDTO>(`/channel/mapping/locations/batch/assign`, data);
}

/** 批量取消地理位置分配 */
export function apiBatchUnassignLocation(data: TBatchUnassignBO) {
  return http.put<TBatchOperationResultDTO>(`/channel/mapping/locations/batch/unassign`, data);
}

/** 分页查询渠道地理位置映射 */
export function apiListChannelLocationMappings(data: TPageQuery) {
  return http.post<TPageResultChannelLocationMappingDTO>(`/channel/mapping/locations/page`, data);
}

/** 根据ID获取渠道地理位置映射详情 */
export function apiGetChannelLocationMappingById(id: string) {
  return http.get<TChannelLocationMappingDTO>(`/channel/mapping/locations/${id}`);
}

/** 为渠道分配地理位置 */
export function apiAssignLocation(id: string, locationId: string) {
  return http.put<TChannelLocationMappingDTO>(`/channel/mapping/locations/${id}/assign/${locationId}`);
}

/** 取消渠道地理位置分配 */
export function apiUnassignLocation(id: string) {
  return http.put<TChannelLocationMappingDTO>(`/channel/mapping/locations/${id}/unassign`);
}

/** 创建渠道信息 */
export function apiCreateChannel(data: TChannelInfoBO) {
  return http.post<TChannelDTO>(`/channels`, data);
}

/** 批量启用/禁用渠道 */
export function apiBatchToggleChannelsEnabled(data: TBatchToggleEnabledBO) {
  return http.put<TBatchOperationResultDTO>(`/channels/batch/toggle-enabled`, data);
}

/** 清除所有渠道缓存 */
export function apiClearAllChannelCache() {
  return http.delete<any>(`/channels/cache`);
}

/** 根据代码获取渠道信息 */
export function apiGetByCode(code: string) {
  return http.get<TChannelDTO>(`/channels/code/${code}`);
}

/** 获取所有启用的渠道 */
export function apiGetAllEnabled() {
  return http.get<any[]>(`/channels/enable`);
}

/** 获取超过请求限制的渠道 */
export function apiGetExceededRequestLimit() {
  return http.get<any[]>(`/channels/exceeded-limit`);
}

/** 分页查询渠道信息 */
export function apiListChannels(data: TPageQuery) {
  return http.post<TPageResultChannelDTO>(`/channels/page`, data);
}

/** 重置所有渠道的请求计数 */
export function apiResetAllRequestCounts() {
  return http.put<any>(`/channels/reset-request-counts`);
}

/** 根据名称搜索渠道 */
export function apiSearchByName(query?: { name?: any }) {
  return http.get<any[]>(`/channels/search`, query);
}

/** 根据渠道类型获取渠道列表 */
export function apiGetBySourceType(sourceType: string) {
  return http.get<any[]>(`/channels/source-type/${sourceType}`);
}

/** 更新渠道信息 */
export function apiUpdateChannel(id: string, data: TChannelInfoBO) {
  return http.put<TChannelDTO>(`/channels/${id}`, data);
}

/** 根据ID获取渠道信息详情 */
export function apiGetChannelById(id: string) {
  return http.get<TChannelDTO>(`/channels/${id}`);
}

/** 删除渠道信息 */
export function apiDeleteChannel(id: string) {
  return http.delete<any>(`/channels/${id}`);
}

/** 清除指定渠道缓存 */
export function apiClearChannelCache(id: string) {
  return http.delete<any>(`/channels/${id}/cache`);
}

/** 增加渠道请求计数 */
export function apiIncrementRequestCount(id: string) {
  return http.put<any>(`/channels/${id}/increment-request-count`);
}

/** 创建新的API应用 */
export function apiCreateApplication(data: TCreateApiApplicationRequest) {
  return http.post<TApiApplicationBO>(`/client/applications`, data);
}

/** 根据appId获取API应用 */
export function apiGetApplicationByAppId(appId: string) {
  return http.get<TApiApplicationBO>(`/client/applications/app/${appId}`);
}

/** 分页获取API应用 */
export function apiGetApplications(data: TPageQuery) {
  return http.post<TPageResultApiApplicationBO>(`/client/applications/page`, data);
}

/** 根据用户ID获取应用列表 */
export function apiGetApplicationsByUserId(userId: string) {
  return http.get<any[]>(`/client/applications/user/${userId}`);
}

/** 更新API应用 */
export function apiUpdateApplication(id: string, data: TUpdateApiApplicationRequest) {
  return http.put<TApiApplicationBO>(`/client/applications/${id}`, data);
}

/** 根据ID获取API应用 */
export function apiGetApplicationById(id: string) {
  return http.get<TApiApplicationBO>(`/client/applications/${id}`);
}

/** 删除API应用 */
export function apiDeleteApplication(id: string) {
  return http.delete<any>(`/client/applications/${id}`);
}

/** 重新生成应用密钥 */
export function apiRegenerateAppSecret(id: string) {
  return http.post<TApiApplicationBO>(`/client/applications/${id}/regenerate-secret`);
}

/** 获取应用完整密钥（敏感操作） */
export function apiGetApplicationSecret(id: string) {
  return http.get<string>(`/client/applications/${id}/secret`);
}

/** 创建新的客户用户交易 */
export function apiCreateTransaction(data: TCreateClientUserTransactionRequest) {
  return http.post<TClientUserTransactionBO>(`/client/transactions`, data);
}

/** 批量更新交易状态 */
export function apiBatchUpdateStatus(query?: { status?: any; transactionIds?: any }) {
  return http.put<Record<string, any>>(`/client/transactions/batch/status`, query);
}

/** 根据外部交易号获取交易 */
export function apiGetTransactionByExternalTransactionNo(externalTransactionNo: string) {
  return http.get<TClientUserTransactionBO>(`/client/transactions/external-transaction-no/${externalTransactionNo}`);
}

/** 分页获取客户用户交易 */
export function apiGetTransactions(data: TPageQuery) {
  return http.post<TPageResultClientUserTransactionBO>(`/client/transactions/page`, data);
}

/** 获取需要审核的交易列表 */
export function apiGetPendingApprovalTransactions() {
  return http.get<any[]>(`/client/transactions/pending-approval`);
}

/** 获取需要重试的交易列表 */
export function apiGetPendingRetryTransactions() {
  return http.get<any[]>(`/client/transactions/pending-retry`);
}

/** 统计指定状态的交易数量 */
export function apiGetTransactionCountByStatus(query?: { status?: any }) {
  return http.get<Record<string, any>>(`/client/transactions/statistics/count-by-status`, query);
}

/** 统计指定时间范围内的交易数量 */
export function apiGetTransactionCountByTimeRange(query?: { endTime?: any; startTime?: any }) {
  return http.get<Record<string, any>>(`/client/transactions/statistics/count-by-time-range`, query);
}

/** 统计指定类型的交易数量 */
export function apiGetTransactionCountByType(query?: { transactionType?: any }) {
  return http.get<Record<string, any>>(`/client/transactions/statistics/count-by-type`, query);
}

/** 统计用户交易数量 */
export function apiGetTransactionCountByUser(query?: { userId?: any }) {
  return http.get<Record<string, any>>(`/client/transactions/statistics/count-by-user`, query);
}

/** 根据状态获取交易列表 */
export function apiGetTransactionsByStatus(status: string) {
  return http.get<any[]>(`/client/transactions/status/${status}`);
}

/** 根据时间范围获取交易列表 */
export function apiGetTransactionsByTimeRange(query?: { endTime?: any; startTime?: any }) {
  return http.get<any[]>(`/client/transactions/time-range`, query);
}

/** 根据交易号获取交易 */
export function apiGetTransactionByTransactionNo(transactionNo: string) {
  return http.get<TClientUserTransactionBO>(`/client/transactions/transaction-no/${transactionNo}`);
}

/** 根据交易类型获取交易列表 */
export function apiGetTransactionsByType(transactionType: string) {
  return http.get<any[]>(`/client/transactions/type/${transactionType}`);
}

/** 根据用户ID获取交易列表 */
export function apiGetTransactionsByUserId(userId: string) {
  return http.get<any[]>(`/client/transactions/user/${userId}`);
}

/** 根据用户ID和交易类型获取交易列表 */
export function apiGetTransactionsByUserIdAndType(userId: string, transactionType: string) {
  return http.get<any[]>(`/client/transactions/user/${userId}/type/${transactionType}`);
}

/** 更新客户用户交易信息 */
export function apiUpdateTransaction(id: string, data: TUpdateClientUserTransactionRequest) {
  return http.put<TClientUserTransactionBO>(`/client/transactions/${id}`, data);
}

/** 根据ID获取客户用户交易 */
export function apiGetTransactionById(id: string) {
  return http.get<TClientUserTransactionBO>(`/client/transactions/${id}`);
}

/** 删除交易 */
export function apiDeleteTransaction(id: string) {
  return http.delete<Record<string, any>>(`/client/transactions/${id}`);
}

/** 审核交易 */
export function apiApproveTransaction(id: string, data: TransactionApprovalRequest) {
  return http.post<TClientUserTransactionBO>(`/client/transactions/${id}/approve`, data);
}

/** 重试交易 */
export function apiRetryTransaction(id: string) {
  return http.post<TClientUserTransactionBO>(`/client/transactions/${id}/retry`);
}

/** 创建新的客户用户 */
export function apiCreateClientUser(data: TCreateClientUserRequest) {
  return http.post<TClientUserBO>(`/client/users`, data);
}

/** 批量设置客户用户激活状态 */
export function apiBatchSetUserEnable(query?: { enabled?: any; userIds?: any }) {
  return http.put<Record<string, any>>(`/client/users/batchenable`, query);
}

/** 根据邮箱获取客户用户 */
export function apiGetUserByEmail(email: string) {
  return http.get<TClientUserBO>(`/client/users/email/${email}`);
}

/** 分页获取客户用户 */
export function apiGetClientUsers(data: TPageQuery) {
  return http.post<TPageResultClientUserBO>(`/client/users/page`, data);
}

/** 获取最近登录的用户 */
export function apiGetRecentlyLoggedInUsers(query?: { limit?: any }) {
  return http.get<any[]>(`/client/users/recent-logins`, query);
}

/** 获取指定时间段内注册的用户数量 */
export function apiGetRegistrationCount(query?: { endTime?: any; startTime?: any }) {
  return http.get<Record<string, any>>(`/client/users/statistics/registration-count`, query);
}

/** 获取激活用户数量 */
export function apiGetEnableUserCount() {
  return http.get<Record<string, any>>(`/client/users/statisticsenable-count`);
}

/** 根据用户名获取客户用户 */
export function apiGetUserByUsername(username: string) {
  return http.get<TClientUserBO>(`/client/users/username/${username}`);
}

/** 验证用户密码 */
export function apiValidatePassword(query?: { password?: any; username?: any }) {
  return http.post<Record<string, any>>(`/client/users/validate-password`, query);
}

/** 更新客户用户信息 */
export function apiUpdateClientUser(id: string, data: TUpdateClientUserRequest) {
  return http.put<TClientUserBO>(`/client/users/${id}`, data);
}

/** 根据ID获取客户用户 */
export function apiGetClientUserById(id: string) {
  return http.get<TClientUserBO>(`/client/users/${id}`);
}

/** 删除客户用户 */
export function apiDeleteUser(id: string) {
  return http.delete<Record<string, any>>(`/client/users/${id}`);
}

/** 更新用户最后登录信息 */
export function apiUpdateLastLoginInfo(id: string, query?: { loginIp?: any }) {
  return http.put<Record<string, any>>(`/client/users/${id}/login-info`, query);
}

/** 更新客户用户密码 */
export function apiUpdateClientUserPassword(id: string, data: TUpdateClientUserPasswordRequest) {
  return http.put<TClientUserBO>(`/client/users/${id}/password`, data);
}

/** 设置客户用户激活状态 */
export function apiSetClientUserEnable(id: string, query?: { enabled?: any }) {
  return http.put<TClientUserBO>(`/client/users/${id}enable`, query);
}

/** 创建新的客户用户钱包 */
export function apiCreateWallet(data: TCreateClientUserWalletRequest) {
  return http.post<TClientUserWalletBO>(`/client/wallets`, data);
}

/** 批量设置钱包激活状态 */
export function apiBatchSetWalletEnable(query?: { enabled?: any; walletIds?: any }) {
  return http.put<Record<string, any>>(`/client/wallets/batchenable`, query);
}

/** 分页获取客户用户钱包 */
export function apiGetWallets(data: TPageQuery) {
  return http.post<TPageResultClientUserWalletBO>(`/client/wallets/page`, data);
}

/** 获取指定用户的钱包数量 */
export function apiGetUserWalletCount(query?: { userId?: any }) {
  return http.get<Record<string, any>>(`/client/wallets/statistics/user-wallet-count`, query);
}

/** 获取激活钱包数量 */
export function apiGetEnableWalletCount() {
  return http.get<Record<string, any>>(`/client/wallets/statisticsenable-count`);
}

/** 根据用户ID获取钱包列表 */
export function apiGetWalletsByUserId(userId: string) {
  return http.get<any[]>(`/client/wallets/user/${userId}`);
}

/** 根据用户ID和货币获取钱包 */
export function apiGetWalletByUserIdAndCurrency(userId: string, currency: string) {
  return http.get<TClientUserWalletBO>(`/client/wallets/user/${userId}/currency/${currency}`);
}

/** 根据用户ID、货币和类型获取钱包 */
export function apiGetWalletByUserIdAndCurrencyAndType(userId: string, currency: string, type: string) {
  return http.get<TClientUserWalletBO>(`/client/wallets/user/${userId}/currency/${currency}/type/${type}`);
}

/** 获取或创建用户的主钱包 */
export function apiGetOrCreateMainWallet(userId: string, query?: { currency?: any }) {
  return http.post<TClientUserWalletBO>(`/client/wallets/user/${userId}/main-wallet`, query);
}

/** 更新客户用户钱包信息 */
export function apiUpdateWallet(id: string, data: TUpdateClientUserWalletRequest) {
  return http.put<TClientUserWalletBO>(`/client/wallets/${id}`, data);
}

/** 根据ID获取客户用户钱包 */
export function apiGetWalletById(id: string) {
  return http.get<TClientUserWalletBO>(`/client/wallets/${id}`);
}

/** 删除钱包 */
export function apiDeleteWallet(id: string) {
  return http.delete<Record<string, any>>(`/client/wallets/${id}`);
}

/** 减少钱包余额 */
export function apiDecreaseBalance(id: string, data: TWalletBalanceOperationRequest) {
  return http.post<Record<string, any>>(`/client/wallets/${id}/decrease-balance`, data);
}

/** 冻结金额 */
export function apiFreezeAmount(id: string, data: TWalletBalanceOperationRequest) {
  return http.post<Record<string, any>>(`/client/wallets/${id}/freeze`, data);
}

/** 增加钱包余额 */
export function apiIncreaseBalance(id: string, data: TWalletBalanceOperationRequest) {
  return http.post<Record<string, any>>(`/client/wallets/${id}/increase-balance`, data);
}

/** 解冻金额 */
export function apiUnfreezeAmount(id: string, data: TWalletBalanceOperationRequest) {
  return http.post<Record<string, any>>(`/client/wallets/${id}/unfreeze`, data);
}

/** 设置钱包激活状态 */
export function apiSetWalletEnable(id: string, query?: { enabled?: any }) {
  return http.put<TClientUserWalletBO>(`/client/wallets/${id}enable`, query);
}

/** 创建地理位置 */
export function apiCreateLocation(data: TLocationBO) {
  return http.post<TLocationDTO>(`/locations`, data);
}

/** 分页查询地理位置 */
export function apiListLocations(data: TPageQuery) {
  return http.post<TPageResultLocationDTO>(`/locations/page`, data);
}

/** 更新地理位置 */
export function apiUpdateLocation(id: string, data: TLocationBO) {
  return http.put<TLocationDTO>(`/locations/${id}`, data);
}

/** 根据ID获取地理位置详情 */
export function apiGetLocationById(id: string) {
  return http.get<TLocationDTO>(`/locations/${id}`);
}

/** 删除地理位置 */
export function apiDeleteLocation(id: string) {
  return http.delete<any>(`/locations/${id}`);
}

/** 账号登录 */
export function apiAdminLogin(data: TUsernameLoginRequest) {
  return http.post<TSessionInfoLongAdminUserBo>(`/login`, data);
}

/** 账号登出 */
export function apiLogout() {
  return http.post<any>(`/logout`);
}

/** 获取所有管理员权限 */
export function apiGetAllPermissions() {
  return http.get<any[]>(`/permissions`);
}

/** 创建新的管理员权限 */
export function apiCreatePermission(data: TCreateAdminPermissionRequest) {
  return http.post<TAdminPermissionBo>(`/permissions`, data);
}

/** 通过代码获取管理员权限 */
export function apiGetPermissionByCode(code: string) {
  return http.get<TAdminPermissionBo>(`/permissions/code/${code}`);
}

/** 分页获取管理员权限 */
export function apiGetPermissions(data: TPageQuery) {
  return http.post<TPageResultAdminPermissionBo>(`/permissions/page`, data);
}

/** 更新管理员权限 */
export function apiUpdatePermission(id: string, data: TCreateAdminPermissionRequest) {
  return http.put<TAdminPermissionBo>(`/permissions/${id}`, data);
}

/** 通过ID获取管理员权限 */
export function apiGetPermissionById(id: string) {
  return http.get<TAdminPermissionBo>(`/permissions/${id}`);
}

/** 设置管理员权限的激活状态 */
export function apiSetPermissionEnable(id: string, query?: { enabled?: any }) {
  return http.patch<TAdminPermissionBo>(`/permissions/${id}enable`, query);
}

/** 获取所有管理员角色 */
export function apiGetAllRoles() {
  return http.get<any[]>(`/roles`);
}

/** 创建新的管理员角色 */
export function apiCreateRole(data: TCreateAdminRoleRequest) {
  return http.post<TAdminRoleBo>(`/roles`, data);
}

/** 通过代码获取管理员角色 */
export function apiGetRoleByCode(code: string) {
  return http.get<TAdminRoleBo>(`/roles/code/${code}`);
}

/** 分页获取管理员角色 */
export function apiGetRoles(data: TPageQuery) {
  return http.post<TPageResultAdminRoleBo>(`/roles/page`, data);
}

/** 更新管理员角色 */
export function apiUpdateRole(id: string, data: TCreateAdminRoleRequest) {
  return http.put<TAdminRoleBo>(`/roles/${id}`, data);
}

/** 通过ID获取管理员角色 */
export function apiGetRoleById(id: string) {
  return http.get<TAdminRoleBo>(`/roles/${id}`);
}

/** 通过ID获取管理员角色权限 */
export function apiGetRolePermissions(id: string) {
  return http.get<any[]>(`/roles/${id}/permissions`);
}

/** 为管理员角色分配权限（通过权限ID） */
export function apiAssignPermissionsToRole(id: string, data: any[]) {
  return http.post<TAdminRoleBo>(`/roles/${id}/permissions`, data);
}

/** 为管理员角色分配权限（通过权限代码） */
export function apiAssignPermissionsToRoleByCodes(id: string, data: any[]) {
  return http.post<TAdminRoleBo>(`/roles/${id}/permissions/codes`, data);
}

/** 设置管理员角色激活状态 */
export function apiSetRoleEnable(id: string, query?: { enabled?: any }) {
  return http.patch<TAdminRoleBo>(`/roles/${id}enable`, query);
}

/** 从管理员角色中移除权限（通过权限代码） */
export function apiRemovePermissionFromRoleByCode(roleId: string, permissionCode: string) {
  return http.delete<TAdminRoleBo>(`/roles/${roleId}/permissions/code/${permissionCode}`);
}

/** 为管理员角色添加权限（通过权限代码） */
export function apiAddPermissionToRoleByCode(roleId: string, permissionCode: string) {
  return http.post<TAdminRoleBo>(`/roles/${roleId}/permissions/code/${permissionCode}`);
}

/** 从管理员角色中移除权限（通过权限ID） */
export function apiRemovePermissionFromRole(roleId: string, permissionId: string) {
  return http.delete<TAdminRoleBo>(`/roles/${roleId}/permissions/${permissionId}`);
}

/** 为管理员角色添加权限（通过权限ID） */
export function apiAddPermissionToRole(roleId: string, permissionId: string) {
  return http.post<TAdminRoleBo>(`/roles/${roleId}/permissions/${permissionId}`);
}

/** 获取当前登录用户 */
export function apiGetSessionUser() {
  return http.get<TSessionInfoLongAdminUserBo>(`/session`);
}

/** 创建业务类型 */
export function apiCreateStaticBusinessType(data: TStaticBusinessTypeBO) {
  return http.post<TStaticBusinessTypeDTO>(`/static/business-types`, data);
}

/** 分页查询业务类型 */
export function apiListStaticBusinessTypes(data: TPageQuery) {
  return http.post<TPageResultStaticBusinessTypeDTO>(`/static/business-types/page`, data);
}

/** 更新业务类型 */
export function apiUpdateStaticBusinessType(id: string, data: TStaticBusinessTypeBO) {
  return http.put<TStaticBusinessTypeDTO>(`/static/business-types/${id}`, data);
}

/** 根据ID获取业务类型详情 */
export function apiGetStaticBusinessTypeById(id: string) {
  return http.get<TStaticBusinessTypeDTO>(`/static/business-types/${id}`);
}

/** 删除业务类型 */
export function apiDeleteStaticBusinessType(id: string) {
  return http.delete<any>(`/static/business-types/${id}`);
}

/** 创建新的静态代理实例 */
export function apiCreateInstance(data: TCreateStaticInstanceRequest) {
  return http.post<TStaticInstanceBO>(`/static/instances`, data);
}

/** 分页获取静态代理实例 */
export function apiGetInstances(data: TPageQuery) {
  return http.post<TPageResultStaticInstanceBO>(`/static/instances/page`, data);
}

/** 根据代理ID获取实例 */
export function apiGetInstanceByProxyId(proxyId: string) {
  return http.get<TStaticInstanceBO>(`/static/instances/proxy-id/${proxyId}`);
}

/** 根据第三方代理ID获取实例 */
export function apiGetInstanceByThirdProxyId(thirdProxyId: string) {
  return http.get<TStaticInstanceBO>(`/static/instances/third-proxy-id/${thirdProxyId}`);
}

/** 更新静态代理实例信息 */
export function apiUpdateInstance(id: string, data: TUpdateStaticInstanceRequest) {
  return http.put<TStaticInstanceBO>(`/static/instances/${id}`, data);
}

/** 根据ID获取静态代理实例 */
export function apiGetInstanceById(id: string) {
  return http.get<TStaticInstanceBO>(`/static/instances/${id}`);
}

/** 激活实例 */
export function apiActivateInstance(id: string) {
  return http.post<TStaticInstanceBO>(`/static/instances/${id}/activate`);
}

/** 停用实例 */
export function apiDeactivateInstance(id: string, query?: { reason?: any }) {
  return http.post<TStaticInstanceBO>(`/static/instances/${id}/deactivate`, query);
}

/** 批量启用/禁用静态线路 */
export function apiBatchToggleStaticLinesEnabled(data: TBatchToggleEnabledBO) {
  return http.put<TBatchOperationResultDTO>(`/static/lines/batch/toggle-enabled`, data);
}

/** 分页查询静态线路 */
export function apiListStaticLines(data: TPageQuery) {
  return http.post<TPageResultStaticLineDTO>(`/static/lines/page`, data);
}

/** 更新静态线路 */
export function apiUpdateStaticLine(id: string, data: TStaticLineBO) {
  return http.put<TStaticLineDTO>(`/static/lines/${id}`, data);
}

/** 根据ID获取静态线路详情 */
export function apiGetStaticLineById(id: string) {
  return http.get<TStaticLineDTO>(`/static/lines/${id}`);
}

/** 获取线路健康状态 */
export function apiGetLineHealth(id: string) {
  return http.get<TLineHealthDTO>(`/static/lines/${id}/health`);
}

/** 创建新的静态代理订单 */
export function apiCreateStaticOrder(data: TCreateStaticOrderRequest) {
  return http.post<TStaticOrderBO>(`/static/orders`, data);
}

/** 获取需要自动续费的订单列表 */
export function apiGetAutoRenewalOrders() {
  return http.get<any[]>(`/static/orders/auto-renewal`);
}

/** 获取即将到期的订单列表 */
export function apiGetExpiringOrders(query?: { days?: any }) {
  return http.get<any[]>(`/static/orders/expiring`, query);
}

/** 分页获取静态代理订单 */
export function apiGetOrders(data: TPageQuery) {
  return http.post<TPageResultStaticOrderBO>(`/static/orders/page`, data);
}

/** 更新静态代理订单信息 */
export function apiUpdateOrder(id: string, data: TUpdateStaticOrderRequest) {
  return http.put<TStaticOrderBO>(`/static/orders/${id}`, data);
}

/** 根据ID获取静态代理订单 */
export function apiGetOrderById(id: string) {
  return http.get<TStaticOrderBO>(`/static/orders/${id}`);
}

/** 删除订单 */
export function apiDeleteOrder(id: string) {
  return http.delete<boolean>(`/static/orders/${id}`);
}

/** 取消订单 */
export function apiCancelOrder(id: string, query?: { reason?: any }) {
  return http.post<TStaticOrderBO>(`/static/orders/${id}/cancel`, query);
}

/** 退款订单 */
export function apiRefundOrder(id: string, data: TRefundOrderRequest) {
  return http.post<TStaticOrderBO>(`/static/orders/${id}/refund`, data);
}

/** 重试订单 */
export function apiRetryOrder(id: string) {
  return http.post<TStaticOrderBO>(`/static/orders/${id}/retry`);
}

/** 创建价格配置 */
export function apiCreateStaticPrice(data: TStaticPriceBO) {
  return http.post<TStaticPriceDTO>(`/static/prices`, data);
}

/** 清除价格缓存 */
export function apiClearStaticPriceCache() {
  return http.delete<any>(`/static/prices/cache`);
}

/** 清除指定ID的价格缓存 */
export function apiClearStaticPriceCacheById(id: string) {
  return http.delete<any>(`/static/prices/cache/${id}`);
}

/** 获取所有有效的价格配置 */
export function apiGetEnablePrices() {
  return http.get<any[]>(`/static/prices/enable`);
}

/** 检查是否存在匹配的价格配置 */
export function apiHasMatchingPrice(query?: { businessTypeId?: any; ispType?: any; locationId?: any; tagId?: any; userId?: any }) {
  return http.get<any>(`/static/prices/exists`, query);
}

/** 根据维度获取最佳匹配价格 */
export function apiGetBestMatchingPrice(query?: { businessTypeId?: any; ispType?: any; locationId?: any; tagId?: any; userId?: any }) {
  return http.get<TStaticPriceDTO>(`/static/prices/match`, query);
}

/** 根据维度获取所有匹配的价格列表 */
export function apiGetMatchingPrices(query?: { businessTypeId?: any; ispType?: any; locationId?: any; tagId?: any; userId?: any }) {
  return http.get<any[]>(`/static/prices/matches`, query);
}

/** 分页查询价格配置 */
export function apiListStaticPrices(data: TPageQuery) {
  return http.post<TPageResultStaticPriceDTO>(`/static/prices/page`, data);
}

/** 获取价格数值 */
export function apiGetPrice(query?: { businessTypeId?: any; defaultPrice?: any; ispType?: any; locationId?: any; tagId?: any; userId?: any }) {
  return http.get<any>(`/static/prices/price`, query);
}

/** 根据ID获取价格配置详情 */
export function apiGetStaticPriceById(id: string) {
  return http.get<TStaticPriceDTO>(`/static/prices/${id}`);
}

/** 手动处理待处理的第三方订单 */
export function apiProcessPendingOrders() {
  return http.post<TProcessResult>(`/third-party-orders/process-pending`);
}

/** 重试失败的第三方订单 */
export function apiRetryFailedOrders() {
  return http.post<TProcessResult>(`/third-party-orders/retry-failed`);
}

/** 查询第三方订单状态 */
export function apiQueryOrderStatus(thirdOrderNo: string) {
  return http.get<ThirdPartyOrderStatus>(`/third-party-orders/status/${thirdOrderNo}`);
}

/** 创建新的管理员用户 */
export function apiCreateAdminUser(data: TCreateAdminUserRequest) {
  return http.post<TAdminUserBo>(`/users`, data);
}

/** 分页获取管理员用户 */
export function apiGetAdminUsers(data: TPageQuery) {
  return http.post<TPageResultAdminUserBo>(`/users/page`, data);
}

/** 更新管理员用户 */
export function apiUpdateAdminUser(id: string, data: TUpdateAdminUserRequest) {
  return http.put<TAdminUserBo>(`/users/${id}`, data);
}

/** 根据ID获取管理员用户 */
export function apiGetAdminUserById(id: string) {
  return http.get<TAdminUserBo>(`/users/${id}`);
}

/** 更新管理员用户密码 */
export function apiUpdateAdminUserPassword(id: string, data: TUpdatePasswordRequest) {
  return http.put<TAdminUserBo>(`/users/${id}/password`, data);
}

/** 为管理员用户分配角色（通过角色ID） */
export function apiAssignRolesToUser(id: string, data: any[]) {
  return http.post<TAdminUserBo>(`/users/${id}/roles`, data);
}

/** 为管理员用户分配角色（通过角色代码） */
export function apiAssignRolesToUserByCodes(id: string, data: any[]) {
  return http.post<TAdminUserBo>(`/users/${id}/roles/codes`, data);
}

/** 设置管理员用户活动状态 */
export function apiSetAdminUserEnable(id: string, query?: { enabled?: any }) {
  return http.patch<TAdminUserBo>(`/users/${id}enable`, query);
}

/** 从管理员用户中移除角色（通过角色代码） */
export function apiRemoveRoleFromUserByCode(userId: string, roleCode: string) {
  return http.delete<TAdminUserBo>(`/users/${userId}/roles/code/${roleCode}`);
}

/** 为管理员用户添加角色（通过角色代码） */
export function apiAddRoleToUserByCode(userId: string, roleCode: string) {
  return http.post<TAdminUserBo>(`/users/${userId}/roles/code/${roleCode}`);
}

/** 从管理员用户中移除角色（通过角色ID） */
export function apiRemoveRoleFromUser(userId: string, roleId: string) {
  return http.delete<TAdminUserBo>(`/users/${userId}/roles/${roleId}`);
}

/** 为管理员用户添加角色（通过角色ID） */
export function apiAddRoleToUser(userId: string, roleId: string) {
  return http.post<TAdminUserBo>(`/users/${userId}/roles/${roleId}`);
}

