// import './ImageUpload.less'

import { FileUtils } from "@/utils/file";
import HttpUtils from "@/utils/http";
import { UploadOutlined } from "@ant-design/icons";
import { Upload, UploadFile } from "antd";
import { useEffect, useState } from "react";

type ImageUploadProps = {
  value: string[]
  onChange: (value: string[]) => void
  maxCount?: number
};

export const ImageUpload = ({
  maxCount,
  value,
  onChange
}: ImageUploadProps) => {

  const [fileList, setFileList] = useState<UploadFile[]>([])

  const [changeId, setChangeId] = useState(0)

  useEffect(() => {
    console.log(1)
    const newFileList = FileUtils.urlsToUploadFiles(value)
    setFileList(newFileList)
  }, [value])

  useEffect(() => {
    changeId && onChange(FileUtils.uploadFilesToUrls(fileList))
  }, [changeId])

  return (
    <div className="ImageUpload">
      <Upload
        action={`${HttpUtils.API_SITE}/upload`}
        headers={{ Authorization: `Bearer ${HttpUtils.TOKEN}` }}
        method="POST"
        name="file"
        beforeUpload={FileUtils.beforeUpload}
        listType="picture-card"
        accept="image/*"
        fileList={fileList}
        maxCount={maxCount}
        onChange={(info) => {
          setFileList(info.fileList)
          if(info.file.status === 'done') {
            setChangeId(changeId+1)
          }
        }}
        onRemove={(file) => {
          setFileList(fileList.filter(f => f.uid !== file.uid))
          setChangeId(changeId+1)
        }}
        isImageUrl={() => true}
        showUploadList={{
          showPreviewIcon: false,
        }}
      >
        {fileList.length >= maxCount ? null : <UploadOutlined style={{ fontSize: 20 }} />}

      </Upload>
    </div>
  );
}
