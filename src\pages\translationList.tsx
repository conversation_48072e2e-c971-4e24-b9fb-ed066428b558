import React, { useRef, useState } from 'react';
import { Button, Form, Input, Modal, Popconfirm, Space, Tabs, message } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined, TranslationOutlined } from '@ant-design/icons';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn, useRequest } from 'ahooks';
import { 
  apiTranslationPageList, 
  apiTranslationCreate, 
  apiTranslationUpdate,
  apiLanguagePageList,
  apiTranslationsByKey,
  apiDeleteTranslationsByKey
} from '@/apis/apis.api';
import { ToolsUtil } from '@/utils/tools';

// Translation Management Component
const TranslationList = () => {
  const tableRef = useRef<ActionType>();
  const [form] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingTranslation, setEditingTranslation] = useState<TTranslationKey | null>(null);
  const [languages, setLanguages] = useState<TLanguage[]>([]);
  const [languagesLoading, setLanguagesLoading] = useState(false);
  const [translationLoading, setTranslationLoading] = useState(false);

  // Fetch all languages for translation form
  const fetchLanguages = useMemoizedFn(async () => {
    setLanguagesLoading(true);
    try {
      const res = await apiLanguagePageList({
        current: 0,
        size: 100,
        filters: [{ field: 'isActive', operator: 'eq', value: 1 }]
      });
      setLanguages(res.records || []);
    } catch (error) {
      console.error('获取语言列表失败:', error);
      message.error('获取语言列表失败');
    } finally {
      setLanguagesLoading(false);
    }
  });

  // Fetch translation details by key
  const fetchTranslationByKey = useMemoizedFn(async (key: string) => {
    if (!key) return;
    
    setTranslationLoading(true);
    try {
      const translationData = await apiTranslationsByKey(key);
      
      // Set form values
      const formValues: any = {
        key: translationData.key,
        module: translationData.module || '',
        description: translationData.description || '',
      };
      
      // Set translation values for each language
      if (translationData.translations) {
        Object.entries(translationData.translations).forEach(([langCode, translation]) => {
          if (translation && translation.value !== undefined) {
            formValues[`translation_${langCode}`] = translation.value;
          }
        });
      }
      
      form.setFieldsValue(formValues);
      
      // Create a TTranslationKey object from the response
      const translationKey: TTranslationKey = {
        translationKey: translationData.key,
        module: translationData.module,
        description: translationData.description,
        translations: translationData.translations || {},
      };
      
      setEditingTranslation(translationKey);
    } catch (error) {
      console.error('获取翻译详情失败:', error);
      message.error('获取翻译详情失败');
    } finally {
      setTranslationLoading(false);
    }
  });

  // Handle deleting a translation
  const handleDelete = useMemoizedFn(async (key: string) => {
    if (!key) return;
    
    try {
      await apiDeleteTranslationsByKey(key);
      message.success('翻译删除成功');
      tableRef.current?.reload();
    } catch (error) {
      console.error('删除翻译失败:', error);
      message.error('删除翻译失败');
    }
  });

  // Handle form submission
  const handleSubmit = useMemoizedFn(async () => {
    try {
      const values = await form.validateFields();
      
      // Prepare translations data
      const translationData: TUpdateTranslationsByKeyRequest = {
        key: values.key,
        module: values.module,
        description: values.description,
        translations: {}
      };
      
      // Process language translations
      languages.forEach(lang => {
        const fieldName = `translation_${lang.code}`;
        if (values[fieldName]) {
          translationData.translations[lang.code] = values[fieldName];
        }
      });
      
      if (editingTranslation) {
        // Update existing translation
        await apiTranslationUpdate(translationData);
        message.success('翻译更新成功');
      } else {
        // Create new translation
        await apiTranslationCreate(translationData);
        message.success('翻译创建成功');
      }
      
      setIsModalVisible(false);
      form.resetFields();
      tableRef.current?.reload();
    } catch (error) {
      console.error('表单提交错误:', error);
      message.error('提交失败，请检查表单');
    }
  });

  // Open modal for editing
  const handleEdit = useMemoizedFn((record: TTranslationKey) => {
    if (!record) return;
    
    // Fetch languages if not already loaded
    if (languages.length === 0) {
      fetchLanguages();
    }

    console.log('record', record);

    // Fetch translation details by key
    fetchTranslationByKey(record.translationKey);
    
    setIsModalVisible(true);
  });

  // Open modal for creating
  const handleCreate = useMemoizedFn(() => {
    setEditingTranslation(null);
    form.resetFields();
    setIsModalVisible(true);
  });



  return (
    <>
      <ProTable<TTranslationKey>
        actionRef={tableRef}
        headerTitle="翻译管理"
        rowKey="translationKey"
        columns={[
          {
            title: '翻译键',
            dataIndex: 'translationKey',
            key: 'translationKey',
            ellipsis: true,
          },
          {
            title: '模块',
            dataIndex: 'module',
            key: 'module',
            ellipsis: true,
          },
          {
            title: '描述',
            dataIndex: 'description',
            key: 'description',
            ellipsis: true,
          },
          {
            title: '操作',
            key: 'actions',
            valueType: 'option',
            render: (_, record: TTranslationKey) => (
              <Space>
                <a onClick={() => handleEdit(record)}>编辑</a>
                <Popconfirm
                  title="确定要删除这个翻译吗？"
                  description="删除后将无法恢复，所有语言的翻译都会被删除。"
                  onConfirm={() => handleDelete(record.translationKey)}
                  okText="确定"
                  cancelText="取消"
                >
                  <a style={{ color: 'red' }}><DeleteOutlined /> 删除</a>
                </Popconfirm>
              </Space>
            ),
          },
        ]}
        search={{
          defaultCollapsed: false,
        }}
        toolBarRender={() => [
          <Button 
            key="add"
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={() => {
              fetchLanguages();
              handleCreate();
            }}
          >
            添加翻译
          </Button>,
        ]}
        request={async (params, sorter) => {
          const filters: TQueryFilter[] = [];
          
          // 添加搜索条件
          if (params.translationKey) {
            filters.push({ field: 'translationKey', operator: 'like', value: params.translationKey });
          }
          if (params.module) {
            filters.push({ field: 'module', operator: 'like', value: params.module });
          }
          if (params.description) {
            filters.push({ field: 'description', operator: 'like', value: params.description });
          }


          try {
            const res = await apiTranslationPageList({
              current: params.current - 1,
              size: params.pageSize,
              filters,
              sorts: ToolsUtil.tableSorterToPageListReqSort(sorter),
            });

            // 转换返回的数据结构，确保字段名匹配
            const data = res.records || [];

            return {
              total: res.total || 0,
              data: data,
              success: true,
            };
          } catch (error) {
            console.error('获取翻译列表失败:', error);
            message.error('获取翻译列表失败');
            return {
              total: 0,
              data: [],
              success: false,
            };
          }
        }}
      />

      {/* Translation Modal */}
      <Modal
        title={editingTranslation ? '编辑翻译' : '添加翻译'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => setIsModalVisible(false)}
        width={800}
        confirmLoading={translationLoading}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="key"
            label="翻译键"
            rules={[{ required: true, message: '请输入翻译键' }]}
          >
            <Input placeholder="例如：common.submit" disabled={!!editingTranslation} />
          </Form.Item>

          <Form.Item
            name="module"
            label="模块"
          >
            <Input placeholder="例如：common" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={2} placeholder="请输入对该翻译键的描述" />
          </Form.Item>

          <Tabs
            defaultActiveKey="translations"
            items={[
              {
                key: 'translations',
                label: '翻译内容',
                children: (
                  <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                    {languagesLoading || translationLoading ? (
                      <div>加载中...</div>
                    ) : languages && languages.length > 0 ? (
                      languages.map(language => (
                        <Form.Item
                          key={language.code}
                          name={`translation_${language.code}`}
                          label={`${language.name} (${language.code})`}
                          rules={[{ required: language.isDefault, message: `请输入${language.name}翻译` }]}
                        >
                          <Input.TextArea 
                            rows={2} 
                            placeholder={`请输入${language.name}翻译`} 
                          />
                        </Form.Item>
                      ))
                    ) : (
                      <div>没有可用的语言，请先在语言管理中添加语言</div>
                    )}
                  </div>
                ),
              },
            ]}
          />
        </Form>
      </Modal>
    </>
  );
};

export default TranslationList;

TranslationList.auth = ['apiTranslationPageList', 'apiTranslationCreate', 'apiTranslationUpdate', 'apiLanguagePageList', 'apiTranslationsByKey', 'apiDeleteTranslationsByKey'];
