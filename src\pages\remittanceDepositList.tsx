import React, { useRef, useState } from 'react';
import { Form, Input, Modal, message, Space, Select, Tooltip, Tag } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, EyeOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import {
  apiRemittanceGetPendingDepositsPage,
  apiRemittanceApproveDeposit,
  apiRemittanceRejectDeposit
} from '@/apis/apis.api';
import { ToolsUtil } from '@/utils/tools';
import ClientUserObj from '@/components/ClientUserObj';

// 交易状态选项
const TRANSACTION_STATUS_OPTIONS = [
  { label: '处理中', value: 'PENDING' },
  { label: '已完成', value: 'COMPLETED' },
  { label: '失败', value: 'FAILED' },
  { label: '已取消', value: 'CANCELLED' },
  { label: '已拒绝', value: 'REJECTED' }
];

// 交易类型选项
const TRANSACTION_TYPE_OPTIONS = [
  { label: '充值', value: 'DEPOSIT' },
  { label: '提现', value: 'WITHDRAW' },
  { label: '支付', value: 'PAYMENT' },
  { label: '退款', value: 'REFUND' },
  { label: '转入', value: 'TRANSFER_IN' },
  { label: '转出', value: 'TRANSFER_OUT' },
  { label: '奖励', value: 'BONUS' },
  { label: '调整', value: 'ADJUSTMENT' },
  { label: '冻结', value: 'FREEZE' },
  { label: '解冻', value: 'UNFREEZE' },
  { label: '汇款充值', value: 'REMITTANCE_DEPOSIT' }
];

// 交易类型数值映射
const TRANSACTION_TYPE_VALUE_MAP = {
  0: 'DEPOSIT',
  1: 'WITHDRAW',
  2: 'PAYMENT',
  3: 'REFUND',
  4: 'TRANSFER_IN',
  5: 'TRANSFER_OUT',
  6: 'BONUS',
  7: 'ADJUSTMENT',
  8: 'FREEZE',
  9: 'UNFREEZE',
  10: 'REMITTANCE_DEPOSIT'
};

// 交易状态数值映射
const TRANSACTION_STATUS_VALUE_MAP = {
  0: 'PENDING',
  1: 'COMPLETED',
  2: 'FAILED',
  3: 'CANCELLED',
  4: 'REJECTED'
};

// 交易状态颜色
const TRANSACTION_STATUS_COLORS = {
  'PENDING': 'orange',
  'COMPLETED': 'green',
  'FAILED': 'red',
  'CANCELLED': 'gray',
  'REJECTED': 'red'
};

// 汇款申请管理组件
const RemittanceDepositList: React.FC & { auth?: string[] } = () => {
  const [rejectForm] = Form.useForm();
  const tableRef = useRef<ActionType>();

  // 状态管理
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [isRejectModalVisible, setIsRejectModalVisible] = useState(false);
  const [currentTransaction, setCurrentTransaction] = useState<TransactionBo | null>(null);

  // 处理查看交易详情
  const handleViewDetails = useMemoizedFn((record: TransactionBo) => {
    setCurrentTransaction(record);
    setIsDetailModalVisible(true);
  });

  // 处理批准申请
  const handleApproveDeposit = useMemoizedFn(async (transactionId: number) => {
    try {
      await apiRemittanceApproveDeposit(transactionId);
      message.success('申请已批准');
      tableRef.current?.reload();
    } catch (error) {
      console.error('批准申请失败:', error);
      message.error('批准申请失败');
    }
  });

  // 显示拒绝申请对话框
  const handleOpenRejectModal = useMemoizedFn((record: TransactionBo) => {
    setCurrentTransaction(record);
    rejectForm.resetFields();
    setIsRejectModalVisible(true);
  });

  // 处理拒绝申请
  const handleRejectDeposit = useMemoizedFn(async () => {
    try {
      if (!currentTransaction) return;

      const values = await rejectForm.validateFields();
      await apiRemittanceRejectDeposit(currentTransaction.id, values.reason);

      message.success('申请已拒绝');
      setIsRejectModalVisible(false);
      tableRef.current?.reload();
    } catch (error) {
      console.error('拒绝申请失败:', error);
      message.error('拒绝申请失败');
    }
  });

  // 格式化交易状态标签
  const formatTransactionStatus = (status: number | string) => {
    const statusKey = typeof status === 'number' ? TRANSACTION_STATUS_VALUE_MAP[status] : status;
    const statusInfo = TRANSACTION_STATUS_OPTIONS.find(option => option.value === statusKey);
    return (
      <Tag color={TRANSACTION_STATUS_COLORS[statusKey] || 'default'}>
        {statusInfo?.label || statusKey}
      </Tag>
    );
  };

  // 格式化交易类型
  const formatTransactionType = (type: number | string) => {
    // 如果是数字，先从映射中获取对应的字符串值
    const typeKey = typeof type === 'number' ? TRANSACTION_TYPE_VALUE_MAP[type] : type;
    // 如果没有找到映射，则直接返回原始值
    if (!typeKey && typeof type === 'number') {
      return `未知类型(${type})`;
    }
    // 从选项中查找对应的标签
    const typeInfo = TRANSACTION_TYPE_OPTIONS.find(option => option.value === typeKey);
    return typeInfo?.label || typeKey;
  };

  // 表格列定义
  const columns: ProColumns<TransactionBo>[] = [
    {
      title: '交易号',
      dataIndex: 'transactionNo',
      key: 'transactionNo',
      copyable: true,
      ellipsis: true,
    },
    {
      title: '用户信息',
      dataIndex: 'userId',
      key: 'userId',
      width: 150,
      render: (_text, record) => record.userInfo ? <ClientUserObj user={record.userInfo} /> : record.userId
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (_, record) => `${record.amount} ${record.currency}`,
    },
    {
      title: '交易类型',
      dataIndex: 'type',
      key: 'type',
      render: (_, record) => formatTransactionType(record.type),
    },
    {
      title: '交易状态',
      dataIndex: 'status',
      key: 'status',
      render: (_, record) => formatTransactionStatus(record.status),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      valueType: 'dateTime',
      sorter: true,
    },
    {
      title: '操作',
      key: 'actions',
      valueType: 'option',
      render: (_, record) => (
        <Space size="middle">
          <a onClick={() => handleViewDetails(record)}>
            <Tooltip title="查看详情">
              <EyeOutlined />
            </Tooltip>
          </a>
          <a onClick={() => handleApproveDeposit(record.id)}>
            <Tooltip title="批准">
              <CheckCircleOutlined style={{ color: 'green' }} />
            </Tooltip>
          </a>
          <a onClick={() => handleOpenRejectModal(record)}>
            <Tooltip title="拒绝">
              <CloseCircleOutlined style={{ color: 'red' }} />
            </Tooltip>
          </a>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<TransactionBo>
        actionRef={tableRef}
        headerTitle="汇款申请管理"
        rowKey="id"
        columns={columns}
        search={{
          defaultCollapsed: false,
        }}
        request={async (params, sorter) => {
          const filters: any[] = [];

          // 添加其他搜索条件
          if (params.userId) {
            filters.push({ field: 'userId', operator: 'eq', value: params.userId });
          }
          if (params.transactionNo) {
            filters.push({ field: 'transactionNo', operator: 'like', value: params.transactionNo });
          }

          let sorts = ToolsUtil.tableSorterToPageListReqSort(sorter);

          // 添加默认排序，创建时间倒序
          if (!sorts || sorts.length === 0) {
            sorts = [{ field: 'createdAt', direction: 'DESC' }];
          }

          try {
            const res = await apiRemittanceGetPendingDepositsPage({
              current: params.current - 1,
              size: params.pageSize,
              filters,
              sorts,
            });

            return {
              total: res.total || 0,
              data: res.records || [],
              success: true,
            };
          } catch (error) {
            console.error('获取汇款申请列表失败:', error);
            message.error('获取汇款申请列表失败');
            return {
              total: 0,
              data: [],
              success: false,
            };
          }
        }}
      />

      {/* 交易详情对话框 */}
      <Modal
        title="交易详情"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={null}
      >
        {currentTransaction && (
          <Form layout="vertical">

            <Form.Item label="交易号">
              <Input value={currentTransaction.transactionNo} readOnly />
            </Form.Item>
            <Form.Item label="用户信息">
              {currentTransaction.userInfo ? (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <ClientUserObj user={currentTransaction.userInfo} />
                  <span style={{ marginLeft: 8 }}>({currentTransaction.userId})</span>
                </div>
              ) : (
                <Input value={currentTransaction.userId} readOnly />
              )}
            </Form.Item>
            <Form.Item label="钱包ID">
              <Input value={currentTransaction.walletId} readOnly />
            </Form.Item>
            <Form.Item label="金额">
              <Input value={`${currentTransaction.amount} ${currentTransaction.currency}`} readOnly />
            </Form.Item>
            <Form.Item label="交易前余额">
              <Input value={`${currentTransaction.balanceBefore} ${currentTransaction.currency}`} readOnly />
            </Form.Item>
            <Form.Item label="交易后余额">
              <Input value={`${currentTransaction.balanceAfter} ${currentTransaction.currency}`} readOnly />
            </Form.Item>
            <Form.Item label="交易类型">
              <Input value={formatTransactionType(currentTransaction.type)} readOnly />
            </Form.Item>
            <Form.Item label="交易状态">
              <Input value={TRANSACTION_STATUS_OPTIONS.find(option =>
                option.value === (typeof currentTransaction.status === 'number'
                ? TRANSACTION_STATUS_VALUE_MAP[currentTransaction.status]
                : currentTransaction.status))?.label || currentTransaction.status} readOnly />
            </Form.Item>
            <Form.Item label="参考ID">
              <Input value={currentTransaction.referenceId || '无'} readOnly />
            </Form.Item>
            <Form.Item label="描述">
              <Input.TextArea value={currentTransaction.description || '无'} readOnly />
            </Form.Item>
            <Form.Item label="创建时间">
              <Input value={new Date(currentTransaction.createdAt).toLocaleString()} readOnly />
            </Form.Item>
            {currentTransaction['metadataJson'] && (
              <Form.Item label="汇款信息">
                <Input.TextArea value={JSON.stringify(JSON.parse(currentTransaction['metadataJson'] as string), null, 2)} readOnly rows={8} />
              </Form.Item>
            )}
          </Form>
        )}
      </Modal>

      {/* 拒绝原因对话框 */}
      <Modal
        title="拒绝申请"
        open={isRejectModalVisible}
        onOk={handleRejectDeposit}
        onCancel={() => setIsRejectModalVisible(false)}
      >
        <Form
          form={rejectForm}
          layout="vertical"
        >
          <Form.Item
            name="reason"
            label="拒绝原因"
            rules={[{ required: true, message: '请输入拒绝原因' }]}
          >
            <Input.TextArea rows={4} placeholder="请输入拒绝原因" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default RemittanceDepositList;

RemittanceDepositList.auth = ['apiRemittanceGetPendingDepositsPage', 'apiRemittanceApproveDeposit', 'apiRemittanceRejectDeposit'];
