/**
 * 增加组件
 * 用法：`pnpm genc -n SomeComp`添加一个`SomeComp`组件到`src/components/SomeComp`
 */
import { program } from 'commander';
import fs from 'fs';

program
  .name('pnpm genc')
  .option('-n, --name <string>', '设置组件名称')
  .helpOption('-h, --help', '打印帮助信息');

program.parse();

const { name } = program.opts();

fs.mkdirSync(`src/components/${name}`);
fs.writeFileSync(`src/components/${name}/index.tsx`, 
`
import ${name}, {T${name}Props } from './${name}';
export type { T${name}Props };
export default ${name};
`);
fs.writeFileSync(`src/components/${name}/${name}.tsx`,
`
export interface T${name}Props {

}

function ${name}(props: T${name}Props) {
  return (
    <div className="${name}">
      {JSON.stringify(props)}
    </div>
  )
}

export default ${name}
`);

console.log(`组件已创建，请修改组件内容\nsrc/components/${name}/${name}.tsx`);