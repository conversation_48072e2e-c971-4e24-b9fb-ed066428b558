import { defineModel } from 'foca';
import { waitUntil } from 'async-wait-until';
import http from '@/utils/http';
import CacheUtils from '@/utils/cache';
import router from '@/router';
import { apiLoginByPwd, apiMyUserInfoGet } from '@/apis/apis.api';

export interface AuthModelState {
  account?: TAdminUser;
  logined: boolean;
  permissions: string[];
}

const initialState: AuthModelState = {
  account: undefined,
  logined: false,
  permissions: [],
};


export const authModel = defineModel('auth', {
  initialState,
  computed: {},
  reducers: {},
  methods: {
    async loginByPwd(...[data]: Parameters<typeof apiLoginByPwd>) {
      try {
        const res = await apiLoginByPwd(data);
        this.afterLoadAccount(res);
      } catch (error) {
        throw new Error('登录失败');
        /* empty */
      } finally {
        this.setState({ logined: true });
      }
    },

    async getAccount() {
      try {
        const res = await apiMyUserInfoGet();
        this.afterLoadAccount(res);
        return res.user;
      } catch (error) {
        /* empty */
      } finally {
        this.setState({ logined: true });
      }
    },
    /**
     * 登录状态检查，否则跳转到登录页
     * @param waitTime 等待时间
     */
    async loginCheck(waitTime = 0) {
      try {
        await waitUntil(() => this.state.logined, waitTime);
        if (!this.state.account) {
          throw Promise.reject('未登录');
        }
      } catch (error) {
        router.navigate('/login', {
          replace: true,
        });
      }
    },

    logout() {
      http.setToken('');
      CacheUtils.removeItem('ACCESS_TOKEN');
      this.setState({
        account: undefined,
        authCodes: [],
      });
      router.navigate('/login', {
        replace: true,
      });
    },
    afterLoadAccount(data: { token?: string; expiredAt: number; user: TAdminUser }) {
      const { token, expiredAt, user } = data;
      if (token != undefined) {
        http.setToken(token, expiredAt);
      }
      this.setState({
        account: user,
        permissions: user.permissions,
      });
    },
    canGoTo(some: {
      [key: string]: any;
      auth?: string[];
    }) {
      if(!this.state.logined) return false;
      if (!some.auth) return true;
      const result = (some.auth || []).every((item) => this.state.permissions.includes(item));
      if(!result && this.state.logined) {
        const needs = some.auth.filter(item => !this.state.permissions.includes(item));
        // console.log(some.path, needs); // TODO
      }
      return result
    }
  },
  events: {
    onInit() {
      this.getAccount();
    },
  },
});
