import { SortOrder } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import ExcelJS from 'exceljs';
import { Random } from 'tony-mockjs';
import NP from 'number-precision';

NP.enableBoundaryChecking(false);

export class ToolsUtil {
  /**
   * 等待一段时间
   * @param msTime 等待时间ms
   * @returns
   */
  static waitTime(msTime: number = 100) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(true);
      }, msTime);
    });
  }

  static random = Random;

  /**
   * 是否开发环境
   * @returns
   */
  static isDebug = import.meta.env.DEV;

  /**
   * 近似，保留几位小数
   */
  static mathRound(num?: number, decimal = 2) {
    return NP.round(num || 0, decimal);
  }

  /**
   * 让精度丢失的数字正确
   */
  static mathStrip(num?: number) {
    return NP.strip(num || 0);
  }

  /**
   * 计算并转为字符串，用于展示数字
   **/
  static calcAndToString({
    num,
    calcFun = (num) => num,
    decimal = 2,
    nansStr = '-',
    unit = '',
  }: {
    num: unknown;
    calcFun?: (num: number) => number;
    decimal?: number;
    nansStr?: string;
    unit?: string;
  }) {
    if (typeof num === 'string' && /^-?(0|([1-9][0-9]*))(\.[\d]+)?$/.test(num)) {
      num = Number(num);
    }
    if (typeof num !== 'number') {
      return nansStr;
    }
    const result = calcFun(num);
    if (typeof result !== 'number') return nansStr;
    return `${this.mathRound(result, decimal)}${unit}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  /** 存成分的金额转字符串 */
  static moneyCentFormat(num: number, unit?: string) {
    return this.calcAndToString({
      num,
      calcFun: (num) => num / 100,
      decimal: 2,
      unit: unit === undefined ? '元' : unit,
    });
  }

  /**
   * 选项转枚举
   * @param options 选项列表
   * @returns
   */
  static optionsToEnum<T extends string | number>(options: { label: string; value: T }[]) {
    return (options || []).reduce(
      (before, current) => {
        before[current.value] = current.label;
        return before;
      },
      {} as Record<T, string>,
    );
  }

  /**
   * 枚举转选项
   * @param enumObj 枚举对象
   * @returns
   */
  static enumToOptions<T extends string | number>(enumObj: Record<T, string>) {
    return Object.keys(enumObj).map((key) => {
      return {
        label: enumObj[key],
        value: key as T,
      };
    });
  }

  /**
   * 时间格式化
   * @param time
   * @param options
   * @returns
   */
  static timeFormat(
    time?: number | string | Date | null | undefined,
    options?: {
      format?: string;
      formatType?: 'time' | 'date';
      emptyStr?: string;
    },
  ) {
    const { formatType = 'time', emptyStr = '-' } = options || {};
    if (!time) return emptyStr;
    const format =
      options?.format || (formatType === 'time' ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD');
    return dayjs(time).format(format);
  }

  /**
   * 把传入的时间范围转成毫秒时间戳时间范围
   * @param range 时间范围，开头和结束都可以设置为null
   * @returns
   */
  static dateRangeToFilterRange(range: [Date | string | null, Date | string | null]) {
    return [
      range[0] ? dayjs(range[0]).startOf('day').valueOf() : 0,
      dayjs(range[1] ? range[1] : '2099-12-31')
        .endOf('day')
        .valueOf(),
    ] as [number, number];
  }

  /**
   * 把表格request的 sorter转成分页请求的sort
   * @param sorter
   * @returns
   */
  static tableSorterToPageListReqSort(sorter: Record<string, SortOrder>) {
    const sort = Object.keys(sorter || {}).map((key) => ({
      field: key,
      direction: sorter[key] === 'ascend' ? 'asc' : 'desc',
    }));
    if (!sort.length) {
      sort.push({ field: 'createdAt', direction: 'desc' });
    }
    return sort as TQuerySort[];
  }

  /** 下载文件 */
  static downloadFile(url: string) {
    const a = document.createElement('a');
    a.href = url;
    a.target = '_blank';
    const name = url.substring(url.lastIndexOf('/') + 1);
    a.download = name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }

  static downloadBlob(blob: Blob, name: string) {
    const localUrl = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = localUrl;
    a.download = name;
    a.click();
    URL.revokeObjectURL(localUrl);
  }

  static async exportExcel<T = any>(options: {
    name: string;
    data: T[];
    columns: {
      title: string;
      width?: number;
      dataIndex: string;
      render?: (record: T) => string | Date | number;
      valueEnum?: { [key: string]: string };
    }[];
  }) {
    const { name, data, columns } = options;
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(name);
    worksheet.columns = columns.map((item) => {
      return {
        header: item.title,
        key: item.dataIndex,
        width: item.width || 10,
      };
    });
    data.forEach((item) => {
      const row = columns.map((col) => {
        if (col.render) {
          return col.render(item);
        }
        if (col.valueEnum) {
          return col.valueEnum[item[col.dataIndex]] || item[col.dataIndex];
        }
        return item[col.dataIndex];
      });
      worksheet.addRow(row);
    });
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    ToolsUtil.downloadBlob(blob, `${name}.xlsx`);
  }

  // 进入全屏
  static fullScreen(el = document.documentElement) {
    el.requestFullscreen();
  }
  // 退出全屏
  static exitFullScreen() {
    document.exitFullscreen();
  }

  /**
   * 基础字典转完全版字典
   * @param baseAgreeDicts
   * @returns
   */
  static baseAgreeDicts2AgreeDicts<T>(baseAgreeDicts: T) {
    const result = {} as Record<string, TAgreeMapOption>;
    function tranToValueDataMap(
      treeOptions: TAgreeOption[],
      parentValues: string[] = [],
      parentChildrenValues: string[] = [],
    ) {
      let result: Record<string, TAgreeMapOption> = {};
      for (const item of treeOptions) {
        parentChildrenValues.push(item.value);
        const childrenValues: string[] = [];
        if (item.children) {
          const childrenMap = tranToValueDataMap(
            item.children,
            [...parentValues, item.value],
            childrenValues,
          );
          result = { ...result, ...childrenMap };
          parentChildrenValues.push(...childrenValues);
        }
        result[item.value] = {
          ...item,
          parentValues: [...parentValues],
          childrenValues,
          upValues: [...parentValues, item.value],
          downValues: [item.value, ...childrenValues],
        };
      }
      return result as Record<string, TAgreeMapOption>;
    }
    for (const key in baseAgreeDicts) {
      const valueDataMap = tranToValueDataMap((baseAgreeDicts as any)[key].options);
      (result as any)[key] = {
        name: (baseAgreeDicts as any)[key].name,
        options: (baseAgreeDicts as any)[key].options,
        values: Object.keys(valueDataMap),
        labels: Object.values(valueDataMap).map((item) => item.label),
        valueLabelMap: Object.values(valueDataMap).reduce((prev: any, item) => {
          return { ...prev, [item.value]: item.label };
        }, {}),
        labelValueMap: Object.values(valueDataMap).reduce((prev, item) => {
          return { ...prev, [item.label]: item.value };
        }, {}),
        valueDataMap,
        labelDataMap: Object.values(valueDataMap).reduce((prev, item) => {
          (prev as any)[item.label] = item;
          return prev;
        }, {}),
        match(value: string | number, ...labels: string[]) {
          return labels.some((label) => this.labelValueMap[label].toString() === value.toString());
        },
      };
    }
    return result as unknown as TAgreeDicts;
  }
  

  
static i18nText(name: string) {
  try {
    return JSON.parse(name)['zh-CN'];
  } catch (error) {
    return name;
  }
}
}
