import { 
  apiCreateStaticBusinessType, 
  apiListStaticBusinessTypes, 
  apiUpdateStaticBusinessType, 
  apiDeleteStaticBusinessType,
  apiGetStaticBusinessTypeById
} from '@/apis/gen.apis';
import { useFormModal } from '@/components/FormilyModal';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { App, Button, Space, Tag, Popconfirm } from 'antd';
import { useRef } from 'react';

type TEditFormData = {
  id?: number;
  code: string;
  name: string;
  description?: string;
  isEnabled: TIsEnum;
  config?: string;
  isPopular: TIsEnum;
};

function StaticBusinessTypesList() {
  const tableRef = useRef<ActionType>();
  const { message } = App.useApp();

  const { formModalRef: editFormModalRef, formModalHolder: editFormModalHolder } =
    useFormModal<TEditFormData>();

  const showEditModal = useMemoizedFn(async (record?: TStaticBusinessTypeDTO) => {
    let initialValues = {};
    
    if (record?.id) {
      // 获取详细信息
      const detail = await apiGetStaticBusinessTypeById(record.id.toString());
      initialValues = { 
        ...detail, 
        enabled: detail.enabled ? 'true' : 'false', 
        popular: detail.popular ? 'true' : 'false' 
      };
    } else {
      initialValues = {
        enabled: 'true',
        popular: 'false',
      };
    }

    editFormModalRef.current?.show({
      modalTitle: record ? '修改业务类型' : '新增业务类型',
      modalWidth: 800,
      onAutoSubmit: async (values: any) => {
        const data = {
          ...values,
          enabled: values.enabled === 'true' ? 1 : 0,
          popular: values.popular === 'true' ? 1 : 0,
        };
        if (record?.id) {
          await apiUpdateStaticBusinessType(record.id.toString(), data);
        } else {
          await apiCreateStaticBusinessType(data);
        }

        message.success('提交成功');
        tableRef.current?.reload();
      },
      initialValues,
      schema: {
        type: 'object',
        properties: {
          code: {
            title: '业务类型编码',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          name: {
            title: '业务类型名称',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          description: {
            title: '描述',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
          },
          enabled: {
            title: '启用状态',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            enum: [
              { label: '启用', value: 'true' },
              { label: '禁用', value: 'false' },
            ],
          },
          popular: {
            title: '是否热门',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            enum: [
              { label: '是', value: 'true' },
              { label: '否', value: 'false' },
            ],
          },
          config: {
            title: '配置信息',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4,
              placeholder: '请输入JSON格式的配置信息',
            },
          },
        },
      },
    });
  });

  const handleDelete = useMemoizedFn(async (id: string) => {
    await apiDeleteStaticBusinessType(id);
    message.success('删除成功');
    tableRef.current?.reload();
  });

  return (
    <div>
      <ProTable<TStaticBusinessTypeDTO>
        actionRef={tableRef}
        headerTitle='静态业务类型管理'
        rowKey='id'
        toolBarRender={() => [
          <Button key='add' type='primary' onClick={() => showEditModal()}>
            新增
          </Button>,
        ]}
        search={{
          defaultCollapsed: false,
        }}
        columns={[
          { title: 'ID', dataIndex: 'id', search: false },
          { title: '编码', dataIndex: 'code' },
          { title: '名称', dataIndex: 'name' },
          { title: '描述', dataIndex: 'description', ellipsis: true, search: false },
          { 
            title: '启用状态', 
            dataIndex: 'enabled',
            width: 100,
            valueEnum: {
              'true': { text: '启用', color: 'green' },
              'false': { text: '禁用', color: 'red' },
            },
            valueType: 'select',
            render: (_text, record) => (
              <Tag color={record.enabled ? 'green' : 'red'}>
                {record.enabled ? '启用' : '禁用'}
              </Tag>
            ),
          },
          { 
            title: '是否热门', 
            dataIndex: 'popular',
            width: 100,
            valueEnum: {
              true: { text: '是', color: 'orange' },
              false: { text: '否', color: 'default' },
            },
            valueType: 'select',
            render: (_text, record) => (
              <Tag color={record.popular ? 'orange' : 'default'}>
                {record.popular ? '热门' : '普通'}
              </Tag>
            ),
          },
      
          { 
            title: '创建时间', 
            dataIndex: 'createdAt',
            width: 180,
            valueType: 'dateTime',
            search: false,
          },
      
          {
            title: '操作',
            valueType: 'option',
            key: 'option',
            render: (_text, record) => {
              return (
                <Space>
                  <a onClick={() => showEditModal(record)}>编辑</a>
                  <Popconfirm
                    title="确定要删除这个业务类型吗？"
                    onConfirm={() => handleDelete(record.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <a style={{ color: 'red' }}>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
        request={async (params) => {
          const filters: any[] = [];

          if (params.id) {
            filters.push({ field: 'id', operator: 'eq', value: params.id });
          }
          if (params.code) {
            filters.push({ field: 'code', operator: 'eq', value: params.code });
          }
          if (params.name) {
            filters.push({ field: 'name', operator: 'eq', value: params.name });
          }
         
          if (params.enabled !== undefined) {
            filters.push({ field: 'enabled', operator: 'eq', value: params.enabled === 'true' ? 1 : 0 });
          }
          if (params.popular !== undefined) {
            filters.push({ field: 'popular', operator: 'eq', value: params.popular === 'true' ? 1 : 0 });
          }

          const res = await apiListStaticBusinessTypes({
            current: (params.current || 1) - 1,
            size: params.pageSize || 20,
            filters,
            sorts: [],
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}
      />
      {editFormModalHolder}
    </div>
  );
}

export default StaticBusinessTypesList;

StaticBusinessTypesList.auth = [
  'apiCreateStaticBusinessType', 
  'apiListStaticBusinessTypes', 
  'apiUpdateStaticBusinessType', 
  'apiDeleteStaticBusinessType',
  'apiGetStaticBusinessTypeById'
];
