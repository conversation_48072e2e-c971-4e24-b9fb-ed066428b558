import {
  apiBrandPageList,
  apiCategoryPageList,
  apiLanguagePageList,
  apiProductCreate,
  apiProductGetById,
  apiProductUpdate,
} from '@/apis/apis.api';
import I18nField from '@/components/I18nField';
import ProductAttributesAndVariants from '@/components/ProductAttributesAndVariants';
import ProductImages from '@/components/ProductImages';
import { ArrowLeftOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Divider,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Space,
  Spin,
  Switch,
  Tabs,
  Typography,
} from 'antd';
import TabPane from 'antd/es/tabs/TabPane';
import React, { useEffect, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

// 商品编辑/创建组件
const ProductEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const isEdit = !!id;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [product, setProduct] = useState<ProductBo | null>(null);
  const [brands, setBrands] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [selectedBrandId, setSelectedBrandId] = useState<number | null>(null);
  const [brandLoading, setBrandLoading] = useState(false);
  const [categoryLoading, setCategoryLoading] = useState(false);
  const [images, setImages] = useState<string[]>([]);
  const [mainImage, setMainImage] = useState<string>('');
  const [variants, setVariants] = useState<any[]>([]);
  const [attributes, setAttributes] = useState<any[]>([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const navigate = useNavigate();
  const attributesAndVariantsRef = useRef<any>(null);

  // 获取品牌分页列表
  const fetchBrands = async () => {
    setBrandLoading(true);
    try {
      const response = await apiBrandPageList({
        current: 0,
        size: 100,
        filters: [{ field: 'isActive' as any, operator: 'eq' as any, value: 1 }],
      });
      setBrands((response as any)?.records || []);
    } catch (error) {
      console.error('获取品牌列表失败:', error);
      message.error('获取品牌列表失败');
    } finally {
      setBrandLoading(false);
    }
  };

  // 获取分类分页列表，根据选中的品牌进行筛选
  const fetchCategories = async (brandId?: number) => {
    setCategoryLoading(true);
    try {
      const filters: any[] = [{ field: 'isActive', operator: 'eq', value: 1 }];

      // 如果有选中的品牌，添加品牌过滤条件
      if (brandId) {
        filters.push({ field: 'brands.id', operator: 'eq', value: brandId });
      }

      const response = await apiCategoryPageList({
        current: 0,
        size: 100,
        filters,
      });

      setCategories((response as any)?.records || []);
    } catch (error) {
      console.error('获取分类列表失败:', error);
      message.error('获取分类列表失败');
    } finally {
      setCategoryLoading(false);
    }
  };

  // 初始化时获取品牌列表
  useEffect(() => {
    fetchBrands();
  }, []);

  // 当选中的品牌变化时，获取对应的分类列表
  useEffect(() => {
    fetchCategories(selectedBrandId || undefined);
  }, [selectedBrandId]);

  // 处理品牌选择变化
  const handleBrandChange = (value: number) => {
    setSelectedBrandId(value);
    form.setFieldsValue({ categoryId: undefined }); // 清空分类选择
    setSelectedCategoryId(null); // 清空已选择的分类ID
  };

  // 处理分类选择变化
  const handleCategoryChange = (value: number) => {
    setSelectedCategoryId(value);
  };

  // 获取商品详情
  useEffect(() => {
    if (isEdit && id) {
      const fetchProduct = async () => {
        setLoading(true);
        try {
          const data = await apiProductGetById(id);
          setProduct(data);

          // 设置图片数据 - 确保数据格式正确
          if (data.images && Array.isArray(data.images)) {
            // 如果images是对象数组，提取url属性
            if (typeof data.images[0] === 'object' && data.images[0]?.url) {
              setImages((data.images as ProductImageBo[]).map((img) => '/api' + img.url));
            } else {
              // 如果已经是字符串数组，直接使用
              setImages((data.images as string[]).map((img) => '/api' + img));
            }
          } else {
            setImages([]);
          }

          // 使用类型断言访问属性
          const productData = data as any;
          setMainImage('/api' + (productData.mainImage || productData.main_image || ''));

          setVariants(data.variants || []);
          setAttributes(data.attributes || []);

          // 设置选中的品牌ID，以便加载相关分类
          if (productData.brandId || productData.brandid) {
            setSelectedBrandId(productData.brandId || productData.brandid);
          }

          // 设置选中的分类ID
          if (productData.categoryId || productData.categoryid) {
            setSelectedCategoryId(productData.categoryId || productData.categoryid);
          }

          form.setFieldsValue({
            ...data,
          });
        } catch (error) {
          console.error('获取商品详情失败:', error);
          message.error('获取商品详情失败');
        } finally {
          setLoading(false);
        }
      };
      fetchProduct();
    }
  }, [isEdit, id, form]);

  // 当属性变化时，自动生成变体
  useEffect(() => {
    // 确保属性已加载且组件已挂载
    if (attributes.length > 0 && attributesAndVariantsRef.current) {
      // 使用setTimeout确保ref已经完全初始化
      setTimeout(() => {
        attributesAndVariantsRef.current.handleGenerateVariants();
      }, 0);
    }
  }, [attributes]);

  // 提交表单
  const handleSubmit = async (values: any) => {
    setSubmitting(true);
    try {
      // 合并输入的值和已有的状态
      const productData = {
        ...values,
        id: isEdit ? id : undefined,
        // 设置图片数据 - 根据ProductBo.java的属性结构
        images: images.map((img) => img.substring(4)), // 图片URL列表
        mainImage: mainImage ? mainImage.substring(4) : '', // 主图URL
        // ProductBo.java中additionalImages存储额外图片，但已被images字段替代
        additionalImages: images.filter((img) => img !== mainImage), // 可选: 保留additionalImages以兼容旧版本
        // 处理变体属性
        attributes: attributes.map((attr) => ({
          id: attr.id,
          code: attr.code,
          name: attr.name,
          options: attr.options,
          isVariantAttribute: true,
        })),
        // 处理变体
        variants: variants.map((variant) => {
          // 确保保留原始ID，只有新创建的变体才使用undefined
          const variantId =
            variant.id && !variant.id.toString().startsWith('new-') ? variant.id : undefined;

          // 构建变体属性数组
          const variantAttributes = [];
          if (variant.attributes) {
            // 如果是对象形式，转换为数组形式
            if (!Array.isArray(variant.attributes)) {
              for (const [key, value] of Object.entries(variant.attributes)) {
                const attr = attributes.find((a) => a.code === key);
                if (attr) {
                  variantAttributes.push({
                    attributeId: attr.id,
                    attributeCode: key,
                    attributeName: attr.name,
                    value: value,
                    variantAttribute: true,
                  });
                }
              }
            } else {
              // 如果已经是数组形式，直接使用
              variantAttributes.push(...variant.attributes);
            }
          }

          return {
            id: variantId,
            sku: variant.sku,
            name: variant.name,
            price: variant.price,
            specialPrice: variant.specialPrice,
            cost: variant.cost,
            isActive: variant.isActive,
            // 变体也可以有自己的图片
            mainImage: variant.mainImage,
            additionalImages: variant.additionalImages || [],
            attributes: variantAttributes,
          };
        }),
        // 设置品牌和分类信息
        brandId: selectedBrandId,
        categoryId: selectedCategoryId,
      };

      // 使用类型断言确保ProductBo类型兼容
      if (isEdit && id) {
        await apiProductUpdate(id, productData as any);
        message.success('商品更新成功');
      } else {
        await apiProductCreate(productData as any);
        message.success('商品创建成功');
      }

      navigate('/product/list');
    } catch (error) {
      console.error('保存商品失败:', error);
      message.error('保存商品失败');
    } finally {
      setSubmitting(false);
    }
  };

  // 生成变体按钮点击事件
  const generateVariants = () => {
    // 直接调用ProductAttributesAndVariants组件的handleGenerateVariants方法
    if (attributesAndVariantsRef.current) {
      attributesAndVariantsRef.current.handleGenerateVariants();
    } else {
      message.error('无法生成变体，请先添加属性');
    }
  };

  return (
    <div className='product-edit-container'>
      <div
        className='page-header'
        style={{ marginBottom: 16, display: 'flex', alignItems: 'center', gap: 16 }}
      >
        <Button
          type='link'
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/product/list')}
          style={{ paddingLeft: 0 }}
        >
          返回
        </Button>
        <Typography.Title level={4} style={{ margin: 0 }}>
          {isEdit ? '编辑商品' : '新增商品'}
        </Typography.Title>
      </div>

      <Card loading={loading}>
        <Tabs defaultActiveKey='basic'>
          <TabPane tab='基本信息' key='basic'>
            <Form
              form={form}
              layout='vertical'
              onFinish={handleSubmit}
              initialValues={{
                isActive: true,
                isFeatured: false,
                sortOrder: 0,
              }}
            >
              <Form.Item
                name='name'
                label='商品名称'
                rules={[{ required: true, message: '请输入商品名称' }]}
              >
                <I18nField>
                  <Input placeholder='请输入商品名称' />
                </I18nField>

              </Form.Item>

              <Form.Item
                name='code'
                label='商品编码'
                rules={[{ required: true, message: '请输入商品编码' }]}
            >
                <Input placeholder='请输入商品编码' />
              </Form.Item>

              <Form.Item name='description' label='商品描述'>
                <I18nField>
                  <Input.TextArea rows={4} placeholder='请输入商品描述' />
                </I18nField>
              </Form.Item>

              <div style={{ display: 'flex', gap: 16 }}>
                <Form.Item name='brandId' label='品牌' style={{ width: '50%' }}>
                  <Select
                    placeholder='请选择品牌'
                    loading={brandLoading}
                    onChange={handleBrandChange}
                  >
                    {brands.map((brand) => (
                      <Select.Option key={brand.id} value={brand.id}>
                        {brand.name}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  name='categoryId'
                  label='分类'
                  rules={[{ required: true, message: '请选择商品分类' }]}
                >
                  <Select
                    loading={categoryLoading}
                    placeholder='请选择商品分类'
                    allowClear
                    onChange={handleCategoryChange}
                  >
                    {categories.map((category) => (
                      <Select.Option key={category.id} value={category.id}>
                        {category.name}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </div>

              <div style={{ display: 'flex', gap: 16 }}>
                <Form.Item name='isActive' label='状态' valuePropName='checked'>
                  <Switch checkedChildren='启用' unCheckedChildren='停用' />
                </Form.Item>

                <Form.Item name='isFeatured' label='推荐' valuePropName='checked'>
                  <Switch checkedChildren='是' unCheckedChildren='否' />
                </Form.Item>

                <Form.Item name='sortOrder' label='排序'>
                  <InputNumber min={0} />
                </Form.Item>
              </div>

              <Divider>SEO 信息</Divider>

              <Form.Item
                name='urlKey'
                label='URL Key'
                rules={[{ required: true, message: '请输入URL Key' }]}
                tooltip='用于前端页面路由，例如：red-t-shirt'
              >
                <Input placeholder='请输入URL Key，用于前端页面路由' />
              </Form.Item>

              <Form.Item name='seoTitle' label='SEO 标题'>
                <Input placeholder='请输入SEO标题' />
              </Form.Item>

              <Form.Item name='seoDescription' label='SEO 描述'>
                <Input.TextArea rows={2} placeholder='请输入SEO描述' />
              </Form.Item>

              <Form.Item name='seoKeywords' label='SEO 关键词'>
                <Input placeholder='请输入SEO关键词，多个关键词用逗号分隔' />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button type='primary' htmlType='submit' loading={submitting}>
                    保存
                  </Button>
                  <Button onClick={() => navigate('/product/list')}>取消</Button>
                </Space>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane tab='商品图片' key='images'>
            <ProductImages
              images={images}
              setImages={setImages}
              mainImage={mainImage}
              setMainImage={setMainImage}
              isEdit={isEdit}
              id={id}
              submitting={submitting}
              onSave={() => handleSubmit(form.getFieldsValue())}
            />
          </TabPane>
          <TabPane tab='商品属性和变体' key='attributes'>
            <ProductAttributesAndVariants
              attributes={attributes}
              setAttributes={setAttributes}
              variants={variants}
              setVariants={setVariants}
              form={form}
              product={product}
              ref={attributesAndVariantsRef}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};



export default ProductEdit;

ProductEdit.auth = ['apiBrandPageList', 'apiCategoryPageList', 'apiLanguagePageList', 'apiProductCreate', 'apiProductGetById', 'apiProductUpdate'];
