import React, { useRef, useState } from 'react';
import { 
  apiProductPageList,
  apiProductSetActive,
  apiProductSetFeatured,
  apiProductDelete
} from '@/apis/apis.api';
import { PlusOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { Button, message, Modal, Space } from 'antd';
import { useNavigate } from 'react-router-dom';

const ProductList: React.FC = () => {
  const navigate = useNavigate();
  const tableRef = useRef<any>();

  const handleEdit = useMemoizedFn((id?: number) => {
    navigate(`/product/edit${id ? `/${id}` : ''}`);
  });

  const handleDelete = useMemoizedFn(async (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除该商品吗？删除后不可恢复。',
      onOk: async () => {
        try {
          await apiProductDelete(id);
          message.success('删除成功');
          tableRef.current?.reload();
        } catch (error) {
          console.error('删除商品失败:', error);
          message.error('删除失败');
        }
      },
    });
  });

  const handleSetActive = useMemoizedFn(async (id: string, isActive: boolean) => {
    try {
      await apiProductSetActive(id, isActive);
      message.success(`${isActive ? '启用' : '停用'}成功`);
      tableRef.current?.reload();
    } catch (error) {
      console.error('设置商品状态失败:', error);
      message.error(`${isActive ? '启用' : '停用'}失败`);
    }
  });

  const handleSetFeatured = useMemoizedFn(async (id: string, isFeatured: boolean) => {
    try {
      await apiProductSetFeatured(id, isFeatured);
      message.success(`${isFeatured ? '设为推荐' : '取消推荐'}成功`);
      tableRef.current?.reload();
    } catch (error) {
      console.error('设置商品推荐状态失败:', error);
      message.error(`${isFeatured ? '设为推荐' : '取消推荐'}失败`);
    }
  });

  const columns = [
    {
      title: '商品名称',
      dataIndex: 'name',
      width: 180,
      ellipsis: true,
    },
    {
      title: '商品编码',
      dataIndex: 'code',
      width: 120,
      ellipsis: true,
    },
    {
      title: 'URL Key',
      dataIndex: 'urlKey',
      width: 150,
      ellipsis: true,
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      width: 120,
      ellipsis: true,
      search: false,
      render: (_, record) => (record.brand ? record.brand.name : '-'),
    },
    {
      title: '分类',
      dataIndex: 'category',
      width: 120,
      ellipsis: true,
      search: false,
      render: (_, record) => (record.category ? record.category.name : '-'),
    },
    {
      title: '变体数量',
      dataIndex: 'variantsCount',
      width: 100,
      search: false,
      render: (_, record) => (record.variants ? record.variants.length : 0),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      valueType: 'select',
      valueEnum: {
        true: { text: '已启用', status: 'Success' },
        false: { text: '已停用', status: 'Error' },
      },
    },
    {
      title: '推荐',
      dataIndex: 'isFeatured',
      valueType: 'select',
      valueEnum: {
        true: { text: '推荐', status: 'Processing' },
        false: { text: '普通', status: 'Default' },
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 320,
      valueType: 'option',
      search: false,
      render: (_, record) => {
        return (
          <Space>
            <a onClick={() => handleEdit(record.id)}>编辑</a>
            <a onClick={() => handleSetActive(record.id, !record.isActive)}>
              {record.isActive ? '停用' : '启用'}
            </a>
            <a onClick={() => handleSetFeatured(record.id, !record.isFeatured)}>
              {record.isFeatured ? '取消推荐' : '设为推荐'}
            </a>
            <a onClick={() => handleDelete(record.id)}>删除</a>
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <ProTable<ProductBo>
        headerTitle="商品列表"
        actionRef={tableRef}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
        }}
        options={{
          density: false,
          fullScreen: true,
          reload: true,
          setting: true,
        }}
        pagination={{
          defaultPageSize: 10,
          showQuickJumper: true,
        }}
        columns={columns}
        toolBarRender={() => [
          <Button 
            key="add"
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={() => handleEdit()}
          >
            新增商品
          </Button>,
        ]}
        request={async (params, sort, filter) => {
          // 转换请求参数
          const { current, pageSize, ...rest } = params;
          
          // 构建过滤条件
          const filters = Object.entries(rest)
            .filter(([_, value]) => value !== undefined && value !== null && value !== '')
            .map(([key, value]) => {
              // 对名称和编码使用like操作符进行模糊搜索
              if (key === 'name' || key === 'code') {
                return {
                  field: key,
                  operator: 'like',
                  value,
                };
              }
              // 其他字段使用精确匹配
              return {
                field: key,
                operator: 'eq',
                value,
              };
            });

          try {
            const result = await apiProductPageList({
              current: current ? current - 1 : 0, // API使用0基索引
              size: pageSize || 10,
              filters
            });
            
            return {
              data: result.records,
              success: true,
              total: result.total,
            };
          } catch (error) {
            console.error('获取商品列表失败:', error);
            message.error('获取商品列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
      />
    </div>
  );
};


export default ProductList;

ProductList.auth = ['apiProductPageList', 'apiProductSetActive', 'apiProductSetFeatured', 'apiProductDelete'];
