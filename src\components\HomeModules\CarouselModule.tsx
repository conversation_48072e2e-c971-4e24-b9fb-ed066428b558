import React, { useState } from 'react';
import { Carousel, Button, List, Image, Space, UploadFile } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { TCarouselModule, TCarouselItem } from './types';
import BaseModuleWrapper from './BaseModuleWrapper';
import { nanoid } from 'nanoid';
import { useFormModal } from '../FormilyModal';
import { useMemoizedFn } from 'ahooks';
import { FileUtils } from '@/utils/file';

interface CarouselModuleProps {
  module: TCarouselModule;
  isFirst: boolean;
  isLast: boolean;
  onUpdate: (updatedModule: TCarouselModule) => void;
  onDelete: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onInsertBefore: () => void;
  isEditing?: boolean;
}

interface TCarouselFormData {
  link: string;
  imageUrlFileList: UploadFile[];
}



const CarouselModuleComponent: React.FC<CarouselModuleProps> = ({
  module,
  isFirst,
  isLast,
  onUpdate,
  onDelete,
  onMoveUp,
  onMoveDown,
  onInsertBefore,
  isEditing: initialIsEditing = false
}) => {
  const [isEditing, setIsEditing] = useState(initialIsEditing);
  const { formModalRef, formModalHolder } = useFormModal<TCarouselFormData>();

  const showFormModal = useMemoizedFn((item?: TCarouselItem) => {
    formModalRef.current?.show({
      modalTitle: item ? '编辑轮播图' : '添加轮播图',
      initialValues: {
        link: item?.link || '',
        imageUrlFileList: item?.imageUrl ? FileUtils.urlsToUploadFiles([item.imageUrl]) : [],
      },
      modalWidth: 800,
      onAutoSubmit: async (values) => {
        console.log("onAutoSubmit values", values);
        const data = {
          id: item?.id || nanoid(),
          link: values.link,
          imageUrl: FileUtils.uploadFilesToUrls(values.imageUrlFileList)[0],
        };
        console.log("onAutoSubmit", data);
        if (item) {
          onUpdate({
            ...module,
            items: module.items.map(item => item.id === data.id ? data : item)
          });
        } else {
          onUpdate({
            ...module,
            items: [...module.items, data]
          });
        }
      },
      schema: {
        properties: {
          imageUrlFileList: {
            title: '图片',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'FileUpload',
            'x-component-props': {
              listType: 'picture-card',
              maxCount: 1,
            },
          },
          link: {
            title: '链接',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
        },
      },
    });
  });

  const handleDeleteItem = (itemId: string) => {
    const updatedItems = module.items.filter(item => item.id !== itemId);
    onUpdate({
      ...module,
      items: updatedItems
    });
  };

  const renderCarouselContent = () => {
    if (module.items.length === 0) {
      return (
        <div className="h-64 flex items-center justify-center bg-gray-100">
          <p className="text-gray-500">暂无轮播图</p>
        </div>
      );
    }

    return (
      <Carousel autoplay arrows >
        {module.items.map(item => (
          <div key={item.id} className="h-64 bg-gray-200">
            <img
              src={item.imageUrl}
              className="h-full block mx-auto"
            />
          </div>
        ))}
      </Carousel>
    );
  };

  const renderEditContent = () => {
    return (
      <div>
        <div className="flex justify-between mb-4">
          <div className="text-lg font-medium">轮播图模块</div>
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => showFormModal()}>
              添加轮播图
            </Button>
          </Space>
        </div>

        <List
          itemLayout="horizontal"
          dataSource={module.items}
          renderItem={item => (
            <List.Item
              actions={[
                <Button icon={<EditOutlined />} onClick={() => showFormModal(item)}>编辑</Button>,
                <Button danger icon={<DeleteOutlined />} onClick={() => handleDeleteItem(item.id)}>删除</Button>
              ]}
            >
              <List.Item.Meta
                avatar={<Image src={item.imageUrl} width={100} height={60} />}
                description={item.link}
              />
            </List.Item>
          )}
        />
        {formModalHolder}
      </div>
    );
  };

  return (
    <BaseModuleWrapper
      isFirst={isFirst}
      isLast={isLast}
      onEditingChange={setIsEditing}
      onDelete={onDelete}
      onMoveUp={onMoveUp}
      onMoveDown={onMoveDown}
      onInsertBefore={onInsertBefore}
      isEditing={isEditing}
    >
      {isEditing ? renderEditContent() : renderCarouselContent()}
    </BaseModuleWrapper>
  );
};

export default CarouselModuleComponent;
