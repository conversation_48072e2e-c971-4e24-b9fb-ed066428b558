import AdminPermissionList from '@/pages/adminPermissionList';
import AdminRoleList from '@/pages/adminRoleList.tsx';
import AdminUserList from '@/pages/adminUserList.tsx';
import ApiApplicationList from '@/pages/apiApplicationList';
import ChannelBusinessTypeMappingsList from '@/pages/channelBusinessTypeMappingsList.tsx';
import ChannelLocationMappingsList from '@/pages/channelLocationMappingsList.tsx';
import ChannelsList from '@/pages/channelsList.tsx';
import ClientTransactionList from '@/pages/clientTransactionList';
import ClientUserManagement from '@/pages/clientUserManagement';
import ClientWalletManagement from '@/pages/clientWalletManagement';
import ExchangeRateRuleList from '@/pages/exchangeRateRuleList';
import HomePage from '@/pages/home';
import LocationsList from '@/pages/locationsList.tsx';
import LoginPage from '@/pages/login';
import StaticBusinessTypesList from '@/pages/staticBusinessTypesList.tsx';
import StaticLinesList from '@/pages/staticLinesList.tsx';
import StaticOrderManagement from '@/pages/staticOrderManagement';
import StaticInstanceManagement from '@/pages/staticInstanceManagement';
import {
  ApiOutlined,
  AreaChartOutlined,
  EnvironmentOutlined,
  TeamOutlined,
  TransactionOutlined,
  UserOutlined,
  WalletOutlined
} from '@ant-design/icons';
import { nanoid } from "nanoid";
import KeepAlive from "react-activation";
import { createBrowserRouter, Navigate } from "react-router-dom";
import DefaultLayout from './layouts/DefaultLayout';
import Dashboard from "./pages/dashboard";
import ClientUserDetail from './pages/clientUserDetail';
//__gen1__;
/**
 *
 */
export type TLayoutRoute = {
  name: string;
  path: string;
  component?: JSX.Element;
  redirect?: string;
  icon?: JSX.Element;
  routes?: TLayoutRoute[];
  hideInMenu?: boolean;
  hideInRouter?: boolean;
  keepAlive?: boolean;
  auth?: string[];
};

/**
 * 用于layout的菜单路由
 */
export const layoutRoute: TLayoutRoute = {
  path: '',
  name: '',
  routes: [
    {
      path: '/dashboard',
      name: '仪表盘',
      icon: <AreaChartOutlined />,
      component: <Dashboard />,
      auth: Dashboard.auth,
    },
    {
      path: '/user',
      name: '后台权限',
      icon: <UserOutlined />,
      redirect: '/userList',
      auth: AdminUserList.auth,
      routes: [
        {
          path: '/userList',
          name: '管理员列表',
          component: <AdminUserList />,
          auth: AdminUserList.auth,
        },
        {
          path: '/roleList',
          name: '角色列表',
          component: <AdminRoleList />,
          auth: AdminRoleList.auth,
        },
        {
          path: '/permissionList',
          name: '权限管理',
          component: <AdminPermissionList />,
          auth: AdminPermissionList.auth,
        },
      ],
    },
    // {
    //   path: '/internationalization',
    //   name: '国际化',
    //   icon: <GlobalOutlined />,
    //   redirect: '/languages',
    //   auth: [...CurrencyList.auth, ...LanguageList.auth, ...RegionList.auth, ...TranslationList.auth, ...ExchangeRateRuleList.auth],
    //   routes: [
    //     {
    //       path: '/currencies',
    //       name: '货币管理',
    //       icon: <DollarOutlined />,
    //       component: <CurrencyList />,
    //       auth: CurrencyList.auth,
    //     },
    //     {
    //       path: '/exchange-rates/:currencyCode',
    //       name: '汇率管理',
    //       icon: <SwapOutlined />,
    //       component: <ExchangeRateList />,
    //       auth: ExchangeRateList.auth,
    //       hideInMenu: true,
    //     },
    //     {
    //       path: '/exchange-rate-rules',
    //       name: '汇率规则管理',
    //       icon: <SwapOutlined />,
    //       component: <ExchangeRateRuleList />,
    //       auth: ExchangeRateRuleList.auth,
    //     },
    //     {
    //       path: '/languages',
    //       name: '语言管理',
    //       icon: <TranslationOutlined />,
    //       component: <LanguageList />,
    //       auth: LanguageList.auth,
    //     },
    //     {
    //       path: '/translations',
    //       name: '翻译管理',
    //       icon: <EditOutlined />,
    //       component: <TranslationList />,
    //       auth: TranslationList.auth,
    //     },
    //     {
    //       path: '/regions',
    //       name: '地区管理',
    //       icon: <EnvironmentOutlined />,
    //       component: <RegionList />,
    //       auth: RegionList.auth,
    //     },
    //   ],
    // },
    
    {
      path: '/client',
      name: '客户管理',
      icon: <TeamOutlined />,
      redirect: '/client/users',
      auth: [],
      routes: [
        // {
        //   path: '/client/users',
        //   name: '客户用户管理',
        //   icon: <UserOutlined />,
        //   component: <ClientUserList />,
        //   auth: ClientUserList.auth || [],
        // },
        {
          path: '/client/user-management',
          name: '客户用户管理',
          icon: <UserOutlined />,
          component: <ClientUserManagement />,
          auth: [],
          routes: [
            {
              path: '/client/client-user-detail/:id',
              name: '客户用户详情',
              component: <ClientUserDetail />,
              auth: [],
              hideInMenu: true,
            },
          ],
        },
        {
          path: '/client/transactions',
          name: '交易管理',
          icon: <TransactionOutlined />,
          component: <ClientTransactionList />,
          auth: [],
        },
        {
          path: '/client/wallets',
          name: '钱包管理',
          icon: <WalletOutlined />,
          component: <ClientWalletManagement />,
          auth: [],
        },
        {
          path: '/client/applications',
          name: 'API应用管理',
          icon: <ApiOutlined />,
          component: <ApiApplicationList />,
          auth: [],
        },
      ],
    },
    // {
    //   path: '/content',
    //   name: '内容管理',
    //   icon: <FileTextOutlined />,
    //   redirect: '/articles',
    //   auth: ArticleList.auth,
    //   routes: [
    //     {
    //       path: '/articles',
    //       name: '文章管理',
    //       icon: <FileTextOutlined />,
    //       component: <ArticleList />,
    //       auth: ArticleList.auth,
    //     },
    //     {
    //       path: '/article-edit',
    //       name: '文章编辑',
    //       component: <ArticleEdit />,
    //       auth: ArticleEdit.auth,
    //       hideInMenu: true,
    //     },
    //     {
    //       path: '/notification-list',
    //       name: '通知模板',
    //       icon: <NotificationOutlined />,
    //       component: <NotificationList />,
    //       auth: NotificationList.auth,
    //     },
    //   ],
    // },
    {
      path: '/proxy',
      name: '代理管理',
      icon: <EnvironmentOutlined />,
      redirect: '/proxy/static-orders',
      auth: [...ChannelsList.auth, ...LocationsList.auth, ...StaticLinesList.auth, ...StaticBusinessTypesList.auth, ...ChannelLocationMappingsList.auth, ...ChannelBusinessTypeMappingsList.auth],
      routes: [
        {
          path: '/proxy/static-orders',
          name: '静态代理订单',
          component: <StaticOrderManagement />,
          auth: [],
        },
        {
          path: '/proxy/static-instances',
          name: '静态代理实例',
          component: <StaticInstanceManagement />,
          auth: [],
        },
        {
          path: '/proxy/channels',
          name: '渠道管理',
          component: <ChannelsList />,
          auth: ChannelsList.auth,
        },
        {
          path: '/proxy/locations',
          name: '地理位置管理',
          icon: <EnvironmentOutlined />,
          component: <LocationsList />,
          auth: LocationsList.auth,
        },
        {
          path: '/proxy/static-lines',
          name: '静态线路管理',
          icon: <EnvironmentOutlined />,
          component: <StaticLinesList />,
          auth: StaticLinesList.auth,
        },
        {
          path: '/proxy/static-business-types',
          name: '静态业务类型管理',
          icon: <EnvironmentOutlined />,
          component: <StaticBusinessTypesList />,
          auth: StaticBusinessTypesList.auth,
        },
        {
          path: '/proxy/channel-location-mappings',
          name: '渠道地理位置映射',
          icon: <EnvironmentOutlined />,
          component: <ChannelLocationMappingsList />,
          auth: ChannelLocationMappingsList.auth,
        },
        {
          path: '/proxy/channel-business-type-mappings',
          name: '渠道业务类型映射',
          icon: <EnvironmentOutlined />,
          component: <ChannelBusinessTypeMappingsList />,
          auth: ChannelBusinessTypeMappingsList.auth,
        },
      ],
    },
    // {
    //   path: '/settings',
    //   name: '设置管理',
    //   redirect: '/settings/home',
    //   icon: <SettingOutlined />,
    //   auth: [...HomeSetting.auth, ...SystemSettings.auth],
    //   routes: [
    //     {
    //       path: '/settings/home',
    //       name: '首页设置',
    //       icon: <HomeOutlined />,
    //       component: <HomeSetting />,
    //       auth: HomeSetting.auth,
    //     },
    //     {
    //       path: '/settings/system',
    //       name: '系统基础设置',
    //       icon: <ToolOutlined />,
    //       component: <SystemSettings />,
    //       auth: SystemSettings.auth,
    //     },
    //   ],
    // },
  ],
};

export const keepAlivePathSet = new Set();

/**
 * 递归地将layoutRoute转换为router所需的RouteObject
 */
const mapRouteObject = (routes: TLayoutRoute[]) => {
  const result: {
    path: string;
    element: JSX.Element;
  }[] = [];
  routes.forEach(item => {
    if (item.component) {
      if (item.keepAlive) {
        keepAlivePathSet.add(item.path);
      }
      result.push({
        path: item.path,
        element: item.keepAlive ? (
          <KeepAlive id={nanoid()} name={item.path}>
            {item.component}
          </KeepAlive>
        ) : item.component,
      });
    } else if (item.redirect) {
      result.push({ path: item.path, element: <Navigate to={item.redirect} replace={true} />, });
    }
    if (item.routes) {
      result.push(...mapRouteObject(item.routes));
    }
  });
  return result;
};

const router = createBrowserRouter([
  {
    path: "/login",
    element: <LoginPage />,
  },
  {
    path: '/',
    element: <HomePage />,
  },
  {
    path: '/exchange-rate-rules/:currencyCode',
    element: <ExchangeRateRuleList />,
  },
  {
    path: "",
    element: <DefaultLayout />,
    children: mapRouteObject([layoutRoute]),
  },
]);

/**
 * 递归地将layoutRoute转换为[path, name][]
 */
const mapPathInfo = (routes: TLayoutRoute[], breadcrumb: {
  path?: string;
  title: string;
}[] = []) => {
  const result: [string, {
    title: string;
    breadcrumb: {
      path?: string;
      title: string;
    }[]
  }][] = [];
  routes.forEach(item => {
    const newBreadcrumb = [...breadcrumb]
    if (item.name) {
      newBreadcrumb.push({
        title: item.name,
        path: item.redirect ? undefined : item.path,
      })
    }
    if (item.name && !item.redirect) {
      result.push([item.path, {
        title: item.name,
        breadcrumb: newBreadcrumb,
      }]);
    }

    if (item.routes) {
      result.push(...mapPathInfo(item.routes, [...newBreadcrumb]));
    }
  });
  return result;
};

/**
 * path对应name的Map
 */
export const pathInfoMap = new Map(mapPathInfo([layoutRoute]));


/**
 * 递归地将layoutRoute转换为[path, name][]
 */
const mapName = (routes: TLayoutRoute[]) => {
  const result: [string, string][] = [];
  routes.forEach(item => {
    if (item.name && !item.redirect) {
      result.push([item.path, item.name]);
    }

    if (item.routes) {
      result.push(...mapName(item.routes));
    }
  });
  return result;
};

/**
 * path对应name的Map
 */
export const pathNameMap = new Map(mapName([layoutRoute]));

export default router;