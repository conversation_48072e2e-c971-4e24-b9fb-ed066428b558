import { apiCreateTag, apiDeleteTag, apiGetAllTags, apiGetProductsByTagId, apiUpdateProductsForTag, apiUpdateTag, apiProductPageList, apiGetTagDetails } from "@/apis/apis.api";
import { useFormModal } from "@/components/FormilyModal";
import { ToolsUtil } from "@/utils/tools";
import { ActionType, ProTable } from "@ant-design/pro-components";
import { useMemoizedFn } from "ahooks";
import { App, Button, Modal, Popconfirm, Space } from "antd";
import { useRef, useState } from "react";

interface TTagFormData {
  name: string;
  description: string;
  type?: number;
  sortOrder?: number;
}

function TagList() {
  const { formModalRef, formModalHolder } = useFormModal<TTagFormData>();
  const { message } = App.useApp();
  const [productSelectVisible, setProductSelectVisible] = useState(false);
  const [currentTagId, setCurrentTagId] = useState<string | null>(null);
  const [selectedProductIds, setSelectedProductIds] = useState<string[]>([]);
  const productTable = useRef<ActionType>(); 
  const [productKeyword, setProductKeyword] = useState('');


  const showFormModal = useMemoizedFn(async (recorId?: string) => {
    let record: TTagBo | null = null;
    if(recorId) {
      record = await apiGetTagDetails(recorId);
    }
    formModalRef.current?.show({
      modalTitle: record ? '编辑标签' : '添加标签',
      initialValues: record ? {
        name: record.name,
        description: record.description,
        type: record.type,
        sortOrder: record.sortOrder,
      } : {
        type: 0,
        sortOrder: 100,
      },
      modalWidth: 800,
      onAutoSubmit: async (values) => {
        const data: Partial<TTagBo> = {
          name: values.name,
          description: values.description,
          type: values.type,
          sortOrder: values.sortOrder,
        };
        if (record) {
          await apiUpdateTag(record.id, data);
        } else {
          await apiCreateTag(data);
        }
        message.success('保存成功');
        productTable.current?.reload();
      },
      schema: {
        properties: {
          name: {
            title: '标签名称',
            type: 'string',
            required: true,
            'x-decorator': 'I18nFormItem',
            'x-component': 'Input',
          },
          description: {
            title: '标签描述',
            type: 'string',
            'x-decorator': 'I18nFormItem',
            'x-component': 'Input.TextArea',
          },
          type: {
            title: '标签类型',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            enum: [
              { label: '百分比', value: 0 },
              { label: '数值', value: 1 },
            ],
            'x-component-props': {
              placeholder: '请选择标签类型'
            }
          },
          sortOrder: {
            title: '排序',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'NumberPicker',
            "x-decorator-props": {
              tooltip: '越小越靠前'
            }
          },
        },
      },
    });
  });

  const handleDelete = useMemoizedFn((record: TTagBo) => {
    apiDeleteTag(record.id).then(() => {
      message.success('删除成功');
    }).catch(() => {
      message.error('删除失败');
    });
  });

  // 打开关联产品的弹窗
  const handleOpenProductSelector = async (tagId: string) => {
    setCurrentTagId(tagId);
    setProductSelectVisible(true);
    setProductKeyword('');
    const products = await apiGetProductsByTagId(tagId);
    setSelectedProductIds(products.map(p => p.id));
  };

  // 确认产品选择
  const handleConfirmProductSelection = async () => {
    if (currentTagId === null) return;

    try {
      await apiUpdateProductsForTag(currentTagId, selectedProductIds);
      message.success('更新关联产品成功');
      setProductSelectVisible(false);
    } catch (error) {
      console.error('更新关联产品失败', error);
      message.error('更新关联产品失败');
    }
  };

  return (
    <div>
      <ProTable<TTagBo>
        headerTitle="标签列表"
        rowKey="id"
        toolBarRender={() => [
          <Button type="primary" key="add" onClick={() => showFormModal()}>添加标签</Button>,
        ]}
        columns={[
          {
            title: '标签名称',
            dataIndex: 'name',
            key: 'name',
          },
          {
            title: '标签描述',
            dataIndex: 'description',
            key: 'description',
          },
          {
            title: '标签类型',
            dataIndex: 'type',
            key: 'type',
            valueEnum: {
              0: '百分比',
              1: '数值',
            },
            render: (text) => text === 0 ? '百分比' : '数值',
          },
          {
            title: '排序',
            dataIndex: 'sortOrder',
            key: 'sortOrder',
            search: false,
          },
          {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            search: false,
            render: (_text, record) => (
              <Space>
                <a onClick={() => showFormModal(record.id)}>编辑</a>
                <a onClick={() => handleOpenProductSelector(record.id)}>关联产品</a>
                <Popconfirm title="确定要删除吗？" onConfirm={() => handleDelete(record)}>
                  <a>删除</a>
                </Popconfirm>
              </Space>
            ),
          },
        ]}
        request={async (params, sorter) => {
          const filters: TQueryFilter[] = [];

          if (params.name) {
            filters.push({ field: 'name', operator: 'like', value: params.name });
          }

          if (params.description) {
            filters.push({ field: 'description', operator: 'like', value: params.description });
          }

          if (params.type) {
            filters.push({ field: 'type', operator: 'eq', value: Number(params.type) });
          }

          const sorts = ToolsUtil.tableSorterToPageListReqSort(sorter);

          const res = await apiGetAllTags({
            current: params.current - 1,
            size: params.pageSize,
            filters,
            sorts,
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}

      />
      {formModalHolder}

      {/* 产品选择弹窗 */}
      <Modal
        title={`关联产品（已选择${selectedProductIds.length}个）`}
        open={productSelectVisible}
        onOk={handleConfirmProductSelection}
        onCancel={() => setProductSelectVisible(false)}
        width={800}
        destroyOnClose
      >
        <ProTable<ProductBo>
          className="-mx-6"
          actionRef={productTable}
          search={false}
          toolbar={{
            search: {
              value: productKeyword,
              onChange: (e) => {
                setProductKeyword(e.target.value);
              },
              onSearch: () => {
                productTable.current?.reload();
              },
            },
          }}
          tableAlertRender={false}
          rowKey="id"
          columns={[
            {
              title: 'ID',
              dataIndex: 'id',
              width: 100,
            },
            {
              title: '名称',
              dataIndex: 'name',
            },
          ]}
          pagination={{
            pageSize: 6,

          }}
          request={async (params) => {
            const res = await apiProductPageList({
              current: params.current - 1,
              size: params.pageSize,
              filters: productKeyword.trim() ? [{ field: 'name', operator: 'like', value: productKeyword.trim() }] : [],
              sorts: [],
            })
            return {
              data: res.records,
              total: res.total,
              success: true,
            }
          }}
          rowSelection={{
            type: 'checkbox',

            selectedRowKeys: selectedProductIds,
            onSelect: (record, selected) => {
                const s = new Set(selectedProductIds)
                if (selected) {
                    s.add(record.id)
                } else {
                    s.delete(record.id)
                }
                console.log(Array.from(s))
                setSelectedProductIds(Array.from(s))
            },
            

          }}
        />
      
      </Modal>
    </div>
  );
}

export default TagList;

TagList.auth = ['apiCreateTag', 'apiDeleteTag', 'apiGetAllTags', 'apiGetProductsByTagId', 'apiUpdateProductsForTag', 'apiUpdateTag', 'apiProductPageList', 'apiGetTagDetails'];
