import { apiCreateStaticOrder, apiGetOrders, apiUpdateOrder, apiDeleteOrder } from '@/apis/gen.apis';
import { useFormModal } from '@/components/FormilyModal';
import UserObj from '@/components/UserObj';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { App, Button, Space, Popconfirm, Tag } from 'antd';
import { useRef } from 'react';

type TEditFormData = {
  userId: string;
  orderNo: string;
  thirdOrderNo: string;
  orderType: TOrderTypeEnum;
  quantity: number;
  duration: number;
  unitPrice: number;
  totalPrice: number;
  currency: string;
  proxyType: string;
  businessName: string;
  orderTime: string;
  proxyStartTime: string;
  proxyEndTime: string;
  orderDetails: string;
  purchaseParams: string;
  remarks: string;
  autoRenewal: boolean;
  renewalDaysBefore: number;
  discountAmount: number;
  discountCode: string;
  actualPayAmount: number;
};

function StaticOrderManagement() {
  const tableRef = useRef<ActionType>();
  const { message } = App.useApp();

  const { formModalRef: editFormModalRef, formModalHolder: editFormModalHolder } =
    useFormModal<TEditFormData>();

  const showEditModal = useMemoizedFn((record?: TStaticOrderBO) => {
    editFormModalRef.current?.show({
      modalTitle: record ? '修改静态代理订单' : '新增静态代理订单',
      modalWidth: 1200,
      onAutoSubmit: async (values) => {
        if (record?.id) {
          await apiUpdateOrder(record.id, values);
        } else {
          await apiCreateStaticOrder(values);
        }

        message.success('提交成功');
        tableRef.current?.reload();
      },
      initialValues: record
        ? {
            ...record,
            orderTime: record.orderTime ? new Date(record.orderTime).toISOString().slice(0, 16) : '',
            proxyStartTime: record.proxyStartTime ? new Date(record.proxyStartTime).toISOString().slice(0, 16) : '',
            proxyEndTime: record.proxyEndTime ? new Date(record.proxyEndTime).toISOString().slice(0, 16) : '',
          }
        : {
            quantity: 1,
            duration: 30,
            unitPrice: 0,
            totalPrice: 0,
            discountAmount: 0,
            actualPayAmount: 0,
            autoRenewal: false,
            renewalDaysBefore: 7,
          },
      schema: {
        type: 'object',
        properties: {
          userId: {
            title: '用户ID',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          orderNo: {
            title: '订单号',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          thirdOrderNo: {
            title: '第三方订单号',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          orderType: {
            title: '订单类型',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              options: [
                { label: '购买', value: 'PURCHASE' },
                { label: '续费', value: 'RENEWAL' },
              ],
            },
          },
          quantity: {
            title: '数量',
            type: 'number',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              min: 1,
              style: { width: '100%' },
            },
          },
          duration: {
            title: '时长(天)',
            type: 'number',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              min: 1,
              style: { width: '100%' },
            },
          },
          unitPrice: {
            title: '单价',
            type: 'number',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              precision: 2,
              min: 0,
              style: { width: '100%' },
            },
          },
          totalPrice: {
            title: '总价',
            type: 'number',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              precision: 2,
              min: 0,
              style: { width: '100%' },
            },
          },
          currency: {
            title: '货币',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              options: [
                { label: '人民币 (CNY)', value: 'CNY' },
                { label: '美元 (USD)', value: 'USD' },
                { label: '欧元 (EUR)', value: 'EUR' },
              ],
            },
          },
          proxyType: {
            title: '代理类型',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          businessName: {
            title: '业务名称',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          orderTime: {
            title: '订单时间',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'DatePicker',
            'x-component-props': {
              showTime: true,
              format: 'YYYY-MM-DD HH:mm:ss',
            },
          },
          proxyStartTime: {
            title: '代理开始时间',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'DatePicker',
            'x-component-props': {
              showTime: true,
              format: 'YYYY-MM-DD HH:mm:ss',
            },
          },
          proxyEndTime: {
            title: '代理结束时间',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'DatePicker',
            'x-component-props': {
              showTime: true,
              format: 'YYYY-MM-DD HH:mm:ss',
            },
          },
          discountAmount: {
            title: '折扣金额',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              precision: 2,
              min: 0,
              style: { width: '100%' },
            },
          },
          discountCode: {
            title: '折扣码',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          actualPayAmount: {
            title: '实际支付金额',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              precision: 2,
              min: 0,
              style: { width: '100%' },
            },
          },
          autoRenewal: {
            title: '自动续费',
            type: 'boolean',
            'x-decorator': 'FormItem',
            'x-component': 'Switch',
          },
          renewalDaysBefore: {
            title: '续费提前天数',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              min: 1,
              style: { width: '100%' },
            },
          },
          orderDetails: {
            title: '订单详情',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 3,
            },
          },
          remarks: {
            title: '备注',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
          },
        },
      },
    });
  });

  const handleDelete = useMemoizedFn(async (id: string) => {
    await apiDeleteOrder(id);
    message.success('删除成功');
    tableRef.current?.reload();
  });

  const getStatusColor = (status: TOrderStatusEnum) => {
    const colorMap: Record<TOrderStatusEnum, string> = {
      PENDING: 'orange',
      PAID: 'blue',
      PROCESSING: 'cyan',
      COMPLETED: 'green',
      FAILED: 'red',
      CANCELLED: 'gray',
      REFUNDED: 'purple',
    };
    return colorMap[status] || 'default';
  };

  const getStatusText = (status: TOrderStatusEnum) => {
    const textMap: Record<TOrderStatusEnum, string> = {
      PENDING: '待处理',
      PAID: '已支付',
      PROCESSING: '处理中',
      COMPLETED: '已完成',
      FAILED: '失败',
      CANCELLED: '已取消',
      REFUNDED: '已退款',
    };
    return textMap[status] || status;
  };

  return (
    <div>
      <ProTable<TStaticOrderBO>
        actionRef={tableRef}
        headerTitle='静态代理订单管理'
        rowKey='id'
        toolBarRender={() => [
          <Button key='add' type='primary' onClick={() => showEditModal()}>
            新增
          </Button>,
        ]}
        search={{
          defaultCollapsed: false,
        }}
        scroll={{ x: 1800 }}
        columns={[
          { title: '订单号', dataIndex: 'orderNo', width: 200, fixed: 'left' },
          { title: '三方订单号', dataIndex: 'thirdOrderNo', width: 150 },
          { title: '用户', dataIndex: 'user', width: 120, render: (_text, record) => <UserObj userObj={record.userObj} /> },
          { 
            title: '订单类型', 
            dataIndex: 'orderType',
            width: 100,
            valueType: 'select',
            valueEnum: {
              1: '购买',
              2: '续费',
            },
          },
          { 
            title: '状态', 
            dataIndex: 'status',
            width: 100,
            // "PENDING" | "PAID" | "PROCESSING" | "COMPLETED" | "FAILED" | "CANCELLED" | "REFUNDED";
            valueType: 'select',
            valueEnum: {
              '0': '待处理',
              '1': '已支付',
              '2': '处理中',
              '3': '已完成',
              '4': '失败',
              '5': '已取消',
              '6': '已退款',
            },
          },
          { title: '数量', dataIndex: 'quantity', width: 80 },
          { title: '时长(天)', dataIndex: 'duration', width: 100 },
          { 
            title: '单价', 
            dataIndex: 'unitPrice',
            width: 120,
            render: (price: number, record) => `${price} ${record.currency}`,
          },
          { 
            title: '总价', 
            dataIndex: 'totalPrice',
            width: 120,
            render: (price: number, record) => `${price} ${record.currency}`,
          },
          { 
            title: '实际支付', 
            dataIndex: 'actualPayAmount',
            width: 120,
            render: (amount: number, record) => `${amount} ${record.currency}`,
          },
          { title: '代理类型', dataIndex: 'proxyType', width: 120 },
          { title: '业务名称', dataIndex: 'businessName', width: 150 },
          { 
            title: '订单时间', 
            dataIndex: 'orderTime',
            valueType: 'dateTime',
            width: 180,
          },
          { 
            title: '代理开始时间', 
            dataIndex: 'proxyStartTime',
            valueType: 'dateTime',
            width: 180,
          },
          { 
            title: '代理结束时间', 
            dataIndex: 'proxyEndTime',
            valueType: 'dateTime',
            width: 180,
          },
          { 
            title: '自动续费', 
            dataIndex: 'autoRenewal',
            width: 100,
            render: (autoRenewal: boolean) => (
              <Tag color={autoRenewal ? 'green' : 'gray'}>
                {autoRenewal ? '是' : '否'}
              </Tag>
            ),
          },
          {
            title: '操作',
            valueType: 'option',
            key: 'option',
            width: 150,
            fixed: 'right',
            render: (_text, record) => {
              return (
                <Space>
                  <a onClick={() => showEditModal(record)}>修改</a>
                  <Popconfirm
                    title="确定要删除这个订单吗？"
                    onConfirm={() => handleDelete(record.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <a style={{ color: 'red' }}>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
        request={async (params, sorter) => {
          const filters: any[] = [];

          if (params.orderNo) {
            filters.push({ field: 'orderNo', operator: 'like', value: params.orderNo });
          }
          if (params.userId) {
            filters.push({ field: 'userId', operator: 'eq', value: params.userId });
          }
          if (params.status) {
            filters.push({ field: 'status', operator: 'eq', value: params.status });
          }
          if (params.orderType) {
            filters.push({ field: 'orderType', operator: 'eq', value: params.orderType });
          }
          if (params.businessName) {
            filters.push({ field: 'businessName', operator: 'like', value: params.businessName });
          }

          const res = await apiGetOrders({
            current: params.current - 1,
            size: params.pageSize,
            filters,
            sorts: [],
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}
      />
      {editFormModalHolder}
    </div>
  );
}

export default StaticOrderManagement;

StaticOrderManagement.auth = ['apiCreateStaticOrder', 'apiGetOrders', 'apiUpdateOrder', 'apiDeleteOrder'];
