import {
  Input,
  Password,
  Select,
  TreeSelect,
  DatePicker,
  TimePicker,
  NumberPicker,
  Transfer,
  Cascader,
  Radio,
  Checkbox,
  Upload,
  Switch,
  FormLayout,
  FormItem,
  FormGrid,
  FormButtonGroup,
  Space,
  Submit,
  Reset,
  ArrayCards,
  ArrayItems,
  ArrayTable,
  ArrayTabs,
  FormCollapse,
  FormStep,
  FormTab,
  Editable,
  PreviewText,
  SelectTable,
 
} from "@formily/antd-v5";
import { createSchemaField } from "@formily/react";
import { createForm, Field, FormPathPattern, IFormProps, onFieldReact } from "@formily/core";
import { useMemo } from "react";
import { action } from '@formily/reactive'
import { Card } from "antd";
import FileUpload from "./FileUpload";
import RichText from "./RichText";
import SizedBox from "./SizedBox";
import MoneyCent from "./MoneyCent";
import I18nFormItem from "./I18nFormItem";

export const SchemaField = createSchemaField({
  components: {
    Card,
    Input,
    Password,
    Select,
    TreeSelect,
    DatePicker,
    TimePicker,
    NumberPicker,
    Transfer,
    Cascader,
    Radio,
    Checkbox,
    Upload,
    Switch,
    FormLayout,
    FormItem,
    FormGrid,
    FormButtonGroup,
    Space,
    Submit,
    Reset,
    ArrayCards,
    ArrayItems,
    ArrayTable,
    ArrayTabs,
    FormCollapse,
    FormStep,
    FormTab,
    Editable,
    PreviewText,
    FileUpload,
    RichText,
    SizedBox,
    SelectTable,
    MoneyCent,
    I18nFormItem,
  },
  scope: {},
});


type GetPropsType<T> = T extends (props: infer P) => void ? P : unknown;
export type TSchemaFieldProps = GetPropsType<typeof SchemaField>;

export function useForm<TFormData extends object>(options?: IFormProps<TFormData>) {
  return useMemo(() => createForm<TFormData>(options), []);
}

export const useAsyncDataSource = (
  pattern: FormPathPattern,
  service: (field: Field) => Promise<{ label: string; value: any }[]>
) => {
  onFieldReact(pattern, (field: any) => {
    field.loading = true
    service(field).then(
      action.bound((data) => {
        field.dataSource = data
        field.loading = false
      })
    )
  })
}