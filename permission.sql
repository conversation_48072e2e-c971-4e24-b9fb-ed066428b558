INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245468618752, 'apiGetUserById', 1, 'Admin User Management/根据ID获取管理员用户'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiGetUserById'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245468618753, 'apiUpdateUser', 1, 'Admin User Management/更新管理员用户'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiUpdateUser'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245468618754, 'apiUpdatePassword', 1, 'Admin User Management/更新管理员用户密码'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiUpdatePassword'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245468618755, 'apiCreateUser', 1, 'Admin User Management/创建新的管理员用户'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCreateUser'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245468618756, 'apiAddRoleToUser', 1, 'Admin User Management/为管理员用户添加角色（通过角色ID）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAddRoleToUser'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245468618757, 'apiRemoveRoleFromUser', 1, 'Admin User Management/从管理员用户中移除角色（通过角色ID）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiRemoveRoleFromUser'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245468618758, 'apiAddRoleToUserByCode', 1, 'Admin User Management/为管理员用户添加角色（通过角色代码）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAddRoleToUserByCode'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245468618759, 'apiRemoveRoleFromUserByCode', 1, 'Admin User Management/从管理员用户中移除角色（通过角色代码）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiRemoveRoleFromUserByCode'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245468618760, 'apiAssignRolesToUser', 1, 'Admin User Management/为管理员用户分配角色（通过角色ID）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAssignRolesToUser'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813056, 'apiAssignRolesToUserByCodes', 1, 'Admin User Management/为管理员用户分配角色（通过角色代码）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAssignRolesToUserByCodes'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813057, 'apiGetUsers', 1, 'Admin User Management/分页获取管理员用户'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiGetUsers'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813058, 'apiSetUserActive', 1, 'Admin User Management/设置管理员用户活动状态'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiSetUserActive'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813059, 'apiAdminUserPageList', 1, 'Admin User Management/分页获取管理员用户'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminUserPageList'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813060, 'apiGetRoles', 1, '管理员角色管理/分页获取管理员角色'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiGetRoles'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813061, 'apiGetRoleById', 1, '管理员角色管理/通过ID获取管理员角色'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiGetRoleById'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813062, 'apiUpdateRole', 1, '管理员角色管理/更新管理员角色'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiUpdateRole'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813063, 'apiGetAllRoles', 1, '管理员角色管理/获取所有管理员角色'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiGetAllRoles'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813064, 'apiCreateRole', 1, '管理员角色管理/创建新的管理员角色'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCreateRole'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813065, 'apiAddPermissionToRole', 1, '管理员角色管理/为管理员角色添加权限（通过权限ID）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAddPermissionToRole'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813066, 'apiRemovePermissionFromRole', 1, '管理员角色管理/从管理员角色中移除权限（通过权限ID）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiRemovePermissionFromRole'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813067, 'apiAddPermissionToRoleByCode', 1, '管理员角色管理/为管理员角色添加权限（通过权限代码）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAddPermissionToRoleByCode'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813068, 'apiRemovePermissionFromRoleByCode', 1, '管理员角色管理/从管理员角色中移除权限（通过权限代码）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiRemovePermissionFromRoleByCode'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813069, 'apiGetRolePermissions', 1, '管理员角色管理/通过ID获取管理员角色权限'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiGetRolePermissions'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813070, 'apiAssignPermissionsToRole', 1, '管理员角色管理/为管理员角色分配权限（通过权限ID）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAssignPermissionsToRole'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813071, 'apiAssignPermissionsToRoleByCodes', 1, '管理员角色管理/为管理员角色分配权限（通过权限代码）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAssignPermissionsToRoleByCodes'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813072, 'apiSetRoleActive', 1, '管理员角色管理/设置管理员角色激活状态'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiSetRoleActive'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813073, 'apiGetRoleByCode', 1, '管理员角色管理/通过代码获取管理员角色'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiGetRoleByCode'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813074, 'apiAdminRolePageList', 1, '管理员角色管理/分页获取管理员角色'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminRolePageList'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813075, 'apiAdminRoleGetById', 1, '管理员角色管理/通过ID获取管理员角色'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminRoleGetById'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813076, 'apiAdminRoleUpdate', 1, '管理员角色管理/更新管理员角色'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminRoleUpdate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813077, 'apiAdminRoleGetAll', 1, '管理员角色管理/获取所有管理员角色'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminRoleGetAll'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813078, 'apiAdminRoleCreate', 1, '管理员角色管理/创建新的管理员角色'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminRoleCreate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813079, 'apiAdminRoleAddPermission', 1, '管理员角色管理/为管理员角色添加权限（通过权限ID）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminRoleAddPermission'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813080, 'apiAdminRoleRemovePermission', 1, '管理员角色管理/从管理员角色中移除权限（通过权限ID）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminRoleRemovePermission'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813081, 'apiAdminRolePermissions', 1, '管理员角色管理/通过ID获取管理员角色权限'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminRolePermissions'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813082, 'apiAdminRoleAssignPermissions', 1, '管理员角色管理/为管理员角色分配权限（通过权限ID）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminRoleAssignPermissions'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813083, 'apiAdminRoleSetActive', 1, '管理员角色管理/设置管理员角色激活状态'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminRoleSetActive'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813084, 'apiAdminRoleGetByCode', 1, '管理员角色管理/通过代码获取管理员角色'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminRoleGetByCode'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813085, 'apiAdminPermissionList', 1, '管理员权限管理/获取所有管理员权限'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminPermissionList'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813086, 'apiAdminPermissionPageList', 1, '管理员权限管理/分页获取管理员权限'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminPermissionPageList'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813087, 'apiAdminPermissionCreate', 1, '管理员权限管理/创建新的管理员权限'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminPermissionCreate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813088, 'apiAdminPermissionUpdate', 1, '管理员权限管理/更新管理员权限'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminPermissionUpdate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813089, 'apiAdminPermissionGetById', 1, '管理员权限管理/通过ID获取管理员权限'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminPermissionGetById'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813090, 'apiAdminPermissionSetActive', 1, '管理员权限管理/设置管理员权限的激活状态'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminPermissionSetActive'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813091, 'apiAdminPermissionGetByCode', 1, '管理员权限管理/通过代码获取管理员权限'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiAdminPermissionGetByCode'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813092, 'apiCurrencyPageList', 1, '管理员货币管理/分页获取所有货币'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCurrencyPageList'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813093, 'apiCurrencyCreate', 1, '管理员货币管理/创建新货币'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCurrencyCreate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813094, 'apiCurrencyUpdate', 1, '管理员货币管理/更新现有货币'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCurrencyUpdate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813095, 'apiCurrencySetDefault', 1, '管理员货币管理/将货币设置为默认货币'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCurrencySetDefault'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813096, 'apiCurrencySetActive', 1, '管理员货币管理/激活或停用货币'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCurrencySetActive'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813097, 'apiLanguagePageList', 1, '管理员语言管理/分页获取所有语言'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiLanguagePageList'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813098, 'apiLanguageCreate', 1, '管理员语言管理/创建新语言'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiLanguageCreate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813099, 'apiLanguageUpdate', 1, '管理员语言管理/更新现有语言'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiLanguageUpdate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813100, 'apiLanguageSetDefault', 1, '管理员语言管理/设置默认语言'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiLanguageSetDefault'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813101, 'apiLanguageSetActive', 1, '管理员语言管理/激活或停用语言'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiLanguageSetActive'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813102, 'apiRegionPageList', 1, '管理员区域管理/分页获取所有区域'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiRegionPageList'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813103, 'apiRegionCreate', 1, '管理员区域管理/创建新区域'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiRegionCreate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813104, 'apiRegionUpdate', 1, '管理员区域管理/更新区域'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiRegionUpdate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813105, 'apiRegionSetActive', 1, '管理员区域管理/设置区域的激活状态'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiRegionSetActive'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813106, 'apiTranslationPageList', 1, '管理员翻译管理/获取所有翻译（分页）'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiTranslationPageList'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813107, 'apiTranslationCreate', 1, '管理员翻译管理/为多种语言创建新的翻译键'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiTranslationCreate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813108, 'apiTranslationUpdate', 1, '管理员翻译管理/更新或创建特定键在多种语言中的翻译'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiTranslationUpdate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813109, 'apiTranslationsByKey', 1, '管理员翻译管理/获取特定键在所有语言中的翻译'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiTranslationsByKey'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813110, 'apiArticlePageList', 1, '管理员文章管理/分页获取所有文章'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiArticlePageList'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813111, 'apiArticleGetById', 1, '管理员文章管理/通过ID获取文章'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiArticleGetById'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813112, 'apiArticleCreate', 1, '管理员文章管理/创建新文章'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiArticleCreate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813113, 'apiArticleUpdate', 1, '管理员文章管理/更新文章'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiArticleUpdate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813114, 'apiBrandPageList', 1, '管理员产品品牌管理/分页获取所有品牌'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiBrandPageList'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813115, 'apiBrandGetById', 1, '管理员产品品牌管理/根据ID获取品牌'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiBrandGetById'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813116, 'apiBrandCreate', 1, '管理员产品品牌管理/创建新品牌'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiBrandCreate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813117, 'apiBrandUpdate', 1, '管理员产品品牌管理/更新品牌'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiBrandUpdate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813118, 'apiBrandDelete', 1, '管理员产品品牌管理/删除品牌'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiBrandDelete'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813119, 'apiBrandSetActive', 1, '管理员产品品牌管理/设置品牌激活状态'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiBrandSetActive'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813120, 'apiBrandSetFeatured', 1, '管理员产品品牌管理/设置品牌推荐状态'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiBrandSetFeatured'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813121, 'apiBrandGetByCategory', 1, '管理员产品品牌管理/分页获取分类下的品牌'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiBrandGetByCategory'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813122, 'apiBrandAddCategory', 1, '管理员产品品牌管理/将分类添加到品牌'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiBrandAddCategory'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813123, 'apiBrandRemoveCategory', 1, '管理员产品品牌管理/从品牌中移除分类'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiBrandRemoveCategory'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813124, 'apiCategoryPageList', 1, '产品分类管理/分页获取所有分类'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCategoryPageList'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813125, 'apiCategoryGetById', 1, '产品分类管理/根据ID获取分类'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCategoryGetById'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813126, 'apiCategoryCreate', 1, '产品分类管理/创建新分类'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCategoryCreate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813127, 'apiCategoryUpdate', 1, '产品分类管理/更新分类'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCategoryUpdate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813128, 'apiCategoryDelete', 1, '产品分类管理/删除分类'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCategoryDelete'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813129, 'apiCategoryTree', 1, '产品分类管理/获取完整分类树'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCategoryTree'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813130, 'apiCategorySubtree', 1, '产品分类管理/获取分类子树'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCategorySubtree'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813131, 'apiCategoryGetByCode', 1, '产品分类管理/根据编码获取分类'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCategoryGetByCode'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813132, 'apiCategoryRootsPage', 1, '产品分类管理/分页获取根分类'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCategoryRootsPage'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813133, 'apiCategoryByParentId', 1, '产品分类管理/根据父级ID分页获取分类'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCategoryByParentId'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813134, 'apiCategoryByLevel', 1, '产品分类管理/根据层级分页获取分类'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCategoryByLevel'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813135, 'apiCategorySetActive', 1, '产品分类管理/设置分类激活状态'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCategorySetActive'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813136, 'apiExchangeRatesByCurrency', 1, '管理员货币管理/获取特定货币的汇率'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiExchangeRatesByCurrency'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813137, 'apiExchangeRateAddRule', 1, '管理员货币管理/为汇率添加规则'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiExchangeRateAddRule'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813138, 'apiExchangeRateApplyRule', 1, '管理员货币管理/将已有规则应用到汇率上'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiExchangeRateApplyRule'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813139, 'apiProductGetById', 1, '产品管理/通过ID获取产品'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiProductGetById'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813140, 'apiProductGetDetails', 1, '产品管理/通过ID获取产品'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiProductGetDetails'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813141, 'apiProductUpdate', 1, '产品管理/更新产品'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiProductUpdate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813142, 'apiProductDelete', 1, '产品管理/删除产品'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiProductDelete'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813143, 'apiProductSetFeatured', 1, '产品管理/设置产品精选状态'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiProductSetFeatured'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813144, 'apiProductSetActive', 1, '产品管理/设置产品活动状态'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiProductSetActive'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813145, 'apiProductCreate', 1, '产品管理/创建新产品'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiProductCreate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813146, 'apiProductPageList', 1, '产品管理/分页获取所有产品'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiProductPageList'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813147, 'apiClientUserPageList', 1, '客户用户管理/分页获取客户用户'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiClientUserPageList'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813148, 'apiClientUserGetAll', 1, '客户用户管理/获取所有客户用户'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiClientUserGetAll'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813149, 'apiClientUserGetAllActive', 1, '客户用户管理/获取所有活跃客户用户'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiClientUserGetAllActive'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813150, 'apiClientUserGetById', 1, '客户用户管理/根据ID获取客户用户'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiClientUserGetById'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813151, 'apiClientUserCreate', 1, '客户用户管理/创建新客户用户'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiClientUserCreate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813152, 'apiClientUserUpdate', 1, '客户用户管理/更新客户用户信息'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiClientUserUpdate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813153, 'apiClientUserResetPassword', 1, '客户用户管理/重置客户用户密码'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiClientUserResetPassword'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813154, 'apiClientUserSetActive', 1, '客户用户管理/设置客户用户活跃状态'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiClientUserSetActive'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813155, 'apiWalletGetUserWallets', 1, '管理员钱包管理/获取用户的所有钱包'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiWalletGetUserWallets'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813156, 'apiWalletGetUserActiveWallets', 1, '管理员钱包管理/获取用户的激活钱包'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiWalletGetUserActiveWallets'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813157, 'apiWalletGetUserMainWallet', 1, '管理员钱包管理/获取用户的主钱包'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiWalletGetUserMainWallet'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813158, 'apiWalletGetUserWalletByCurrency', 1, '管理员钱包管理/获取用户的特定货币钱包'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiWalletGetUserWalletByCurrency'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813159, 'apiWalletCreateDefaultWallet', 1, '管理员钱包管理/为用户创建默认钱包'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiWalletCreateDefaultWallet'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813160, 'apiWalletDeposit', 1, '管理员钱包管理/钱包充值'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiWalletDeposit'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813161, 'apiWalletWithdraw', 1, '管理员钱包管理/钱包提现'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiWalletWithdraw'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813162, 'apiWalletGetTransactionsPage', 1, '管理员钱包管理/分页获取钱包交易记录'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiWalletGetTransactionsPage'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813163, 'apiWalletGetPendingDeposits', 1, '管理员钱包管理/获取用户的待审批存款申请'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiWalletGetPendingDeposits'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813164, 'apiOrderGetAllOrders', 1, '管理员订单管理/分页获取所有订单'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiOrderGetAllOrders'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813165, 'apiOrderGetOrderDetails', 1, '管理员订单管理/获取订单详情'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiOrderGetOrderDetails'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813166, 'apiOrderGetOrderByOrderNumber', 1, '管理员订单管理/根据订单号获取订单'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiOrderGetOrderByOrderNumber'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813167, 'apiOrderUpdateOrderStatus', 1, '管理员订单管理/更新订单状态'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiOrderUpdateOrderStatus'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813168, 'apiOrderUpdatePaymentStatus', 1, '管理员订单管理/更新订单支付状态'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiOrderUpdatePaymentStatus'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813169, 'apiOrderAddAdminNotes', 1, '管理员订单管理/添加管理员备注'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiOrderAddAdminNotes'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813170, 'apiOrderPlatformReceive', 1, '管理员订单管理/平台收货'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiOrderPlatformReceive'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813171, 'apiOrderPlatformShip', 1, '管理员订单管理/平台发货到买家'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiOrderPlatformShip'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813172, 'apiOrderPlatformVerify', 1, '管理员订单管理/平台验证完成'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiOrderPlatformVerify'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813173, 'apiOrderPlatformReceiveReturn', 1, '管理员订单管理/平台确认退货并退回卖家'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiOrderPlatformReceiveReturn'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813174, 'apiPriceQuoteGetAll', 1, '报价管理/分页获取所有报价'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiPriceQuoteGetAll'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813175, 'apiPriceQuoteGetDetails', 1, '报价管理/获取报价详情'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiPriceQuoteGetDetails'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813176, 'apiPriceQuoteUpdateStatus', 1, '报价管理/更新报价状态'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiPriceQuoteUpdateStatus'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813177, 'apiPriceQuoteGetPage', 1, '报价管理/分页获取所有报价'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiPriceQuoteGetPage'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813178, 'apiRemittanceGetPendingDepositsPage', 1, '管理员钱包管理/分页获取所有待审核的汇款充值记录'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiRemittanceGetPendingDepositsPage'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813179, 'apiRemittanceApproveDeposit', 1, '管理员钱包管理/批准存款申请'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiRemittanceApproveDeposit'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813180, 'apiRemittanceRejectDeposit', 1, '管理员钱包管理/拒绝存款申请'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiRemittanceRejectDeposit'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813181, 'apiSystemSettingGetById', 1, '系统设置管理/根据ID获取系统设置'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiSystemSettingGetById'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813182, 'apiSystemSettingUpdate', 1, '系统设置管理/更新现有系统设置'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiSystemSettingUpdate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813183, 'apiSystemSettingDelete', 1, '系统设置管理/删除系统设置'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiSystemSettingDelete'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813184, 'apiSystemSettingGetByKey', 1, '系统设置管理/根据键获取系统设置'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiSystemSettingGetByKey'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813185, 'apiSystemSettingGetAll', 1, '系统设置管理/获取所有系统设置'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiSystemSettingGetAll'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813186, 'apiSystemSettingCreate', 1, '系统设置管理/创建新的系统设置'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiSystemSettingCreate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813187, 'apiSystemSettingPageList', 1, '系统设置管理/分页获取系统设置'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiSystemSettingPageList'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813188, 'apiSystemSettingSetActive', 1, '系统设置管理/设置系统设置激活状态'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiSystemSettingSetActive'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813189, 'apiSystemSettingGetByGroup', 1, '系统设置管理/根据分组获取系统设置'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiSystemSettingGetByGroup'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813190, 'apiGetTagDetails', 1, '管理员标签管理/获取标签详情'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiGetTagDetails'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813191, 'apiUpdateTag', 1, '管理员标签管理/更新标签'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiUpdateTag'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813192, 'apiCreateTag', 1, '管理员标签管理/创建新标签'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiCreateTag'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813193, 'apiGetAllTags', 1, '管理员标签管理/分页获取所有标签'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiGetAllTags'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813194, 'apiGetProductsByTagId', 1, '管理员标签管理/获取标签关联的所有产品'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiGetProductsByTagId'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813195, 'apiUpdateProductsForTag', 1, '管理员标签管理/更新标签关联的产品'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiUpdateProductsForTag'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813196, 'apiExchangeRateRuleCreate', 1, '管理员货币管理/创建新的汇率规则'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiExchangeRateRuleCreate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813197, 'apiExchangeRateRuleUpdate', 1, '管理员货币管理/更新现有汇率规则'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiExchangeRateRuleUpdate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813198, 'apiExchangeRateRulePageList', 1, '管理员货币管理/分页获取汇率规则'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiExchangeRateRulePageList'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813199, 'apiNotificationTemplateGet', 1, '通知模板管理/获取通知模板详情'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiNotificationTemplateGet'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813200, 'apiNotificationTemplateUpdate', 1, '通知模板管理/更新通知模板'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiNotificationTemplateUpdate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813201, 'apiNotificationTemplateEnable', 1, '通知模板管理/启用通知模板'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiNotificationTemplateEnable'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813202, 'apiNotificationTemplateDisable', 1, '通知模板管理/禁用通知模板'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiNotificationTemplateDisable'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813203, 'apiNotificationTemplatePage', 1, '通知模板管理/获取通知模板列表'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiNotificationTemplatePage'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813204, 'apiNotificationTemplateCreate', 1, '通知模板管理/创建通知模板'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiNotificationTemplateCreate'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813205, 'apiNotificationTemplateSend', 1, '通知模板管理/使用模板发送通知'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiNotificationTemplateSend'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813206, 'apiNotificationTemplateBatchSend', 1, '通知模板管理/批量使用模板发送通知'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiNotificationTemplateBatchSend'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813207, 'apiDeleteTranslationsByKey', 1, '管理员翻译管理/删除特定键的所有翻译'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiDeleteTranslationsByKey'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813208, 'apiArticleDelete', 1, '管理员文章管理/删除文章'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiArticleDelete'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813209, 'apiWalletGetTransactions', 1, '管理员钱包管理/获取钱包交易记录'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiWalletGetTransactions'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813210, 'apiWalletApproveDeposit', 1, '管理员钱包管理/批准存款申请'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiWalletApproveDeposit'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813211, 'apiWalletRejectDeposit', 1, '管理员钱包管理/拒绝存款申请'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiWalletRejectDeposit'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813212, 'apiDeleteTag', 1, '管理员标签管理/删除标签'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiDeleteTag'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813213, 'apiExchangeRateRuleDelete', 1, '管理员货币管理/删除汇率规则'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiExchangeRateRuleDelete'
);

INSERT INTO public.admin_permission (id, code, is_active, name)
SELECT 7320819245472813214, 'apiNotificationTemplateDelete', 1, '通知模板管理/删除通知模板'
WHERE NOT EXISTS (
    SELECT 1 FROM public.admin_permission WHERE code = 'apiNotificationTemplateDelete'
);