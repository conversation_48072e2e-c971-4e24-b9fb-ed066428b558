import {
  apiCategoryByLevel,
  apiCategoryByParentId,
  apiCategoryCreate,
  apiCategoryDelete,
  apiCategoryPageList,
  apiCategoryRootsPage,
  apiCategorySetActive,
  apiCategoryUpdate
} from '@/apis/apis.api';
import { ToolsUtil } from '@/utils/tools';
import { PlusOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { Button, Card, message, Modal, Space, Tabs } from 'antd';
import React, { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import I18nText from '@/components/I18nText';

const { TabPane } = Tabs;

const CategoryList: React.FC = () => {
  const navigate = useNavigate();
  const tableRef = useRef<any>();
  const [activeTab, setActiveTab] = useState<string>('all');
  const [selectedParentId, setSelectedParentId] = useState<number | null>(null);
  const [selectedLevel, setSelectedLevel] = useState<number | null>(null);

  const handleEdit = useMemoizedFn((id?: number) => {
    navigate(`/category/edit${id ? `/${id}` : ''}`);
  });

  const handleDelete = useMemoizedFn(async (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除该类别吗？删除后不可恢复。',
      onOk: async () => {
        try {
          await apiCategoryDelete(id);
          message.success('删除成功');
          tableRef.current?.reload();
        } catch (error) {
          console.error('删除类别失败:', error);
          message.error('删除失败');
        }
      },
    });
  });

  const handleSetActive = useMemoizedFn(async (id: string, isActive: boolean) => {
    try {
      await apiCategorySetActive(id, isActive);
      message.success(`${isActive ? '启用' : '停用'}成功`);
      tableRef.current?.reload();
    } catch (error) {
      console.error('设置类别状态失败:', error);
      message.error(`${isActive ? '启用' : '停用'}失败`);
    }
  });

  const handleViewChildren = useMemoizedFn((parentid: string) => {
    setActiveTab('custom');
    setSelectedParentId(parentId);
    setSelectedLevel(null);
    tableRef.current?.reload({
      parentId
    });
  });

  const columns = [
    {
      title: '类别名称',
      dataIndex: 'name',
      width: 180,
      ellipsis: true,
      render: (name: string) => <I18nText value={name} />,
    },
    {
      title: '编码',
      dataIndex: 'code',
      width: 120,
      ellipsis: true,
    },
    {
      title: '层级',
      dataIndex: 'level',
      width: 80,
      valueType: 'select',
      valueEnum: {
        1: { text: '一级类别' },
        2: { text: '二级类别' },
        3: { text: '三级类别' },
      },
    },
    {
      title: '父类别ID',
      dataIndex: 'parentId',
      width: 100,
      render: (parentId) => parentId || '-',
      search: false,
    },
    {
      title: '排序',
      dataIndex: 'sortOrder',
      width: 80,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      width: 80,
      valueType: 'select',
      valueEnum: {
        true: { text: '已启用', status: 'Success' },
        false: { text: '已停用', status: 'Error' },
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
      search: false,
    },
    {
      title: '操作',
      key: 'action',
      width: 240,
      valueType: 'option',
      search: false,
      render: (_, record) => {
        return (
        <Space>
          <a onClick={() => handleEdit(record.id)}>编辑</a>
          {record.level < 3 && (
            <a onClick={() => handleViewChildren(record.id)}>查看子类别</a>
          )}
          <a onClick={() => handleSetActive(record.id, !record.isActive)}>
            {record.isActive ? '停用' : '启用'}
          </a>
          <a onClick={() => handleDelete(record.id)}>删除</a>
        </Space>
      )},
    },
  ];

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setSelectedParentId(null);
    setSelectedLevel(null);

    if (key === 'level1') {
      setSelectedLevel(1);
    } else if (key === 'level2') {
      setSelectedLevel(2);
    } else if (key === 'level3') {
      setSelectedLevel(3);
    } else if (key === 'roots') {
      // 根类别，不需要设置额外参数
    }

    tableRef.current?.reload();
  };

  return (
    <div>
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane tab="全部类别" key="all" />
        <TabPane tab="根类别" key="roots" />
        <TabPane tab="一级类别" key="level1" />
        <TabPane tab="二级类别" key="level2" />
        <TabPane tab="三级类别" key="level3" />
        {selectedParentId !== null && <TabPane tab="自定义查询" key="custom" />}
      </Tabs>

      <ProTable
        actionRef={tableRef}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
        }}
        options={{
          density: false,
          fullScreen: true,
          reload: true,
          setting: true,
        }}
        pagination={{
          defaultPageSize: 10,
          showQuickJumper: true,
        }}
        columns={columns}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleEdit()}
          >
            添加类别
          </Button>,
        ]}
        request={async (params, sorter) => {
          const filters: any[] = [];

          // 添加搜索条件
          if (params.name) {
            filters.push({ field: 'name', operator: 'like', value: params.name });
          }
          if (params.code) {
            filters.push({ field: 'code', operator: 'like', value: params.code });
          }
          if (params.level) {
            filters.push({ field: 'level', operator: 'eq', value: params.level });
          }
          if (params.parentId) {
            filters.push({ field: 'parentId', operator: 'eq', value: params.parentId });
          }
          if (params.isActive !== undefined) {
            filters.push({ field: 'isActive', operator: 'eq', value: params.isActive });
          }

          let sorts = ToolsUtil.tableSorterToPageListReqSort(sorter);

          // 添加默认排序，激活状态优先
          if (!sorts || sorts.length === 0) {
            sorts = [{ field: 'isActive', direction: 'DESC' }, { field: 'sortOrder', direction: 'ASC' }];
          }

          try {
            let res;

            // 根据当前选中的标签页和搜索条件选择不同的API
            if (activeTab === 'roots') {
              res = await apiCategoryRootsPage({
                current: params.current - 1,
                size: params.pageSize,
                filters,
                sorts,
              });
            } else if (selectedParentId !== null) {
              res = await apiCategoryByParentId(selectedParentId, {
                current: params.current - 1,
                size: params.pageSize,
                filters,
                sorts,
              });
            } else if (selectedLevel !== null) {
              res = await apiCategoryByLevel(selectedLevel, {
                current: params.current - 1,
                size: params.pageSize,
                filters,
                sorts,
              });
            } else {
              res = await apiCategoryPageList({
                current: params.current - 1,
                size: params.pageSize,
                filters,
                sorts,
              });
            }

            return {
              total: res.total || 0,
              data: res.records.map((item) => ({ ...item, children: undefined })) || [],
              success: true,
            };
          } catch (error) {
            console.error('获取类别列表失败:', error);
            message.error('获取类别列表失败');
            return {
              total: 0,
              data: [],
              success: false,
            };
          }
        }}
      />
    </div>
  );
};

export default CategoryList;

CategoryList.auth = ['apiCategoryByLevel', 'apiCategoryByParentId', 'apiCategoryCreate', 'apiCategoryDelete', 'apiCategoryPageList', 'apiCategoryRootsPage', 'apiCategorySetActive', 'apiCategoryUpdate'];
