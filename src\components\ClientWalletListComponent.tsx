import { apiCreateWallet, apiGetWallets, apiUpdateWallet, apiDeleteWallet } from '@/apis/gen.apis';
import { useFormModal } from '@/components/FormilyModal';
import { useClientUserSearch } from '@/hooks/useClientUserSearch';
import { AgreeDicts } from '@/utils/agreeDict';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { App, Button, Space, Popconfirm, Tag } from 'antd';
import { ValueType } from 'exceljs';
import { useRef } from 'react';

type TEditFormData = {
  userId: string;
  balance: number;
  frozenAmount: number;
  currency: string;
  type: string;
  note: string;
};

interface ClientWalletListComponentProps {
  userId?: string; // 可选的用户ID，用于过滤该用户的钱包
  showUserIdColumn?: boolean; // 是否显示用户ID列
  headerTitle?: string; // 自定义标题
  showAddButton?: boolean; // 是否显示新增按钮
}

function ClientWalletListComponent({
  userId,
  showUserIdColumn = true,
  headerTitle = '客户钱包列表',
  showAddButton = true
}: ClientWalletListComponentProps) {
  const tableRef = useRef<ActionType>();
  const { message } = App.useApp();

  const { formModalRef: editFormModalRef, formModalHolder: editFormModalHolder } =
    useFormModal<TEditFormData>();

  const showEditModal = useMemoizedFn((record?: TClientUserWalletBO) => {
    editFormModalRef.current?.show({
      modalTitle: record ? '修改钱包' : '新增钱包',
      modalWidth: 800,
      onAutoSubmit: async (values) => {
        // 如果传入了userId，自动设置到表单数据中
        const formData = userId ? { ...values, userId } : values;

        if (record?.id) {
          await apiUpdateWallet(record.id, formData);
        } else {
          await apiCreateWallet(formData);
        }

        message.success('提交成功');
        tableRef.current?.reload();
      },
      initialValues: record
        ? {
          ...record,
        }
        : userId ? {
          userId,
          balance: 0,
          frozenAmount: 0,
        } : {
          balance: 0,
          frozenAmount: 0,
        },
      schema: {
        type: 'object',
        properties: {
          userId: {
            title: '用户ID',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-disabled': !!userId, // 如果传入了userId，则禁用编辑
          },
          currency: {
            title: '货币',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              options: AgreeDicts.ClientWallet_currency.options,
            },
          },
          type: {
            title: '钱包类型',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              options: AgreeDicts.ClientWallet_type.options,
            },
          },
          balance: {
            title: '余额',
            type: 'number',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'NumberPicker',
            'x-component-props': {
              precision: 8,
              min: 0,
              style: { width: '100%' },
            },
          },
          frozenAmount: {
            title: '冻结金额',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'NumberPicker',
            'x-component-props': {
              precision: 8,
              min: 0,
              style: { width: '100%' },
            },
          },
          note: {
            title: '备注',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 3,
            },
          },
        },
      },
    });
  });

  const handleDelete = useMemoizedFn(async (id: string) => {
    await apiDeleteWallet(id);
    message.success('删除成功');
    tableRef.current?.reload();
  });

  const clientUserSearchProps = useClientUserSearch()


  // 构建列配置
  const columns = [
    ...(showUserIdColumn ? [
      { title: '用户ID', dataIndex: 'userId', width: 150, valueType: 'select', fieldProps: clientUserSearchProps },
    ] : []),

    { title: '货币', dataIndex: 'currency', width: 100, valueType: 'select', valueEnum: AgreeDicts.ClientWallet_currency.valueLabelMap },
    { title: '钱包类型', dataIndex: 'type', width: 120, valueType: 'select', valueEnum: AgreeDicts.ClientWallet_type.valueLabelMap },
    {
      title: '余额',
      dataIndex: 'balance',
      width: 150,
      search: false,
      render: (balance: number, record: TClientUserWalletBO) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
          {balance.toFixed(2)} {record.currency}
        </span>
      ),
    },
    {
      title: '冻结金额',
      dataIndex: 'frozenAmount',
      search: false,
      width: 150,
      render: (frozenAmount: number, record: TClientUserWalletBO) => (
        <span style={{ color: frozenAmount > 0 ? '#ff4d4f' : '#666' }}>
          {frozenAmount.toFixed(2)} {record.currency}
        </span>
      ),
    },
    {
      title: '可用余额',
      dataIndex: 'availableBalance',
      search: false,
      width: 150,
      render: (availableBalance: number, record: TClientUserWalletBO) => (
        <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
          {availableBalance.toFixed(2)} {record.currency}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      width: 100,
      valueType: 'select',
      valueEnum: {
        true: { text: '启用', color: 'green' },
        false: { text: '禁用', color: 'red' },
      },
    },
    {
      title: '最后交易时间',
      dataIndex: 'lastTransactionTime',
      width: 180,
      valueType: 'dateTime',
      search: false,
    },
    { title: '备注', dataIndex: 'note', ellipsis: true, width: 200, search: false, },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      search: false,
      width: 180,
    },
    {
      title: '操作',
      valueType: 'option' as const,
      key: 'option',
      width: 150,
      render: (_text: any, record: TClientUserWalletBO) => {
        return (
          <Space>
            <a onClick={() => showEditModal(record)}>修改</a>
            <Popconfirm
              title="确定要删除这个钱包吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <a style={{ color: 'red' }}>删除</a>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <ProTable<TClientUserWalletBO>
        actionRef={tableRef}
        headerTitle={headerTitle}
        rowKey='id'
        toolBarRender={() => showAddButton ? [
          <Button key='add' type='primary' onClick={() => showEditModal()}>
            新增
          </Button>,
        ] : []}
        search={{
          defaultCollapsed: false,
        }}
        columns={columns as any}
        request={async (params, sorter) => {
          const filters: any[] = [];

          // 如果传入了userId，自动添加用户过滤条件
          if (userId) {
            filters.push({ field: 'userId', operator: 'eq', value: userId });
          }

          if (params.userId && !userId) { // 只有在没有预设userId时才允许搜索
            filters.push({ field: 'userId', operator: 'eq', value: params.userId });
          }
          if (params.currency) {
            filters.push({ field: 'currency', operator: 'eq', value: params.currency });
          }
          if (params.type) {
            filters.push({ field: 'type', operator: 'eq', value: params.type });
          }
          if (params.enabled) {
            filters.push({ field: 'enabled', operator: 'eq', value: params.enabled });
          }

          const res = await apiGetWallets({
            current: params.current - 1,
            size: params.pageSize,
            filters,
            sorts: [],
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}
      />
      {editFormModalHolder}
    </div>
  );
}

export default ClientWalletListComponent;
