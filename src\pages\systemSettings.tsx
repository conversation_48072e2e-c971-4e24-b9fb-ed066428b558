import React, { useEffect, useState } from 'react';
import { Card, Form, Input, Button, message, Typography, Spin, Upload, Image } from 'antd';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { apiSystemSettingGetAll, apiSystemSettingUpdate, apiSystemSettingCreate } from '@/apis/apis.api';
import HttpUtils from '@/utils/http';
import type { UploadChangeParam } from 'antd/es/upload';
import type { UploadFile } from 'antd/es/upload/interface';
import { useModel } from 'foca';

// Import system setting keys from the model
import {
  WEBSITE_NAME_KEY,
  WEBSITE_LOGO_KEY,
  PAYMENT_ACCOUNT_NAME_KEY,
  PAYMENT_ACCOUNT_NUMBER_KEY,
  PAYMENT_BANK_NAME_KEY,
  BID_DEPOSIT_PERCENTAGE_KEY,
  PLATFORM_FEE_PERCENTAGE_KEY,
  ORDER_AUTO_CANCEL_TIMEOUT_KEY,
  systemSettingsModel
} from '@/stores/systemSettingsModel';

interface SystemSettings {
  websiteName: string;
  websiteLogo: string;
  paymentAccountName: string;
  paymentAccountNumber: string;
  paymentBankName: string;
  bidDepositPercentage: string;
  platformFeePercentage: string;
  orderAutoCancelTimeout: string;
}

const SystemSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialValues, setInitialValues] = useState<SystemSettings>({
    websiteName: '',
    websiteLogo: '',
    paymentAccountName: '',
    paymentAccountNumber: '',
    paymentBankName: '',
    bidDepositPercentage: '',
    platformFeePercentage: '',
    orderAutoCancelTimeout: '24'
  });

  // State for logo upload
  const [logoUrl, setLogoUrl] = useState<string>('');
  const [logoLoading, setLogoLoading] = useState<boolean>(false);

  // Handle logo upload
  const handleLogoChange = (info: UploadChangeParam<UploadFile>) => {
    if (info.file.status === 'uploading') {
      setLogoLoading(true);
      return;
    }

    if (info.file.status === 'done') {
      const url = info.file.response?.url || '';
      setLogoUrl(url);
      setLogoLoading(false);
    }
  };

  // Custom upload function
  const customUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;
    try {
      const url = await HttpUtils.uploadFile(file);
      // Ensure the URL is a plain string without extra quotes
      const cleanUrl = url.replace(/^"|"$/g, '');
      setLogoUrl(cleanUrl); // Update the logo URL state directly
      onSuccess({ url: cleanUrl }, file);
    } catch (error) {
      onError(error);
    }
  };

  // Before upload validation
  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return false;
    }

    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片大小不能超过2MB!');
      return false;
    }

    return true;
  };

  // Fetch current system settings
  const fetchSettings = async () => {
    setLoading(true);
    try {
      // Get all settings at once
      const allSettings = await apiSystemSettingGetAll();

      // Create a map for easier access
      const settingsMap = allSettings.reduce((map, setting) => {
        map[setting.key] = setting;
        return map;
      }, {} as Record<string, TSystemSettingBo>);

      // Extract values for our form fields
      const values = {
        websiteName: settingsMap[WEBSITE_NAME_KEY]?.value || '',
        websiteLogo: settingsMap[WEBSITE_LOGO_KEY]?.value || '',
        paymentAccountName: settingsMap[PAYMENT_ACCOUNT_NAME_KEY]?.value || '',
        paymentAccountNumber: settingsMap[PAYMENT_ACCOUNT_NUMBER_KEY]?.value || '',
        paymentBankName: settingsMap[PAYMENT_BANK_NAME_KEY]?.value || '',
        bidDepositPercentage: settingsMap[BID_DEPOSIT_PERCENTAGE_KEY]?.value || '20',
        platformFeePercentage: settingsMap[PLATFORM_FEE_PERCENTAGE_KEY]?.value || '5',
        orderAutoCancelTimeout: settingsMap[ORDER_AUTO_CANCEL_TIMEOUT_KEY]?.value || '24'
      };

      // Set logo URL for display
      if (settingsMap[WEBSITE_LOGO_KEY]?.value) {
        try {
          // Try to parse the value in case it's a JSON string with quotes
          const logoValue = settingsMap[WEBSITE_LOGO_KEY].value;
          // Remove any extra quotes that might be present
          const cleanedValue = logoValue.replace(/^"|"$/g, '');
          setLogoUrl(cleanedValue);
        } catch (e) {
          // If parsing fails, use the value as is
          setLogoUrl(settingsMap[WEBSITE_LOGO_KEY].value);
        }
      }

      setInitialValues(values);
      form.setFieldsValue(values);
    } catch (error) {
      console.error('获取系统设置失败:', error);
      message.error('获取系统设置失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  // Update a system setting by key
  const updateSystemSetting = async (key: string, value: string, allSettings: TSystemSettingBo[]) => {
    try {
      // Find the setting in our cached settings
      const setting = allSettings.find(s => s.key === key);

      if (setting) {
        // If setting exists, update it
        await apiSystemSettingUpdate(setting.id, { value });
        return true;
      } else {
        // If setting doesn't exist, create it
        const group = key.startsWith('client.transaction') ? 'Transaction' :
                     key.startsWith('client.order') ? 'Order' : 'system';
        await apiSystemSettingCreate({
          key,
          value,
          active: true,
          group,
          description: key.includes('bid_deposit_percentage') ? '出价时冻结的押金比例（百分比）' :
                     key.includes('platform_fee_percentage') ? '平台交易费用比例（百分比）' :
                     key.includes('auto_cancel_unpaid') ? '订单支付超时时间（小时）' : ''
        });
        return true;
      }
    } catch (error) {
      console.error(`更新系统设置 ${key} 失败:`, error);
      return false;
    }
  };

  // Handle form submission
  const handleSubmit = async (values: SystemSettings) => {
    setLoading(true);
    try {
      // Get all settings first to have the latest data
      const allSettings = await apiSystemSettingGetAll();

      // Ensure logoUrl doesn't have extra quotes
      const cleanLogoUrl = logoUrl.replace(/^"|"$/g, '');

      // Update the system settings model
      systemSettingsModel.setSettings({
        websiteName: values.websiteName,
        websiteLogo: cleanLogoUrl
      });

      const results = await Promise.all([
        updateSystemSetting(WEBSITE_NAME_KEY, values.websiteName, allSettings),
        updateSystemSetting(WEBSITE_LOGO_KEY, cleanLogoUrl, allSettings),
        updateSystemSetting(PAYMENT_ACCOUNT_NAME_KEY, values.paymentAccountName, allSettings),
        updateSystemSetting(PAYMENT_ACCOUNT_NUMBER_KEY, values.paymentAccountNumber, allSettings),
        updateSystemSetting(PAYMENT_BANK_NAME_KEY, values.paymentBankName, allSettings),
        updateSystemSetting(BID_DEPOSIT_PERCENTAGE_KEY, values.bidDepositPercentage, allSettings),
        updateSystemSetting(PLATFORM_FEE_PERCENTAGE_KEY, values.platformFeePercentage, allSettings),
        updateSystemSetting(ORDER_AUTO_CANCEL_TIMEOUT_KEY, values.orderAutoCancelTimeout, allSettings)
      ]);

      if (results.every(result => result)) {
        message.success('系统设置已更新');
        fetchSettings(); // Refresh data
      } else {
        message.error('部分系统设置更新失败');
      }
    } catch (error) {
      console.error('更新系统设置失败:', error);
      message.error('更新系统设置失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Spin spinning={loading}>
      <Card
        title={<Typography.Title level={4}>系统基础设置</Typography.Title>}
        bordered={false}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={initialValues}
        >
          <Form.Item
            name="websiteName"
            label="网站名称"
            rules={[{ required: true, message: '请输入网站名称' }]}
          >
            <Input placeholder="请输入网站名称" />
          </Form.Item>

          <Form.Item
            label="网站Logo"
            extra="建议尺寸: 200px × 60px, 文件大小不超过2MB"
          >
            <Upload
              name="logo"
              listType="picture-card"
              showUploadList={false}
              beforeUpload={beforeUpload}
              onChange={handleLogoChange}
              customRequest={customUpload}
            >
              {logoUrl ? (
                <div style={{ position: 'relative', width: '100%', height: '100%' }}>
                  <Image
                    src={logoUrl}
                    alt="logo"
                    style={{ width: '100%', height: '100%', objectFit: 'contain' }}
                    preview={false}
                  />
                </div>
              ) : (
                <div>
                  {logoLoading ? <LoadingOutlined /> : <PlusOutlined />}
                  <div style={{ marginTop: 8 }}>上传Logo</div>
                </div>
              )}
            </Upload>
          </Form.Item>

          <Form.Item
            name="paymentAccountName"
            label="网站汇款账户名称"
            rules={[{ required: true, message: '请输入网站汇款账户名称' }]}
          >
            <Input placeholder="请输入网站汇款账户名称" />
          </Form.Item>

          <Form.Item
            name="paymentAccountNumber"
            label="网站汇款账号"
            rules={[{ required: true, message: '请输入网站汇款账号' }]}
          >
            <Input placeholder="请输入网站汇款账号" />
          </Form.Item>

          <Form.Item
            name="paymentBankName"
            label="网站汇款银行"
            rules={[{ required: true, message: '请输入网站汇款银行' }]}
          >
            <Input placeholder="请输入网站汇款银行" />
          </Form.Item>

          <Form.Item
            name="bidDepositPercentage"
            label="出价时冻结的押金比例（百分比）"
            rules={[{ required: true, message: '请输入出价时冻结的押金比例' }]}
          >
            <Input placeholder="请输入出价时冻结的押金比例" suffix="%" />
          </Form.Item>

          <Form.Item
            name="platformFeePercentage"
            label="平台交易费用比例（百分比）"
            rules={[{ required: true, message: '请输入平台交易费用比例' }]}
          >
            <Input placeholder="请输入平台交易费用比例" suffix="%" />
          </Form.Item>

          <Form.Item
            name="orderAutoCancelTimeout"
            label="订单支付超时时间（小时）"
            rules={[{ required: true, message: '请输入订单支付超时时间' }]}
            tooltip="超过此时间未支付的订单将被自动取消"
          >
            <Input placeholder="请输入订单支付超时时间" suffix="小时" />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              保存设置
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </Spin>
  );
};

// Add auth property to the component
export default SystemSettings;

SystemSettings.auth = ['apiSystemSettingGetAll', 'apiSystemSettingUpdate', 'apiSystemSettingCreate'];
