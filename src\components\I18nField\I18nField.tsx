import { cloneElement, isValidElement, useState, useEffect, useMemo } from "react";
import { useModel } from "foca";
import { languageModel } from "@/stores/languageModel";

export type TI18nFieldProps = {
  children: React.ReactElement;
  value?: TI18nFieldValue | string;
  onChange?: (value: string) => void;
  defaultCollapsed?: boolean;
}

function I18nField(props: TI18nFieldProps) {
  const { defaultCollapsed = true } = props;
  const [collapsed, setCollapsed] = useState(defaultCollapsed);

  // Use language model from store
  const { languages, initialized } = useModel(languageModel);

  // Load languages if not initialized
  useEffect(() => {
    if (!initialized) {
      languageModel.loadLanguages();
    }
  }, [initialized]);

  // Fallback to these languages if no languages are found
  const fallbackLanguages = [
    'zh-CN',
    'en-US',
  ];

  // Get language codes from the store or use fallback
  const availableLanguages = languages.length > 0
    ? languages.map(lang => lang.code)
    : fallbackLanguages;

  // Get language name from code using the store
  const getLanguageName = (code: string) => {
    return languageModel.getLanguageName(code);
  };

  // Get the default language from the available languages
  const defaultLanguage = useMemo(() => {
    // Find the language marked as default
    const defaultLang = languages.find(lang => lang.isDefault);
    if (defaultLang) return defaultLang.code;

    // If no default language is set, use the first available language
    return availableLanguages.length > 0 ? availableLanguages[0] : fallbackLanguages[0];
  }, [languages, availableLanguages, fallbackLanguages]);

  // Parse value if it's a string
  const parsedValue = useMemo(() => {
    // If it's a string, try to parse it as JSON
    if (typeof props.value === 'string') {
      try {
        const parsed = JSON.parse(props.value);
        // Check if it has the _i18n flag
        if (parsed && parsed._i18n === true) {
          return parsed;
        } else {
          // If no _i18n flag, treat it as a simple string for the default language
          return {
            _i18n: true,
            [defaultLanguage]: props.value
          };
        }
      } catch (e) {
        // If parsing fails, treat it as a simple string for the default language
        return {
          _i18n: true,
          [defaultLanguage]: props.value
        };
      }
    }
    // If it's an object, check if it has the _i18n flag
    if (props.value && typeof props.value === 'object') {
      if (props.value._i18n === true) {
        return props.value;
      } else {
        // If no _i18n flag, create a new object with the flag
        return {
          _i18n: true,
          ...props.value
        };
      }
    }
    // Default empty object with _i18n flag
    return { _i18n: true };
  }, [props.value, defaultLanguage]);

  const itemOnChange = (lang: string, value: string) => {
    // Create a new object with the updated value
    const newValue = {
      _i18n: true,
      ...parsedValue,
      [lang]: value
    };

    // Convert the object to a JSON string
    const jsonValue = JSON.stringify(newValue);

    // Pass the JSON string to the onChange handler
    props.onChange?.(jsonValue);
  }

  const toggleCollapse = () => {
    setCollapsed(!collapsed);
  }

  const controls = (availableLanguages.length > 0 ? availableLanguages : fallbackLanguages).map((lang, index) => {
    // 如果是折叠状态且不是第一个语言，则不显示
    if (collapsed && index !== 0) return null;

    if (!isValidElement(props.children)) return null;

    const Comp = cloneElement((props.children as any), {
      onChange: (value: any) => {
        // 处理不同类型的输入值
        const finalValue = value?.target?.value !== undefined ? value.target.value : value;
        itemOnChange(lang, finalValue);
      },
      checked: parsedValue?.[lang],
      value: parsedValue?.[lang],
      placeholder: `${getLanguageName(lang)} - ${(props.children as any).props.placeholder || ''}`
    });

    return (
      <div key={lang} className="i18n-field-item">
        <div className="i18n-field-lang text-sm text-blue-500">{getLanguageName(lang)}</div>
        {Comp}
      </div>
    );
  });

  return (
    <div className="I18nField flex flex-col gap-4">
      {controls}
      <div
        className="text-sm text-blue-500 cursor-pointer hover:text-blue-700 flex items-center"
        onClick={toggleCollapse}
      >
        {collapsed ?
          <span>展开更多语言 ▼</span> :
          <span>收起语言 ▲</span>
        }
      </div>
    </div>
  )
}

export default I18nField
