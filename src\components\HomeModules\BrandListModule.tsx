import React, { useState, useEffect } from 'react';
import { Button, Modal, Form, Input, List, Space, Image, Card, Row, Col, message } from 'antd';
import { DeleteOutlined, RightOutlined } from '@ant-design/icons';
import { TBrandListModule } from './types';
import BaseModuleWrapper from './BaseModuleWrapper';
import { apiBrandPageList } from '@/apis/apis.api';
import { useModel } from 'foca';
import { homeSettingModel } from '@/stores/homeSettingModel';
import I18nField from '../I18nField';

interface BrandListModuleProps {
  module: TBrandListModule;
  isFirst: boolean;
  isLast: boolean;
  onUpdate: (updatedModule: TBrandListModule) => void;
  onDelete: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onInsertBefore: () => void;
  isEditing?: boolean;
}

const BrandListModuleComponent: React.FC<BrandListModuleProps> = ({
  module,
  isFirst,
  isLast,
  onUpdate,
  onDelete,
  onMoveUp,
  onMoveDown,
  onInsertBefore,
  isEditing: initialIsEditing
}) => {
  const homeSettingModalState = useModel(homeSettingModel);
  const [isEditing, setIsEditing] = useState(initialIsEditing || false);
  const [isModuleSettingVisible, setIsModuleSettingVisible] = useState(false);
  const [moduleForm] = Form.useForm();
  const [brandSelectVisible, setBrandSelectVisible] = useState(false);
  const [brandList, setBrandList] = useState<any[]>([]);
  const [brandLoading, setBrandLoading] = useState(false);
  const [selectedBrandIds, setSelectedBrandIds] = useState<string[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });


  useEffect(() => {
    homeSettingModel.loadBrandByIds(module.brands);
  }, [module.brands])

  const handleModuleSettings = () => {
    moduleForm.setFieldsValue({
      title: module.title,
      moreText: module.moreText,
      moreLink: module.moreLink
    });
    setIsModuleSettingVisible(true);
  };

  const handleModuleSettingsSubmit = () => {
    moduleForm.validateFields().then(values => {
      onUpdate({
        ...module,
        title: values.title,
        moreText: values.moreText,
        moreLink: values.moreLink
      });
      setIsModuleSettingVisible(false);
    });
  };

  const loadBrands = async (options?: {
    page?: number, pageSize?: number, keyword?: string
  }) => {
    const { page = 1, pageSize = 6, keyword } = options || {};
    setBrandLoading(true);
    try {

      const res = await apiBrandPageList({
        current: page - 1,
        size: pageSize,
        filters: keyword ? [{ field: 'name', operator: 'like', value: keyword }] : [],
        sorts: [],
      });
      setBrandList(res.records || []);
      setPagination({
        current: page,
        pageSize,
        total: res.total || 0
      });
      homeSettingModel.updateBrandMapByBrands(res.records || []);
    } catch (error) {
      console.error('加载品牌列表失败', error);
      message.error('加载品牌列表失败');
    } finally {
      setBrandLoading(false);
    }
  };

  const handleOpenBrandSelector = () => {
    setBrandSelectVisible(true);
    loadBrands();
    setSelectedBrandIds([...module.brands]);
  };

  const handleBrandSelect = (brandId: string) => {
    if (selectedBrandIds.includes(brandId)) {
      setSelectedBrandIds(selectedBrandIds.filter(id => id !== brandId));
    } else {
      setSelectedBrandIds([...selectedBrandIds, brandId]);
    }
  };

  const handleConfirmBrandSelection = () => {
    onUpdate({
      ...module,
      brands: Array.from(new Set([...selectedBrandIds]))
    });

    setBrandSelectVisible(false);
    setSelectedBrandIds([]);
    console.log("handleConfirmBrandSelection 12", isEditing);
  };

  useEffect(() => {
    console.log("isEditing", isEditing);
  }, [isEditing]);

  const handleDeleteItem = (itemId: string) => {
    const updatedItems = module.brands.filter(item => item !== itemId);
    onUpdate({
      ...module,
      brands: updatedItems
    });
  };

  const handleSearch = (key: string) => {
    loadBrands({ keyword: key });
  };

  const renderBrandListContent = () => {
    return (
      <div>
        <div className="flex justify-between mb-4">
          <div className="text-lg font-medium"  >{module.title['zh-CN'] || '品牌列表'}</div>
          {module.moreText && (
            <a href={module.moreLink} className="text-blue-500 flex items-center">
              {module.moreText['zh-CN']} <RightOutlined />
            </a>
          )}
        </div>

        {module.brands.length === 0 ? (
          <div className="h-40 flex items-center justify-center bg-gray-100">
            <p className="text-gray-500">暂无品牌</p>
          </div>
        ) : (
          <Row gutter={[16, 16]}>
            {module.brands.map(brandId => {
              const brand = homeSettingModalState.brandMap[brandId];
              return (
                brand ? <Col span={4} key={brandId}>
                  <Card
                    hoverable
                    cover={
                      <Image
                        alt={brand.name}
                        src={brand.logo}
                        className="h-40 object-cover"
                        preview={false}
                      />
                    }
                  >
                    <Card.Meta
                      title={brand.name}
                    />
                  </Card>
                </Col> : null
              );
            })}
          </Row>
        )}
      </div>
    );
  };

  const renderEditContent = () => {
    return (
      <div>
        <div className="flex justify-between mb-4">
          <div className="text-lg font-medium">品牌列表模块</div>
          <Space>
            <Button onClick={handleModuleSettings}>模块设置</Button>
            <Button type="primary" onClick={handleOpenBrandSelector}>
              从品牌库选择
            </Button>
            <Button type="primary" onClick={() => setIsEditing(false)}>
              完成
            </Button>
          </Space>
        </div>

        <List
          itemLayout="horizontal"
          dataSource={module.brands}
          renderItem={item => {
            const brand = homeSettingModel.state.brandMap[item];
            return brand ? (
              <List.Item
                actions={[
                  <Button danger icon={<DeleteOutlined />} onClick={() => handleDeleteItem(item)}>删除</Button>
                ]}
              >
                <List.Item.Meta
                  avatar={<Image src={brand.logo} width={80} height={80} />}
                  title={brand.name}
                />
              </List.Item>
            ) : null;
          }}
        />

        <Modal
          title="选择品牌"
          open={brandSelectVisible}
          onOk={handleConfirmBrandSelection}
          onCancel={() => setBrandSelectVisible(false)}
          width={800}
          okButtonProps={{ disabled: selectedBrandIds.length === 0 }}
        >
          <div className="mb-4 flex justify-between">

            <div>已选择 {selectedBrandIds.length} 个品牌</div>
            <Input.Search
              className="w-1/2"
              placeholder="搜索品牌"
              onSearch={handleSearch}
            />
          </div>
          <List
            itemLayout="horizontal"
            dataSource={brandList}
            loading={brandLoading}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              onChange: (page) => loadBrands({ page })
            }}
            renderItem={item => (
              <List.Item
                actions={[
                  <Button
                    type={selectedBrandIds.includes(item.id) ? "primary" : "default"}
                    onClick={() => handleBrandSelect(item.id)}
                  >
                    {selectedBrandIds.includes(item.id) ? "已选择" : "选择"}
                  </Button>
                ]}
              >
                <List.Item.Meta
                  avatar={<Image src={item.mainImage} width={60} height={60} />}
                  title={item.name}
                  description={`价格: ¥${item.price?.toFixed(2) || '0.00'} | 库存: ${item.stock || 0}`}
                />
              </List.Item>
            )}
          />
        </Modal>

        <Modal
          title="模块设置"
          open={isModuleSettingVisible}
          onOk={handleModuleSettingsSubmit}
          onCancel={() => setIsModuleSettingVisible(false)}
        >
          <Form form={moduleForm} layout="vertical">
            <Form.Item
              name="title"
              label="模块标题"
              rules={[{ required: true, message: '请输入模块标题' }]}
            >
              <I18nField>
                <Input placeholder="请输入模块标题" />
              </I18nField>
            </Form.Item>
            <Form.Item
              name="moreText"
              label="更多按钮文本"
            >
              <I18nField>
                <Input placeholder="请输入更多按钮文本" />
              </I18nField>
            </Form.Item>
            <Form.Item
              name="moreLink"
              label="更多按钮链接"
            >
              <Input placeholder="请输入更多按钮链接" />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    );
  };

  return (
    <BaseModuleWrapper
      isFirst={isFirst}
      isLast={isLast}
      onEditingChange={setIsEditing}
      onDelete={onDelete}
      onMoveUp={onMoveUp}
      onMoveDown={onMoveDown}
      onInsertBefore={onInsertBefore}
      isEditing={isEditing}
    >
      {isEditing ? renderEditContent() : renderBrandListContent()}
    </BaseModuleWrapper>
  );
};

export default BrandListModuleComponent;
