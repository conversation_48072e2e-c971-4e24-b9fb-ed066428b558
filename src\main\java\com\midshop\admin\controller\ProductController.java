package com.midshop.admin.controller;

import com.midshop.admin.dto.ProductBo;
import com.midshop.admin.dto.TPageListRes;
import com.midshop.admin.dto.request.ProductPageQuery;
import com.midshop.admin.service.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/products")
@Tag(name = "Product Management")
public class ProductController {

    @Autowired
    private ProductService productService;

    @GetMapping("/{id}")
    @Operation(summary = "Get product by ID")
    public ProductBo getProductById(@PathVariable Long id) {
        return productService.getProductById(id);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update product")
    public ProductBo updateProduct(@PathVariable Long id, @RequestBody ProductBo productBo) {
        return productService.updateProduct(id, productBo);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete product")
    public void deleteProduct(@PathVariable Long id) {
        productService.deleteProduct(id);
    }

    @PutMapping("/{id}/featured/{isFeatured}")
    @Operation(summary = "Set product featured status")
    public ProductBo setProductFeatured(@PathVariable Long id, @PathVariable Boolean isFeatured) {
        return productService.setProductFeatured(id, isFeatured);
    }

    @PutMapping("/{id}/active/{isActive}")
    @Operation(summary = "Set product active status")
    public ProductBo setProductActive(@PathVariable Long id, @PathVariable Boolean isActive) {
        return productService.setProductActive(id, isActive);
    }

    @PostMapping
    @Operation(summary = "Create product")
    public ProductBo createProduct(@RequestBody ProductBo productBo) {
        return productService.createProduct(productBo);
    }

    @PostMapping("/page")
    @Operation(summary = "Get paginated list of products")
    public TPageListRes<ProductBo> getProductsPage(@RequestBody ProductPageQuery query) {
        return productService.getProductsPage(query);
    }
}
