import { 
  apiCreateChannel, 
  apiListChannels, 
  apiUpdateChannel, 
  apiDeleteChannel,
  apiGetChannelById,
  apiBatchToggleChannelsEnabled,
  apiClearAllChannelCache,
  apiResetAllRequestCounts
} from '@/apis/gen.apis';
import { useFormModal } from '@/components/FormilyModal';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { App, Button, Space, Tag, Popconfirm, Dropdown } from 'antd';
import { useRef, useState } from 'react';
import { MoreOutlined } from '@ant-design/icons';

type TEditFormData = {
  id?: number;
  code: string;
  name: string;
  description?: string;
  sourceType: TChannelType;
  baseUrl: string;
  configJson?: string;
  isEnabled: TIsEnum;
  priority?: number;
  dailyRequestLimit?: number;
  currentRequestCount?: number;
  supportedProxyTypes?: string;
  extra?: string;
};

function ChannelsList() {
  const tableRef = useRef<ActionType>();
  const { message } = App.useApp();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const { formModalRef: editFormModalRef, formModalHolder: editFormModalHolder } =
    useFormModal<TEditFormData>();

  const showEditModal = useMemoizedFn(async (record?: TChannelDTO) => {
    let initialValues = {};
    
    if (record?.id) {
      // 获取详细信息
      const detail = await apiGetChannelById(record.id.toString());
      initialValues = { ...detail, enabled: detail.enabled ? 'true' : 'false' };
    } else {
      initialValues = {
        sourceType: 'SELF',
        enabled: 'true',
        priority: 1,
        dailyRequestLimit: 1000,
        currentRequestCount: 0,
      };
    }

    editFormModalRef.current?.show({
      modalTitle: record ? '修改渠道' : '新增渠道',
      modalWidth: 1000,
      onAutoSubmit: async (values: any) => {
        const data = {
          ...values,
          enabled: values.enabled === 'true' ? 1 : 0,
        };
        if (record?.id) {
          await apiUpdateChannel(record.id.toString(), data);
        } else {
          await apiCreateChannel(data);
        }

        message.success('提交成功');
        tableRef.current?.reload();
      },
      initialValues,
      schema: {
        type: 'object',
        properties: {
          code: {
            title: '渠道编码',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          name: {
            title: '渠道名称',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          description: {
            title: '描述',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
          },
          sourceType: {
            title: '渠道类型',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            enum: [
              { label: '自营', value: 'SELF' },
              { label: 'IPNUX', value: 'IPNUX' },
            ],
          },
          baseUrl: {
            title: '基础URL',
            type: 'string',
            // required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          configJson: {
            title: '配置JSON',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4,
            },
          },
          enabled: {
            title: '启用状态',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            enum: [
              { label: '启用', value: 'true' },
              { label: '禁用', value: 'false' },
            ],
          },
         
        },
      },
    });
  });

  const handleDelete = useMemoizedFn(async (id: string) => {
    await apiDeleteChannel(id);
    message.success('删除成功');
    tableRef.current?.reload();
  });

 

  const handleClearCache = useMemoizedFn(async () => {
    await apiClearAllChannelCache();
    message.success('清除缓存成功');
  });

  const handleResetRequestCounts = useMemoizedFn(async () => {
    await apiResetAllRequestCounts();
    message.success('重置请求计数成功');
    tableRef.current?.reload();
  });

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  const moreMenuItems = [
    {
      key: 'clearCache',
      label: '清除所有缓存',
      onClick: handleClearCache,
    },
    {
      key: 'resetCounts',
      label: '重置请求计数',
      onClick: handleResetRequestCounts,
    },
  ];

  return (
    <div>
      <ProTable<TChannelDTO>
        actionRef={tableRef}
        headerTitle='渠道管理'
        rowKey='id'
        rowSelection={rowSelection}
        toolBarRender={() => [
          <Button key='add' type='primary' onClick={() => showEditModal()}>
            新增
          </Button>,
          // <Button 
          //   key='batchEnable' 
          //   onClick={() => handleBatchToggleEnabled(true)}
          //   disabled={selectedRowKeys.length === 0}
          // >
          //   批量启用
          // </Button>,
          // <Button 
          //   key='batchDisable' 
          //   onClick={() => handleBatchToggleEnabled(false)}
          //   disabled={selectedRowKeys.length === 0}
          // >
          //   批量禁用
          // </Button>,
          // <Dropdown key='more' menu={{ items: moreMenuItems }}>
          //   <Button>
          //     更多操作 <MoreOutlined />
          //   </Button>
          // </Dropdown>,
        ]}
        search={{
          defaultCollapsed: false,
        }}
        columns={[
          { title: 'ID', dataIndex: 'id', width: 80 },
          { title: '编码', dataIndex: 'code' },
          { title: '名称', dataIndex: 'name' },
          { title: '描述', dataIndex: 'description', ellipsis: true, search: false },
          { 
            title: '类型', 
            dataIndex: 'sourceType',
            width: 100,
            valueType: 'select',
            valueEnum: {
              0: '自营',
              1: 'IPNUX',
            },
          },
          { title: '基础URL', dataIndex: 'baseUrl' },
          { 
            title: '启用状态', 
            dataIndex: 'enabled',
            width: 100,
            valueType: 'select',
            valueEnum: {
              true: '启用',
              false: '禁用',
            },
            render: (_text, record) => (
              <Tag color={record.enabled ? 'green' : 'red'}>
                {record.enabled ? '启用' : '禁用'}
              </Tag>
            ),
          },
          // { title: '优先级', dataIndex: 'priority', width: 80 },
          // { title: '每日限制', dataIndex: 'dailyRequestLimit', width: 100 },
          // { title: '当前计数', dataIndex: 'currentRequestCount', width: 100 },
          { 
            title: '创建时间', 
            dataIndex: 'createdAt',
            width: 180,
            valueType: 'dateTime',
          },
          {
            title: '操作',
            valueType: 'option',
            key: 'option',
            render: (_text, record) => {
              return (
                <Space>
                  <a onClick={() => showEditModal(record)}>编辑</a>
                  <Popconfirm
                    title="确定要删除这个渠道吗？"
                    onConfirm={() => handleDelete(record.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <a style={{ color: 'red' }}>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
        request={async (params) => {
          const filters: any[] = [];

          if (params.id) {
            filters.push({ field: 'id', operator: 'eq', value: params.id });
          }
          if (params.code) {
            filters.push({ field: 'code', operator: 'like', value: params.code });
          }
          if (params.name) {
            filters.push({ field: 'name', operator: 'like', value: params.name });
          }
         
          if (params.sourceType) {
            filters.push({ field: 'sourceType', operator: 'eq', value: params.sourceType });
          }
          if (params.enabled !== undefined) {
            filters.push({ field: 'enabled', operator: 'eq', value: params.enabled === 'true' ? 1 : 0 });
          }

          const res = await apiListChannels({
            current: (params.current || 1) - 1,
            size: params.pageSize || 20,
            filters,
            sorts: [],
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}
      />
      {editFormModalHolder}
    </div>
  );
}

export default ChannelsList;

ChannelsList.auth = [
  'apiCreateChannel', 
  'apiListChannels', 
  'apiUpdateChannel', 
  'apiDeleteChannel',
  'apiGetChannelById',
  'apiBatchToggleChannelsEnabled',
  'apiClearAllChannelCache',
  'apiResetAllRequestCounts'
];
