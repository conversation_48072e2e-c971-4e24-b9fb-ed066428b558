import { defineModel } from 'foca';

export const layoutModel = defineModel('layout', {
  initialState: {
    headerHeight: 40,
    sidebarWidth: 200,
    navbarHeight: 40,
    windowWidth: window.innerWidth,
    windowHeight: window.innerHeight,
  },

  reducers: {
    onWindowResize(state) {
      state.windowHeight = window.innerHeight;
      state.windowWidth = window.innerWidth;
    },
  },
  methods: {

  },
  events: {
    onInit() {
      window.addEventListener('resize', () => {
        this.onWindowResize();
      });
    },
  },
});
