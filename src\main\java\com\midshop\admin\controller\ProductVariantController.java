package com.midshop.admin.controller;

import com.midshop.admin.dto.ProductVariantBo;
import com.midshop.admin.service.ProductVariantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/variants")
@Tag(name = "Product Variant Management")
public class ProductVariantController {

    @Autowired
    private ProductVariantService productVariantService;

    @GetMapping("/{id}")
    @Operation(summary = "Get product variant by ID")
    public ProductVariantBo getVariantById(@PathVariable Long id) {
        return productVariantService.getVariantById(id);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update product variant")
    public ProductVariantBo updateVariant(@PathVariable Long id, @RequestBody ProductVariantBo variantBo) {
        return productVariantService.updateVariant(id, variantBo);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete product variant")
    public void deleteVariant(@PathVariable Long id) {
        productVariantService.deleteVariant(id);
    }

    @PutMapping("/{id}/in-stock/{isInStock}")
    @Operation(summary = "Set product variant stock status")
    public ProductVariantBo setVariantInStock(@PathVariable Long id, @PathVariable Boolean isInStock) {
        return productVariantService.setVariantInStock(id, isInStock);
    }

    @PutMapping("/{id}/active/{isActive}")
    @Operation(summary = "Set product variant active status")
    public ProductVariantBo setVariantActive(@PathVariable Long id, @PathVariable Boolean isActive) {
        return productVariantService.setVariantActive(id, isActive);
    }

    @PostMapping("/{variantId}/attributes/{attributeId}")
    @Operation(summary = "Add attribute to product variant")
    public ProductVariantBo addAttributeToVariant(
            @PathVariable Long variantId,
            @PathVariable Long attributeId,
            @RequestParam String value) {
        return productVariantService.addAttributeToVariant(variantId, attributeId, value);
    }

    @PutMapping("/{variantId}/attributes/{attributeId}")
    @Operation(summary = "Update product variant attribute")
    public ProductVariantBo updateVariantAttribute(
            @PathVariable Long variantId,
            @PathVariable Long attributeId,
            @RequestParam String value) {
        return productVariantService.updateVariantAttribute(variantId, attributeId, value);
    }

    @DeleteMapping("/{variantId}/attributes/{attributeId}")
    @Operation(summary = "Remove attribute from product variant")
    public ProductVariantBo removeAttributeFromVariant(
            @PathVariable Long variantId,
            @PathVariable Long attributeId) {
        return productVariantService.removeAttributeFromVariant(variantId, attributeId);
    }
}
