import { apiAdminUserPageList, apiAdminRolePageList, apiUpdateUser, apiCreateUser, apiAssignRolesToUser } from '@/apis/apis.api';
import { useFormModal } from '@/components/FormilyModal';
import { ToolsUtil } from '@/utils/tools';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useCreation, useMemoizedFn, useRequest } from 'ahooks';
import { App, Button, Space } from 'antd';
import { useRef } from 'react';

type TEditFormData = {
  nickname: string;
  username: string;
  password: string;
  rPassword: string;
};

type TRolesEditFormData = {
  roles: string[];
}


function AdminUserList() {
  const tableRef = useRef<ActionType>();
  const { message } = App.useApp();

  const { formModalRef: editFormModalRef, formModalHolder: editFormModalHolder } =
    useFormModal<TEditFormData>();
  const { formModalRef: editRolesFormModalRef, formModalHolder: editRolesFormModalHolder } =
    useFormModal<TRolesEditFormData>();

  const { data: rolesOptions } = useRequest(async () => {
    const res = await apiAdminRolePageList({
      current: 0,
      size: 10000,
      filters: [],
      sorts: [],
    });
    return res.records
      .filter((item) => {
        return item.code !== 'admin';
      })
      .map((item) => ({
        label: item.name,
        value: item.code,
      }));
  });

  const roleMap = useCreation(() => {
    return rolesOptions?.reduce(
      (prev, item) => {
        prev[item.value] = item.label;
        return prev;
      },
      {} as Record<string, string>,
    );
  }, [rolesOptions]);

  const showEditRolesModal = useMemoizedFn((record: TAdminUser) => {
    editRolesFormModalRef.current?.show({
      modalTitle: '修改',
      modalWidth: 1000,
      onAutoSubmit: async (values) => {
        await apiAssignRolesToUser(record!.id, values.roles);
        message.success('提交成功');
        tableRef.current?.reload();
      },
      initialValues: {
        roles: record.roles,
      },
      schema: {
        type: 'object',
        properties: {
          roles: {
            title: '角色',
            type: 'array',
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              placeholder: '请选择角色',
              mode: 'multiple',
            },
            enum: rolesOptions || [],
          },
        },
      },
    });
  });


  const showEditModal = useMemoizedFn((record?: TAdminUser) => {
    editFormModalRef.current?.show({
      modalTitle: record ? '修改' : '新增',
      modalWidth: 1000,
      onAutoSubmit: async (values) => {
        const data = {
          ...values,
        };
        if (!data.password) {
          delete data.password;
          delete data.rPassword;
        }
        if (data.password && data.rPassword && data.password !== data.rPassword) {
          message.error('两次密码不一致');
          return;
        }
       
        if(record?.id) {
          await apiUpdateUser(record!.id, data);
        } else {
          await apiCreateUser(data);
        }

        message.success('提交成功');
        tableRef.current?.reload();
      },
      initialValues: record,
      schema: {
        type: 'object',
        properties: {
          username: {
            required: true,
            title: '用户名',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入用户名',
            },
          },
          nickname: {
            required: true,
            title: '昵称',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入昵称',
            },
          },
         
          password: {
            required: !record?.id,
            title: '密码',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Password',
            'x-component-props': {
              placeholder: '请输入密码',
            },
          },
          rPassword: {
            required: !record?.id,
            title: '重复密码',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Password',
            'x-component-props': {
              placeholder: '请输入密码',
            },
          },
          // roles: {
          //   title: '角色',
          //   type: 'array',
          //   'x-decorator': 'FormItem',
          //   'x-component': 'Select',
          //   'x-component-props': {
          //     placeholder: '请选择角色',
          //     mode: 'multiple',
          //   },
          //   enum: rolesOptions || [],
          // },
        },
      },
    });
  });

  return (
    <div>
      <div>
        <ProTable<TAdminUser>
          actionRef={tableRef}
          headerTitle='管理员列表'
          rowKey='_id'
          toolBarRender={() => [
            <Button key='add' type='primary' onClick={() => showEditModal()}>
              新增
            </Button>,
          ]}
          search={{
            defaultCollapsed: false,
          }}
          columns={[
            { title: '用户名', dataIndex: 'username' },
            { title: '昵称', dataIndex: 'nickname' },
         
            {
              title: '角色',
              dataIndex: 'roles',
              // TODO 完善角色搜索，角色显示
              // // valueEnum: lodash.omit(AgreeDicts.Merchant_role.valueLabelMap, ['admin']),
              valueType: 'select',
              render: (_text, record) =>
                (record.roles || []).map((role) => roleMap[role]).join(','),
            },
            // {
            //   title: '登录时间',
            //   dataIndex: 'lastLoginAt',
            //   render: (_text, record) => ToolsUtil.timeFormat(record.lastLoginAt),
            //   valueType: 'dateRange',
            // },
            // { title: '登录IP', dataIndex: 'lastLoginIp' },

            {
              title: '创建时间',
              dataIndex: 'createdAt',
              render: (_text, record) => ToolsUtil.timeFormat(record.createdAt),
              valueType: 'dateRange',
            },

            {
              title: '操作',
              valueType: 'option',
              key: 'option',
              render: (_text, record) => {
                return (
                  <Space>
                    <a onClick={() => showEditModal(record)}>修改</a>
                    <a onClick={() => showEditRolesModal(record)}>角色</a>
                  </Space>
                );
              },
            },
          ]}
          request={async (params, sorter) => {
            const filters: TQueryFilter[] = [];
            if (params.nickname) {
              filters.push({
                field: 'nickname',
                operator: 'eq',
                value: params.nickname,
              });
            }
            if (params.mobile) {
              filters.push({
                field: 'mobile',
                operator: 'eq',
                value: params.mobile,
              });
            }
            if (params.realname) {
              filters.push({
                field: 'realname',
                operator: 'eq',
                value: params.realname,
              });
            }

            if (params.roles) {
              filters.push({
                field: 'roles',
                operator: 'in',
                value: [params.roles],
              });
            }

            if (params.lastLoginIp) {
              filters.push({
                field: 'lastLoginIp',
                operator: 'eq',
                value: params.lastLoginIp,
              });
            }


            const res = await apiAdminUserPageList({
              current: params.current - 1,
              size: params.pageSize,
              filters,
              sorts: [],
            });

            return {
              total: res.total,
              data: res.records,
              success: true,
            };
          }}
        />
        {editRolesFormModalHolder}
        {editFormModalHolder}
      </div>
    </div>
  );
}

export default AdminUserList;

AdminUserList.auth = ['apiAdminUserPageList', 'apiAdminRolePageList', 'apiUpdateUser', 'apiCreateUser', 'apiAssignRolesToUser'];
