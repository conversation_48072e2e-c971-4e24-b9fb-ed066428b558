interface ProductBo {
  id: string;
  name: string;
  shortDescription: string;
  description: string;
  code: string;
  // 支持多种属性名称
  brandId: string;
  brandid: string;
  categoryId: string;
  categoryid: string;
  price: number;
  specialPrice: number;
  specialPriceFrom: string;
  specialPriceTo: string;
  cost: number;
  weight: number;
  length: number;
  width: number;
  height: number;
  hasMultipleVariants: boolean;
  isActive: boolean;
  isFeatured: boolean;
  urlKey: string;
  metaTitle: string;
  metaKeywords: string;
  metaDescription: string;
  mainImage: string;
  main_image: string;
  additionalImages: string[];
  images: string[] | ProductImageBo[];
  brand: ProductBrandBo;
  category: ProductCategoryBo;
  variants: ProductVariantBo[];
  attributes: ProductAttributeBo[];
  attributeValues: Record<string, string>;
  createdAt: string;
  updatedAt: string;
}

// 品牌业务对象
interface ProductBrandBo {
  id: string;
  code: string;
  name: string;
  firstLetter: string;
  description: string;
  logo: string;
  website: string;
  country: string;
  foundedYear: number;
  sortOrder: number;
  featured: boolean;
  isActive: boolean;
  categories: ProductCategoryBo[];
  categoryIds: string[];
  createdAt: string;
  updatedAt: string;
}

// 分类业务对象
interface ProductCategoryBo {
  id: string;
  code: string;
  name: string;
  description: string;
  parentId: string;
  level: number;
  path: string;
  pathNames: string[];
  sortOrder: number;
  isActive: boolean;
  isFeatured: boolean;
  imageUrl: string;
  children: ProductCategoryBo[];
  parent: ProductCategoryBo;
  createdAt: string;
  updatedAt: string;
}

// 商品变体业务对象
interface ProductVariantBo {
  id: string;
  sku: string;
  name: string;
  price: number;
  specialPrice: number;
  specialPriceFrom: string;
  specialPriceTo: string;
  cost: number;
  weight: number;
  length: number;
  width: number;
  height: number;
  inStock: boolean;
  stockQuantity: number;
  trackInventory: boolean;
  isActive: boolean;
  active: boolean;
  mainImage: string;
  additionalImages: string[];
  images: ProductImageBo[] | string[];
  productId: string;
  attributes: ProductVariantAttributeBo[] | Record<string, string>;
  createdAt: string;
  updatedAt: string;
}

// 商品变体属性业务对象
interface ProductVariantAttributeBo {
  attributeId: string;
  attributeCode: string;
  attributeName: string;
  value: string;
  variantAttribute: boolean;
}

// 商品图片业务对象
interface ProductImageBo {
  id: string;
  url: string;
  title: string;
  position: number;
  isMain: boolean;
  isActive: boolean;
  productId: string;
  variantId: string;
  createdAt: string;
  updatedAt: string;
}

// 商品属性业务对象
interface ProductAttributeBo {
  id: string;
  code: string;
  name: string;
  description: string;
  type: number; // text, number, boolean, select
  filterable: boolean;
  searchable: boolean;
  comparable: boolean;
  visibleOnFront: boolean;
  required: boolean;
  sortOrder: number;
  active: boolean;
  isActive: boolean;
  options: string;
  attributeOptions: AttributeOption[];
  isVariantAttribute: boolean;
  categories: ProductCategoryBo[];
  categoryIds: string[];
  createdAt: string;
  updatedAt: string;
}

// 属性选项
interface AttributeOption {
  value: string;
  label: string;
}

// 商品属性值业务对象
interface ProductAttributeValueBo {
  id: string;
  productId: string;
  variantId: string;
  attributeId: string;
  attributeid: string;
  attributeCode: string;
  value: string;
  attribute: ProductAttributeBo;
  createdAt: string;
  updatedAt: string;
}

// 商品分页查询参数
interface ProductPageQuery extends TPageListReq {
  keyword: string;
  categoryId: string;
  brandId: string;
  isActive: boolean;
  isFeatured: boolean;
  minPrice: number;
  maxPrice: number;
}

// 商品分页结果
interface PageResultProductBo {
  records: ProductBo[];
  total: number;
  current: number;
  size: number;
  pages: number;
}

// 品牌分页结果
interface PageResultProductBrandBo {
  records: ProductBrandBo[];
  total: number;
  current: number;
  size: number;
  pages: number;
}

// 分类分页结果
interface PageResultProductCategoryBo {
  records: ProductCategoryBo[];
  total: number;
  current: number;
  size: number;
  pages: number;
}

// 商品变体分页查询参数
interface ProductVariantPageQuery extends TPageListReq {
  productId: number;
  isActive: boolean;
  isInStock: boolean;
  minPrice: number;
  maxPrice: number;
}

// 商品属性分页查询参数
interface ProductAttributePageQuery extends TPageListReq {
  keyword: string;
  isActive: boolean;
  isVariantAttribute: boolean;
  type: string;
}