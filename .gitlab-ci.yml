stages:
  - package
  - image
package:
  image: node:20
  stage: package
  tags:
    - k3s
  only:
    refs:
      - develop
      - main
      - tags
  script:
    - yarn --cache-folder .yarn-cache
    - yarn build
  cache:
    key: npm-ci-cache
    paths:
      - .yarn-cache
  artifacts:
    paths:
      - dist
    expire_in: 1h

branches:
  stage: image
  tags:
    - k3s
  only:
    refs:
      - main
      - develop
  image:
    name: moby/buildkit:rootless
    entrypoint: [ "sh", "-c" ]
  variables:
    BUILDKITD_FLAGS: --oci-worker-no-process-sandbox
  before_script:
    - |
      mkdir ~/.docker
      echo "{\"auths\":{\"***************:20080\":{\"auth\":\"************************\"}}}" > ~/.docker/config.json
  script:
    - |
      buildctl-daemonless.sh \
          build --frontend=dockerfile.v0 \
          --export-cache type=inline \
          --local context=. \
          --local dockerfile=. \
          --output type=image,name=***************:20080/wejoy/mid-shop-admin:$CI_COMMIT_REF_NAME,push=true,registry.insecure=true

tags:
  stage: image
  tags:
    - k3s
  only:
    refs:
      - tags
  image:
    name: moby/buildkit:rootless
    entrypoint: [ "sh", "-c" ]
  variables:
    BUILDKITD_FLAGS: --oci-worker-no-process-sandbox
  before_script:
    - |
      mkdir ~/.docker
      echo "{\"auths\":{\"***************:20080\":{\"auth\":\"************************\"}}}" > ~/.docker/config.json
  script:
    - |
      buildctl-daemonless.sh \
          build --frontend=dockerfile.v0 \
          --export-cache type=inline \
          --local context=. \
          --local dockerfile=. \
          --output type=image,name=***************:20080/wejoy/mid-shop-admin:$CI_COMMIT_TAG,push=true,registry.insecure=true
