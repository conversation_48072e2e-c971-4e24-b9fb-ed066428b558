import { apiCreateClientUser, apiGetClientUsers, apiUpdateClientUser, apiDeleteUser } from '@/apis/gen.apis';
import { useFormModal } from '@/components/FormilyModal';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { App, Button, Space, Popconfirm, Tag } from 'antd';
import { useRef } from 'react';
import { useNavigate } from 'react-router-dom';

type TEditFormData = {
  username: string;
  password: string;
  email: string;
  region: string;
  language: string;
  currency: string;
  phone: string;
  nickname: string;
  avatar: string;
  remarks: string;
};

function ClientUserManagement() {
  const tableRef = useRef<ActionType>();
  const { message } = App.useApp();
  const navigate = useNavigate();

  const { formModalRef: editFormModalRef, formModalHolder: editFormModalHolder } =
    useFormModal<TEditFormData>();

  const showEditModal = useMemoizedFn((record?: TClientUserBO) => {
    editFormModalRef.current?.show({
      modalTitle: record ? '修改客户用户' : '新增客户用户',
      modalWidth: 600,
      onAutoSubmit: async (values: any) => {
        const data = {
          ...values,
          enabled: values.enabled === 'true' ? 1 : 0,
        }
        if (record?.id) {
          // 编辑时不传密码字段
          if(!values.password){
            delete data.password;
          }

          await apiUpdateClientUser(record.id, data);
        } else {
          await apiCreateClientUser(data);
        }

        message.success('提交成功');
        tableRef.current?.reload();
      },
      initialValues: record
        ? {
          ...record,
          password: '', // 编辑时密码字段为空
        }
        : {},
      schema: {
        type: 'object',
        properties: {
          username: {
            title: '用户名',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-disabled': !!record, // 编辑时禁用用户名
          },
          password: {
            title: '密码',
            type: 'string',
            required: !record, // 新增时必填，编辑时非必填
            'x-decorator': 'FormItem',
            'x-component': 'Input.Password',
            'x-component-props': {
              placeholder: record ? '留空则不修改密码' : '请输入密码',
            },
          },
          email: {
            title: '邮箱',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-validator': 'email',
          },
          nickname: {
            title: '昵称',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          phone: {
            title: '手机号',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          remarks: {
            title: '备注',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
          },
        },
      },
    });
  });

  const handleDelete = useMemoizedFn(async (id: string) => {
    await apiDeleteUser(id);
    message.success('删除成功');
    tableRef.current?.reload();
  });

  const handleViewDetail = useMemoizedFn((id: string) => {
    navigate(`/client/client-user-detail/${id}`);
  });

  return (
    <div>
      <ProTable<TClientUserBO>
        actionRef={tableRef}
        headerTitle='客户用户管理'
        rowKey='id'
        toolBarRender={() => [
          <Button key='add' type='primary' onClick={() => showEditModal()}>
            新增
          </Button>,
        ]}
        search={{
          defaultCollapsed: false,
        }}
        columns={[
          { title: 'ID', dataIndex: 'id', },
          { title: '用户名', dataIndex: 'username', },
          { title: '邮箱', dataIndex: 'email', },
          { title: '昵称', dataIndex: 'nickname', },
          { title: '手机号', dataIndex: 'phone', },
        
          {
            title: '状态',
            dataIndex: 'enabled',
            valueType: 'select',
            valueEnum: {
              true: { text: '启用', status: 'Success' },
              false: { text: '禁用', status: 'Error' },
            },
            render: (enabled: boolean) => (
              <Tag color={enabled ? 'green' : 'red'}>
                {enabled ? '启用' : '禁用'}
              </Tag>
            ),
          },
          {
            title: '最后登录时间',
            dataIndex: 'lastLoginAt',
            valueType: 'dateTime',
            search: false,
          },
          { title: '最后登录IP', dataIndex: 'lastLoginIp', search: false },
          {
            title: '创建时间',
            dataIndex: 'createdAt',
            valueType: 'dateTime',
            search: false,

          },
          {
            title: '操作',
            valueType: 'option',
            key: 'option',

            render: (_text, record) => {
              return (
                <Space>
                  <a onClick={() => handleViewDetail(record.id)}>详情</a>
                  <a onClick={() => showEditModal(record)}>修改</a>
                  <Popconfirm
                    title="确定要删除这个用户吗？"
                    onConfirm={() => handleDelete(record.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <a style={{ color: 'red' }}>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
        request={async (params, sorter) => {
          const filters: any[] = [];

          if(params.id){
            filters.push({ field: 'id', operator: 'eq', value: params.id });
          }

          if (params.username) {
            filters.push({ field: 'username', operator: 'like', value: params.username });
          }
          if (params.email) {
            filters.push({ field: 'email', operator: 'eq', value: params.email });
          }
          if (params.phone) {
            filters.push({ field: 'phone', operator: 'eq', value: params.phone });
          }
          if (params.enabled !== undefined) {
            filters.push({ field: 'enabled', operator: 'eq', value: params.enabled === 'true' ? 1 : 0 });
          }

          const res = await apiGetClientUsers({
            current: params.current - 1,
            size: params.pageSize,
            filters,
            sorts: [],
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}
      />
      {editFormModalHolder}
    </div>
  );
}

export default ClientUserManagement;

ClientUserManagement.auth = ['apiCreateClientUser', 'apiGetClientUsers', 'apiUpdateClientUser', 'apiDeleteUser'];
