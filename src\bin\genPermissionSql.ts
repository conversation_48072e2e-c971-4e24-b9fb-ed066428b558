// INSERT INTO public.admin_permission (id, code, is_active, name)
// SELECT 1345407045100245000, 'apiGetUsers', 1, '分页获取管理员用户'
// WHERE NOT EXISTS (
//     SELECT 1 FROM public.admin_permission WHERE code = 'apiGetUsers'
// );

import { Snowflake } from "@skorotkiewicz/snowflake-id";

const machineId = 1; // machine ID (0-1023)
const snowflake = new Snowflake(machineId);

import fs from 'fs';

/**
 * 从 Postman Collection JSON 提取所有接口的父级 name、name、url、method
 * @returns Array<{ parentName: string, name: string, url: string, method: string, orignalUrl: string }>
 */
function extractDocApiInfo(): Array<{ parentName: string, name: string, url: string, method: string, orignalUrl: string }> {
  const content = fs.readFileSync('./mid-shop.postman.json', 'utf-8');
  const json = JSON.parse(content);
  const result: Array<{ parentName: string, name: string, url: string, method: string, orignalUrl: string }> = [];

  function traverse(items: any[], parentName: string) {
    if (!Array.isArray(items)) return;
    for (const item of items) {
      if (item.item) {
        // 递归子分组
        traverse(item.item, item.name);
      } else if (item.request) {
        let url = item.request.url?.raw || '';
        if (!url.startsWith('{{baseUrl}}/admin/')) continue;
        const orignalUrl = url.replace('{{baseUrl}}', '');

        url = url.replace('{{baseUrl}}/admin', '');
        // 将url中的/:paramName 替换为/_p_
        url = url.replace(/\/:([^/]+)/g, '/_p_');
        const method = item.request.method || '';
        result.push({
          parentName,
          name: item.name,
          orignalUrl,
          url,
          method,
        });
      }
    }
  }

  if (Array.isArray(json.item)) {
    traverse(json.item, '');
  }
  return result;
}

const extractProjectApis = (docApis: Array<{ parentName: string, name: string, url: string, method: string, orignalUrl: string }>) => {
  const content = fs.readFileSync('./src/apis/apis.api.ts', 'utf-8');

  // 匹配所有 export function xxx(...) { ... return http.method<...>(url, ...)
  // const funcRegex = /export function (\w+)\s*\([^)]*\)\s*{[^}]*?return http\.(get|post|put|delete|patch)\s*<[^>]*>?\(([`'"])([^`'"]+)(?:\3|,)/g;
  const funcRegex = /export function (\w+)\s*\([^)]*\)\s*{[^}]*?return http\.(get|post|put|delete|patch)\s*<.*?>?\(([`'"])([^`'"]+)(?:\3|,)/g;
  // 兼容无泛型的 http 调用
  const funcRegex2 = /export function (\w+)\s*\([^)]*\)\s*{[^}]*?return http\.(get|post|put|delete|patch)\s*\(([`'"])([^`'"]+)(?:\3|,)/g;

  let match;
  const apis: any[] = [];
  let used = new Set();
  // 先用带泛型的匹配
  while ((match = funcRegex.exec(content))) {
    const [_, name, method, _quote, rawUrl] = match;
    if (used.has(name)) continue;
    used.add(name);
    apis.push({ name, method: method.toUpperCase(), rawUrl });
  }
  // 再用无泛型的匹配
  while ((match = funcRegex2.exec(content))) {
    const [_, name, method, _quote, rawUrl] = match;
    if (used.has(name)) continue;
    used.add(name);
    apis.push({ name, method: method.toUpperCase(), rawUrl });
  }



  // 处理 url，去掉问号及其后的部分，${xxx} 替换为 _p_
  const normalizeUrl = (url: string) => {
    let u = url.split('?')[0];
    u = u.replace(/\$\{[^}]+\}/g, '_p_');
    return u;
  };
  // 建立 docApis 的查找表
  const docMap = new Map();
  for (const doc of docApis) {
    docMap.set(doc.method + ':' + doc.url, doc);
  }

  // 生成结果
  const result = apis.map(api => {
    const url = normalizeUrl(api.rawUrl);
    
    // docApis 里也做过参数归一化，这里也要保证和那边一样，比如 /:xxx 变成 _p_，但项目代码里是 _p_ 了
    // 所以只要 method+url 能对上即可
    const doc = docMap.get(api.method + ':' + url);
    return {
      name: api.name,
      method: api.method,
      url,
      docUrl: doc?.orignalUrl || '',
      apiUrl: api.rawUrl,
      comment: doc ? doc.parentName + '/' + doc.name : '',
    };
  });
  const res = result.sort((a) => a.docUrl.length === 0 ? -1 : 1);
  fs.writeFileSync('./projectApis.json', JSON.stringify(res, null, 2));

  return res;
}

/**
 * 批量重写 src/apis/apis.api.ts 的注释
 * @param projectApis [{name, comment}]
 */
function updateApiComments(projectApis: Array<{ name: string; comment: string }>) {
  const filePath = './src/apis/apis.api.ts';
  let content = fs.readFileSync(filePath, 'utf-8');

  // 1. 删除所有 API 函数体上方的注释（/** ... */ 或 // ...）
  // 只删除 export function 前的注释
  content = content.replace(/(\/\*\*([\s\S]*?)\*\/\s*|\/\/.*\s*)?(export function \w+\s*\()/g, '$3');

  // 2. 构建 name->comment 映射
  const commentMap = new Map();
  for (const api of projectApis) {
    if (api.comment && api.comment.trim()) {
      commentMap.set(api.name, api.comment.trim());
    }
  }

  // 3. 为每个 export function xxx 添加注释
  // 匹配所有 export function xxx
  content = content.replace(/(export function (\w+)\s*\()/g, (match, p1, p2) => {
    const comment = commentMap.get(p2);
    if (comment) {
      return `/** ${comment} */\n${p1}`;
    }
    return match;
  });

  fs.writeFileSync(filePath, content, 'utf-8');
}

async function genPermissionSql(projectApis: Array<{ name: string, comment: string }>) {
  const sqls: string[] = [];
  for (const api of projectApis) {
    // 生成唯一ID
    const id = await snowflake.generate();
    // code
    const code = api.name;
    // name
    const name = api.comment.replace(/'/g, "''"); // 防止单引号冲突
    sqls.push(`INSERT INTO public.admin_permission (id, code, is_active, name)\nSELECT ${id}, '${code}', 1, '${name}'\nWHERE NOT EXISTS (\n    SELECT 1 FROM public.admin_permission WHERE code = '${code}'\n);`);
  }
  const data = sqls.join('\n\n');
  fs.writeFileSync('./permission.sql', data);
}

async function main() {
  const docApis = extractDocApiInfo();

  const projectApis = extractProjectApis(docApis)
  genPermissionSql(projectApis);
  updateApiComments(projectApis);
}

main()
