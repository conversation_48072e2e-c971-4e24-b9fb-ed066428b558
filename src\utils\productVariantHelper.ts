/**
 * 商品变体和属性处理工具函数
 * 
 * 这个文件提供了处理商品变体和属性的工具函数，包括：
 * 1. 从API返回的商品数据中提取变体属性
 * 2. 从API返回的商品数据中提取变体
 * 3. 根据属性生成所有可能的变体组合
 */

/**
 * 从产品数据中提取变体属性
 * @param product 产品数据
 * @returns 提取的属性数组
 */
export const extractProductAttributes = (product: ProductBo) => {
  const variantAttributes = new Map();
  
  // 遍历所有变体，提取变体属性
  if (product.variants && product.variants.length > 0) {
    product.variants.forEach(variant => {
      if (variant.attributes && variant.attributes.length > 0) {
        variant.attributes.forEach(attr => {
          if (attr.variantAttribute) {
            // 如果是变体属性，添加到Map中
            if (!variantAttributes.has(attr.attributeCode)) {
              variantAttributes.set(attr.attributeCode, {
                id: attr.attributeId,
                code: attr.attributeCode,
                name: attr.attributeName,
                options: [attr.value],
                isVariantAttribute: true
              });
            } else {
              // 如果属性已存在，添加新的属性值（如果不存在）
              const existingAttr = variantAttributes.get(attr.attributeCode);
              if (!existingAttr.options.includes(attr.value)) {
                existingAttr.options.push(attr.value);
              }
            }
          }
        });
      }
    });
  }
  
  // 将Map转换为数组
  return Array.from(variantAttributes.values());
};

/**
 * 从产品数据中提取变体
 * @param product 产品数据
 * @returns 提取的变体数组
 */
export const extractProductVariants = (product: ProductBo) => {
  if (!product.variants || product.variants.length === 0) {
    return [];
  }
  
  return product.variants.map(variant => {
    // 从变体属性构建属性对象
    const attributesObj = {};
    if (variant.attributes && variant.attributes.length > 0) {
      variant.attributes.forEach(attr => {
        if (attr.variantAttribute) {
          attributesObj[attr.attributeCode] = attr.value;
        }
      });
    }
    
    return {
      id: variant.id,
      sku: variant.sku,
      name: variant.name,
      price: variant.price,
      specialPrice: variant.specialPrice,
      cost: variant.cost,
      isActive: variant.active,
      attributes: attributesObj
    };
  });
};

/**
 * 生成所有可能的变体组合
 * @param attributes 属性列表
 * @returns 所有可能的变体组合
 */
export const generateVariantCombinations = (attributes: Record<string, any>[]) => {
  // 如果没有属性，返回空数组
  if (!attributes || attributes.length === 0) {
    return [];
  }
  
  // 将属性列表转换为对象，键为属性代码，值为属性值数组
  const attributeMap: Record<string, any[]> = {};
  attributes.forEach(attr => {
    if (attr.isVariantAttribute && attr.options && attr.options.length > 0) {
      attributeMap[attr.code] = attr.options;
    }
  });
  
  // 如果没有变体属性，返回空数组
  const keys = Object.keys(attributeMap);
  if (keys.length === 0) {
    return [];
  }
  
  // 生成所有可能的组合
  const generateCombinations = (
    attributes: Record<string, any[]>,
    current: Record<string, any> = {},
    keys: string[] = Object.keys(attributes),
    index: number = 0
  ): Record<string, any>[] => {
    if (index === keys.length) {
      return [current];
    }
    
    const key = keys[index];
    const values = attributes[key];
    const result: Record<string, any>[] = [];
    
    values.forEach(value => {
      const newCurrent = { ...current, [key]: value };
      result.push(...generateCombinations(attributes, newCurrent, keys, index + 1));
    });
    
    return result;
  };
  
  // 生成所有组合
  const combinations = generateCombinations(attributeMap);
  
  // 为每个组合生成变体
  return combinations.map((combination, index) => {
    // 生成变体名称
    const variantName = Object.entries(combination)
      .map(([key, value]) => {
        const attr = attributes.find(a => a.code === key);
        return attr ? `${value}` : '';
      })
      .filter(Boolean)
      .join(' ');
    
    // 生成SKU
    const sku = Object.entries(combination)
      .map(([key, value]) => {
        const attr = attributes.find(a => a.code === key);
        return attr ? `${String(value).substring(0, 3).toUpperCase()}` : '';
      })
      .filter(Boolean)
      .join('-');
    
    return {
      id: `${index}`,
      sku: `SKU-${sku}`,
      name: variantName,
      price: 0,
      specialPrice: 0,
      cost: 0,
      isActive: true,
      attributes: combination
    };
  });
};

/**
 * 合并现有变体和新生成的变体
 * @param existingVariants 现有变体
 * @param newVariants 新生成的变体
 * @returns 合并后的变体
 */
export const mergeVariants = (existingVariants: any[], newVariants: any[]) => {
  // 如果没有现有变体，直接返回新变体
  if (!existingVariants || existingVariants.length === 0) {
    return newVariants;
  }
  
  // 如果没有新变体，直接返回现有变体
  if (!newVariants || newVariants.length === 0) {
    return existingVariants;
  }
  
  // 创建一个映射，键为属性组合的字符串表示，值为变体
  const variantMap = new Map();
  
  // 添加现有变体到映射
  existingVariants.forEach(variant => {
    const key = JSON.stringify(variant.attributes || {});
    variantMap.set(key, variant);
  });
  
  // 合并新变体，如果属性组合已存在，保留现有变体
  newVariants.forEach(variant => {
    const key = JSON.stringify(variant.attributes || {});
    if (!variantMap.has(key)) {
      variantMap.set(key, variant);
    }
  });
  
  // 将映射转换回数组
  return Array.from(variantMap.values());
};

/**
 * 使用示例：
 * 
 * // 在ProductEdit组件中
 * 
 * // 1. 导入工具函数
 * import { 
 *   extractProductAttributes, 
 *   extractProductVariants,
 *   generateVariantCombinations,
 *   mergeVariants
 * } from '@/utils/productVariantHelper';
 * 
 * // 2. 在获取商品详情的useEffect中使用
 * useEffect(() => {
 *   if (isEdit && id) {
 *     const fetchProduct = async () => {
 *       try {
 *         const data = await apiProductGetById(parseInt(id));
 *         
 *         // 提取属性和变体
 *         const extractedAttributes = extractProductAttributes(data);
 *         const extractedVariants = extractProductVariants(data);
 *         
 *         setAttributes(extractedAttributes);
 *         setVariants(extractedVariants);
 *         
 *         // 其他处理...
 *       } catch (error) {
 *         console.error('获取商品详情失败:', error);
 *       }
 *     };
 *     
 *     fetchProduct();
 *   }
 * }, [isEdit, id]);
 * 
 * // 3. 在属性变化时生成变体
 * useEffect(() => {
 *   if (attributes.length > 0) {
 *     // 生成新的变体组合
 *     const newVariants = generateVariantCombinations(attributes);
 *     
 *     // 合并现有变体和新变体
 *     const mergedVariants = mergeVariants(variants, newVariants);
 *     
 *     setVariants(mergedVariants);
 *   }
 * }, [attributes]);
 */
