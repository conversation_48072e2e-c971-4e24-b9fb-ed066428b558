import { apiAdminPermissionPageList, apiAdminPermissionCreate, apiAdminPermissionUpdate } from '@/apis/apis.api';
import { useFormModal } from '@/components/FormilyModal';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { App, Button, Space } from 'antd';
import { useRef } from 'react';

type TEditFormData = {};

function AdminPermissionList() {
  const tableRef = useRef<ActionType>();
  const { message } = App.useApp();

  const { formModalRef: editFormModalRef, formModalHolder: editFormModalHolder } =
    useFormModal<TEditFormData>();

  const showEditModal = useMemoizedFn((record?: TAdminPermission) => {
    editFormModalRef.current?.show({
      modalTitle: '修改',
      modalWidth: 1000,
      onAutoSubmit: async (values) => {
        const data: Partial<TAdminPermission> = {
          ...values,
          type: 1,
        };
        if(record?.id){
          await apiAdminPermissionUpdate(record.id, data);
        } else {
          await apiAdminPermissionCreate(data);
        }
        message.success('提交成功');
        tableRef.current?.reload();
      },
      initialValues: record
        ? {
            ...record,
          }
        : {},
      schema: {
        type: 'object',
        properties: {
          name: {
            title: '名称',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          code: {
            title: '编码',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          description: {
            title: '描述',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
          },
        },
      },
    });
  });

  return (
    <div>
      <ProTable<TAdminPermission>
        actionRef={tableRef}
        headerTitle='后台权限列表'
        rowKey='id'
        toolBarRender={() => [
          <Button key='add' type='primary' onClick={() => showEditModal()}>
            新增
          </Button>,
        ]}
        search={{
          defaultCollapsed: false,
        }}
        columns={[
          { title: '名称', dataIndex: 'name' },
          { title: '编码', dataIndex: 'code' },
          {
            title: '操作',
            valueType: 'option',
            key: 'option',
            render: (_text, record) => {
              return (
                <Space>
                  <a onClick={() => showEditModal(record)}>修改</a>
                </Space>
              );
            },
          },
        ]}
        request={async (params, sorter) => {
          const filters: TQueryFilter[] = [];

          if (params.name) {
            filters.push({ field: 'name', operator: 'like', value: params.name });
          }
          if (params.code) {
            filters.push({ field: 'code', operator: 'eq', value: params.code });
          }
         

          const res = await apiAdminPermissionPageList({
            current: params.current - 1,
            size: params.pageSize,
            filters,
            sorts: [
              { field: 'createdAt', direction: 'desc' },
            ],
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}
      />
      {editFormModalHolder}
    </div>
  );
}

export default AdminPermissionList;

AdminPermissionList.auth = ['apiAdminPermissionPageList', 'apiAdminPermissionCreate', 'apiAdminPermissionUpdate'];
