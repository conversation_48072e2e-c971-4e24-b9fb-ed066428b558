// 首页模块类型定义

// 基础模块接口
export interface TBaseModule {
  id: string;
  type: ModuleType;
  order: number;
}

// 模块类型枚举
export enum ModuleType {
  Carousel = 'carousel',      // 轮播图
  ProductList = 'productList', // 商品列表
  BrandList = 'brandList',     // 品牌列表
  ArticleList = 'articleList', // 文章列表
  HotspotImage = 'hotspotImage' // 热区图
}

// 轮播图模块
export interface TCarouselModule extends TBaseModule {
  type: ModuleType.Carousel;
  items: TCarouselItem[];
}

// 轮播图项
export interface TCarouselItem {
  id: string;
  imageUrl: string;
  link: string;
}

// 商品列表模块
export interface TProductListModule extends TBaseModule {
  type: ModuleType.ProductList;
  title: Record<string, string>;
  moreText: Record<string, string>;
  moreLink: string;
  products: string[];
}

// 品牌列表模块
export interface TBrandListModule extends TBaseModule {
  type: ModuleType.BrandList;
  title: Record<string, string>;
  moreText: Record<string, string>;
  moreLink: string;
  brands: string[];
}

// 文章列表模块
export interface TArticleListModule extends TBaseModule {
  type: ModuleType.ArticleList;
  title: Record<string, string>;
  moreText: Record<string, string>;
  moreLink: string;
  articles: string[];
}



// 热区图模块
export interface THotspotImageModule extends TBaseModule {
  type: ModuleType.HotspotImage;
  imageUrl: string;
  hotspots: THotspot[];
}

// 热点
export interface THotspot {
  id: string;
  x: number; // 热点 x 坐标（百分比）
  y: number; // 热点 y 坐标（百分比）
  width: number; // 热点宽度（百分比）
  height: number; // 热点高度（百分比）
  link: string;
}

// 所有模块类型的联合类型
export type THomeModule = 
  | TCarouselModule 
  | TProductListModule 
  | TBrandListModule 
  | TArticleListModule 
  | THotspotImageModule;

// 首页配置
export interface THomeConfig {
  modules: THomeModule[];
}
