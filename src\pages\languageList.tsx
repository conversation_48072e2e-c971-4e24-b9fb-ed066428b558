import React, { useRef, useState } from 'react';
import { Button, Form, Input, Modal, Space, message } from 'antd';
import { PlusOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import {
  apiLanguagePageList,
  apiLanguageCreate,
  apiLanguageUpdate,
  apiLanguageSetDefault,
  apiLanguageSetActive
} from '@/apis/apis.api';
import { ToolsUtil } from '@/utils/tools';

// Language List Component
const LanguageList = () => {
  const tableRef = useRef<ActionType>();
  const [form] = Form.useForm();
  const [editingLanguage, setEditingLanguage] = useState<TLanguage | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);

  // Handle language form submission
  const handleSubmit = useMemoizedFn(async () => {
    try {
      const values = await form.validateFields();

      if (editingLanguage) {
        // Update existing language
        await apiLanguageUpdate(editingLanguage.code, values);
        message.success('语言更新成功');
      } else {
        // Create new language
        await apiLanguageCreate(values);
        message.success('语言创建成功');
      }

      setIsModalVisible(false);
      form.resetFields();
      tableRef.current?.reload();
    } catch (error) {
      console.error('表单提交错误:', error);
      message.error('提交失败，请检查表单');
    }
  });

  // Handle setting a language as default
  const handleSetDefault = useMemoizedFn(async (code: string) => {
    try {
      await apiLanguageSetDefault(code);
      message.success('默认语言设置成功');
      tableRef.current?.reload();
    } catch (error) {
      message.error('设置默认语言失败');
    }
  });

  // Handle activating/deactivating a language
  const handleSetActive = useMemoizedFn(async (code: string, active: boolean) => {
    try {
      await apiLanguageSetActive(code, active);
      message.success(`语言${active ? '激活' : '停用'}成功`);
      tableRef.current?.reload();
    } catch (error) {
      message.error(`语言${active ? '激活' : '停用'}失败`);
    }
  });

  // Open modal for editing language
  const handleEdit = useMemoizedFn((record: TLanguage) => {
    setEditingLanguage(record);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  });

  // Open modal for creating language
  const handleCreate = useMemoizedFn(() => {
    setEditingLanguage(null);
    form.resetFields();
    setIsModalVisible(true);
  });

  // Language table columns
  const columns = [
    {
      title: '语言代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '语言名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '本地名称',
      dataIndex: 'nativeName',
      key: 'nativeName',
    },
    {
      title: '默认',
      dataIndex: 'isDefault',
      key: 'isDefault',
      render: (isDefault: boolean, record: TLanguage) => (
        isDefault ?
          <CheckCircleOutlined style={{ color: 'green' }} /> :
          <Button
            type="link"
            onClick={() => handleSetDefault(record.code)}
          >
            设为默认
          </Button>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      valueEnum: {
        true: { text: '已激活', status: 'Success' },
        false: { text: '已停用', status: 'Error' },
      },
    },
    {
      title: '操作',
      key: 'actions',
      valueType: 'option',
      render: (_: any, record: TLanguage) => (
        <Space>
          <a onClick={() => handleEdit(record)}>编辑</a>
          <a onClick={() => handleSetActive(record.code, !record.isActive)}>
            {record.isActive ? '停用' : '激活'}
          </a>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<TLanguage>
        actionRef={tableRef}
        headerTitle="语言管理"
        rowKey="code"
        columns={columns}
        search={{
          defaultCollapsed: false,
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreate}
          >
            添加语言
          </Button>,
        ]}
        request={async (params, sorter) => {
          const filters: TFilterItem<TLanguage>[] = [];

          // 添加搜索条件
          if (params.code) {
            filters.push({ field: 'code', operator: 'like', value: params.code });
          }
          if (params.name) {
            filters.push({ field: 'name', operator: 'like', value: params.name });
          }

          let sorts = ToolsUtil.tableSorterToPageListReqSort(sorter);

          // 添加默认排序，激活状态优先
          if (!sorts || sorts.length === 0) {
            sorts = [{ field: 'isActive', direction: 'DESC' }];
          }

          try {
            const res = await apiLanguagePageList({
              current: params.current - 1,
              size: params.pageSize,
              filters,
              sorts,
            });

            return {
              total: res.total || 0,
              data: res.records || [],
              success: true,
            };
          } catch (error) {
            console.error('获取语言列表失败:', error);
            message.error('获取语言列表失败');
            return {
              total: 0,
              data: [],
              success: false,
            };
          }
        }}
      />

      {/* Language Modal */}
      <Modal
        title={editingLanguage ? '编辑语言' : '添加语言'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => setIsModalVisible(false)}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="code"
            label="语言代码"
            rules={[{ required: true, message: '请输入语言代码' }]}
          >
            <Input placeholder="例如：zh_CN" disabled={!!editingLanguage} />
          </Form.Item>

          <Form.Item
            name="name"
            label="语言名称"
            rules={[{ required: true, message: '请输入语言名称' }]}
          >
            <Input placeholder="例如：简体中文" />
          </Form.Item>

          <Form.Item
            name="nativeName"
            label="本地名称"
            rules={[{ required: true, message: '请输入本地名称' }]}
          >
            <Input placeholder="例如：简体中文" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default LanguageList;

LanguageList.auth = ['apiLanguagePageList', 'apiLanguageCreate', 'apiLanguageUpdate', 'apiLanguageSetDefault', 'apiLanguageSetActive'];
