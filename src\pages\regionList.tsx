import React, { useRef, useState } from 'react';
import { Button, Form, Input, Modal, message, Space } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { 
  apiRegionPageList,
  apiRegionCreate,
  apiRegionUpdate,
  apiRegionSetActive,
} from '@/apis/apis.api';
import { ToolsUtil } from '@/utils/tools';

// Region Management Component
const RegionList = () => {
  const [form] = Form.useForm();
  const tableRef = useRef<ActionType>();
  const [editingRegion, setEditingRegion] = useState<TRegion | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);

  // Handle form submission
  const handleSubmit = useMemoizedFn(async () => {
    try {
      const values = await form.validateFields();
      
      if (editingRegion) {
        // Update existing region
        await apiRegionUpdate({
          ...values,
          id: editingRegion.id
        });
        message.success('地区更新成功');
      } else {
        // Create new region
        await apiRegionCreate(values);
        message.success('地区创建成功');
      }
      
      setIsModalVisible(false);
      form.resetFields();
      tableRef.current?.reload();
    } catch (error) {
      console.error('表单提交错误:', error);
      message.error('提交失败，请检查表单');
    }
  });

  // Handle activating/deactivating a region
  const handleSetActive = useMemoizedFn(async (id: string, active: boolean) => {
    try {
      await apiRegionSetActive(id, active);
      message.success(`地区${active ? '激活' : '停用'}成功`);
      tableRef.current?.reload();
    } catch (error) {
      console.error('设置地区状态失败:', error);
      message.error('设置地区状态失败');
    }
  });

  // Open modal for editing
  const handleEdit = useMemoizedFn((record: TRegion) => {
    setEditingRegion(record);
    form.setFieldsValue({
      code: record.code,
      name: record.name,
    });
    setIsModalVisible(true);
  });

  // Open modal for creating
  const handleCreate = useMemoizedFn(() => {
    setEditingRegion(null);
    form.resetFields();
    setIsModalVisible(true);
  });

  // Table columns
  const columns = [
    {
      title: '地区代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '地区名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      valueEnum: {
        true: { text: '已激活', status: 'Success' },
        false: { text: '已停用', status: 'Error' },
      },
    },
    {
      title: '操作',
      key: 'actions',
      valueType: 'option',
      render: (_, record: TRegion) => (
        <Space>
          <a onClick={() => handleEdit(record)}>编辑</a>
          <a onClick={() => handleSetActive(record.id, !record.isActive)}>
            {record.isActive ? '停用' : '激活'}
          </a>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<TRegion>
        actionRef={tableRef}
        headerTitle="地区管理"
        rowKey="id"
        columns={columns}
        search={{
          defaultCollapsed: false,
        }}
        toolBarRender={() => [
          <Button 
            key="add"
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={handleCreate}
          >
            添加地区
          </Button>,
        ]}
        request={async (params, sorter) => {
          const filters: TFilterItem<TRegion>[] = [];
          
          // 添加搜索条件
          if (params.code) {
            filters.push({ field: 'code', operator: 'like', value: params.code });
          }
          if (params.name) {
            filters.push({ field: 'name', operator: 'like', value: params.name });
          }
          if (params.isActive !== undefined) {
            filters.push({ field: 'isActive', operator: 'eq', value: params.isActive });
          }

          let sorts = ToolsUtil.tableSorterToPageListReqSort(sorter);
          
          // 添加默认排序，激活状态优先
          if (!sorts || sorts.length === 0) {
            sorts = [{ field: 'isActive', direction: 'DESC' }];
          }

          try {
            const res = await apiRegionPageList({
              current: params.current - 1,
              size: params.pageSize,
              filters,
              sorts,
            });

            return {
              total: res.total || 0,
              data: res.records || [],
              success: true,
            };
          } catch (error) {
            console.error('获取地区列表失败:', error);
            message.error('获取地区列表失败');
            return {
              total: 0,
              data: [],
              success: false,
            };
          }
        }}
      />

      {/* Region Modal */}
      <Modal
        title={editingRegion ? '编辑地区' : '添加地区'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => setIsModalVisible(false)}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="code"
            label="地区代码"
            rules={[{ required: true, message: '请输入地区代码' }]}
          >
            <Input placeholder="例如：CN, US" disabled={!!editingRegion} />
          </Form.Item>

          <Form.Item
            name="name"
            label="地区名称"
            rules={[{ required: true, message: '请输入地区名称' }]}
          >
            <Input placeholder="例如：中国, 美国" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};


export default RegionList;

RegionList.auth = ['apiRegionPageList', 'apiRegionCreate', 'apiRegionUpdate', 'apiRegionSetActive'];
