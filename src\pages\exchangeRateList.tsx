import React, { useEffect, useState } from 'react';
import { Button, Table, Typography, Breadcrumb, Space, message, Card, Modal, Form, Input, Select, InputNumber, Spin } from 'antd';
import { ArrowLeftOutlined, PlusOutlined } from '@ant-design/icons';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { 
  apiCurrencyPageList, 
  apiExchangeRatesByCurrency,
  apiExchangeRateSetActive,
  apiExchangeRateAddRule,
  apiExchangeRateApplyRule,
  apiExchangeRateRulePageList
} from '@/apis/apis.api';

// Exchange Rate List Component
const ExchangeRateList: React.FC = () => {
  const { currencyCode } = useParams<{ currencyCode: string }>();
  const [sourceCurrency, setSourceCurrency] = useState<TCurrency | null>(null);
  const [allCurrencies, setAllCurrencies] = useState<TCurrency[]>([]);
  const [exchangeRates, setExchangeRates] = useState<TExchangeRate[]>([]);
  const [loading, setLoading] = useState(false);
  const [ruleModalVisible, setRuleModalVisible] = useState(false);
  const [currentRate, setCurrentRate] = useState<TExchangeRate | null>(null);
  const [ruleForm] = Form.useForm();
  const [calculatedRate, setCalculatedRate] = useState<number | null>(null);
  const navigate = useNavigate();
  
  // States for rule selection
  const [ruleList, setRuleList] = useState<TExchangeRateRuleBo[]>([]);
  const [ruleLoading, setRuleLoading] = useState(false);
  const [ruleSearchValue, setRuleSearchValue] = useState('');
  const [selectedRuleId, setSelectedRuleId] = useState<number | 'custom' | null>(null);
  const [customRuleVisible, setCustomRuleVisible] = useState(false);

  // Fetch source currency, available target currencies, and exchange rates
  useEffect(() => {
    const fetchData = async () => {
      if (!currencyCode) return;
      
      setLoading(true);
      try {
        // Get all active currencies
        const res = await apiCurrencyPageList({
          current: 0,
          size: 100,
          filters: [
            { field: 'isActive', operator: 'eq', value: 1 }
          ],
        });

        // Find source currency
        const source = res.records.find(c => c.code === currencyCode);
        if (!source) {
          console.error('未找到指定货币');
          navigate('/currencies');
          return;
        }
        
        setSourceCurrency(source);
        setAllCurrencies(res.records);

        // Fetch exchange rates for this currency
        const ratesRes = await apiExchangeRatesByCurrency(currencyCode);
        console.log('Exchange rates:', ratesRes);
        
        // Ensure we have an array of exchange rates
        const ratesArray = Array.isArray(ratesRes) ? ratesRes : [];
        
        setExchangeRates(ratesArray);
      } catch (error) {
        console.error('获取数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currencyCode, navigate]);

  // Get currency name by code
  const getCurrencyName = (code: string) => {
    const currency = allCurrencies.find(c => c.code === code);
    return currency ? currency.name : code;
  };


  // Calculate the adjusted rate based on rule type and value
  const calculateAdjustedRate = (baseRate: number, ruleType: string, ruleValue: number) => {
    switch (ruleType) {
      case 'FIXED_RATE':
        return ruleValue;
      case 'PERCENTAGE_MARKUP':
        return baseRate * (1 + ruleValue / 100);
      case 'PERCENTAGE_DISCOUNT':
        return baseRate * (1 - ruleValue / 100);
      case 'FIXED_MARKUP':
        return baseRate + ruleValue;
      case 'FIXED_DISCOUNT':
        return baseRate - ruleValue;
      default:
        return baseRate;
    }
  };

  // Preview the adjusted rate when rule form values change
  const handleRuleFormValuesChange = () => {
    try {
      const values = ruleForm.getFieldsValue();
      if (currentRate && values.ruleType && values.value !== undefined) {
        const adjustedRate = calculateAdjustedRate(
          currentRate.rate,
          values.ruleType,
          values.value
        );
        setCalculatedRate(adjustedRate);
      }
    } catch (error) {
      console.error('计算调整后汇率失败:', error);
    }
  };

  // Open the rule modal for a specific exchange rate
  const openRuleModal = (rate: TExchangeRate) => {
    setCurrentRate(rate);
    setCalculatedRate(null);
    setSelectedRuleId(null);
    setCustomRuleVisible(false);
    ruleForm.resetFields();
    setRuleModalVisible(true);
    fetchRules();
  };

  // Fetch available rules
  const fetchRules = async (searchName?: string) => {
    setRuleLoading(true);
    try {
      const filters = [];
      if (searchName) {
        filters.push({ field: 'name', operator: 'like', value: searchName });
      }
      
      const res = await apiExchangeRateRulePageList({
        current: 0,
        size: 50,
        filters,
        sorts: [{ field: 'id', direction: 'DESC' }]
      });
      
      setRuleList(res.records || []);
    } catch (error) {
      console.error('获取规则列表失败:', error);
      message.error('获取规则列表失败');
    } finally {
      setRuleLoading(false);
    }
  };

  // Handle rule selection change
  const handleRuleSelectChange = (value: number | 'custom') => {
    setSelectedRuleId(value);
    
    if (value === 'custom') {
      setCustomRuleVisible(true);
      setCalculatedRate(null);
    } else {
      setCustomRuleVisible(false);
      
      // Find the selected rule and set form values
      const selectedRule = ruleList.find(rule => rule.id === value);
      if (selectedRule && currentRate) {
        // Set form values based on the selected rule
        ruleForm.setFieldsValue({
          description: selectedRule.description,
          ruleType: selectedRule.ruleType,
          value: selectedRule.value
        });
        
        // Calculate and display the adjusted rate
        const adjustedRate = calculateAdjustedRate(
          currentRate.rate,
          selectedRule.ruleType,
          selectedRule.value
        );
        setCalculatedRate(adjustedRate);
      }
    }
  };

  // Handle rule search
  const handleRuleSearch = (value: string) => {
    setRuleSearchValue(value);
    fetchRules(value);
  };

  // Handle rule form submission
  const handleRuleSubmit = async () => {
    try {
      if (!currentRate) return;
      
      setLoading(true);
      
      if (selectedRuleId === 'custom') {
        // Create and apply a new custom rule
        const values = await ruleForm.validateFields();
        
        // Add rule to the exchange rate
        const result = await apiExchangeRateAddRule(
          currentRate.fromCurrency,
          currentRate.toCurrency,
          {
            name: `${currentRate.fromCurrency} to ${currentRate.toCurrency} Custom Rule`,
            description: values.description,
            ruleType: values.ruleType,
            value: values.value,
            isActive: 'TRUE',
            isTemporary: 'TRUE' // Always set as temporary rule
          }
        );
        
        message.success('汇率规则添加成功');
        
        // Update the exchange rate in the local state
        setExchangeRates(prevRates => 
          prevRates.map(rate => 
            (rate.fromCurrency === currentRate.fromCurrency && rate.toCurrency === currentRate.toCurrency) 
              ? result 
              : rate
          )
        );
      } else if (selectedRuleId !== null) {
        // Apply an existing rule
        const result = await apiExchangeRateApplyRule(
          currentRate.fromCurrency,
          currentRate.toCurrency,
          selectedRuleId
        );
        
        message.success('已应用现有规则到汇率');
        
        // Update the exchange rate in the local state
        setExchangeRates(prevRates => 
          prevRates.map(rate => 
            (rate.fromCurrency === currentRate.fromCurrency && rate.toCurrency === currentRate.toCurrency) 
              ? result 
              : rate
          )
        );
      } else {
        message.error('请选择一个规则或创建新规则');
        setLoading(false);
        return;
      }
      
      setRuleModalVisible(false);
    } catch (error) {
      console.error('设置汇率规则失败:', error);
      message.error('设置汇率规则失败');
    } finally {
      setLoading(false);
    }
  };

  // Common column definitions
  const getColumns = (isFromCurrent: boolean) => [
    {
      title: isFromCurrent ? '目标货币代码' : '来源货币代码',
      key: 'currencyCode',
      render: (_: any, record: TExchangeRate) => {
        return isFromCurrent ? record.toCurrency : record.fromCurrency;
      }
    },
    {
      title: isFromCurrent ? '目标货币名称' : '来源货币名称',
      key: 'currencyName',
      render: (_: any, record: TExchangeRate) => {
        const code = isFromCurrent ? record.toCurrency : record.fromCurrency;
        return getCurrencyName(code);
      },
    },
    {
      title: '基础汇率',
      dataIndex: 'rate',
      key: 'rate',
      render: (rate: number) => rate.toFixed(6),
    },
    {
      title: '调整后汇率',
      key: 'adjustedRate',
      render: (_: any, record: TExchangeRate) => {
        // If there's a rule applied and we have the adjusted rate, show it
        if (record.ruleId && record.ruleAppliedRate !== undefined) {
          return <span style={{ color: 'blue' }}>{record.ruleAppliedRate.toFixed(6)}</span>;
        }
        return <span style={{ color: 'gray' }}>无调整</span>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: TExchangeRate) => {
        // Create a unique ID for this exchange rate
        return (
          <Space>
            <a onClick={() => openRuleModal(record)}>设置规则</a>
          </Space>
        );
      },
    }
  ];

  // Filter exchange rates for the two tables
  const fromCurrentRates = exchangeRates.filter(rate => rate.fromCurrency === currencyCode);
  const toCurrentRates = exchangeRates.filter(rate => rate.toCurrency === currencyCode);

  return (
    <>
      <div style={{ marginBottom: 16 }}>
        <Breadcrumb>
          <Breadcrumb.Item>
            <Link to="/currencies">货币管理</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>汇率管理</Breadcrumb.Item>
        </Breadcrumb>
      </div>
      
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate('/currencies')}
          style={{ marginRight: 16 }}
        >
          返回
        </Button>
        {sourceCurrency && (
          <Typography.Title level={4} style={{ margin: 0 }}>
            {sourceCurrency.name} ({sourceCurrency.code}) 汇率管理
          </Typography.Title>
        )}
        <Button 
          type="primary" 
          onClick={() => navigate('/exchange-rate-rules')}
          style={{ marginLeft: 'auto' }}
        >
          汇率规则管理
        </Button>
      </div>

      {/* From Current Currency to Other Currencies */}
      <Card 
        title={<Typography.Title level={5}>从 {sourceCurrency?.name || currencyCode} 到其他货币</Typography.Title>}
        style={{ marginBottom: 24 }}
      >
        <div style={{ marginBottom: 16 }}>
          <Typography.Text>
            共 {fromCurrentRates.length} 条记录
          </Typography.Text>
        </div>

        <Table
          loading={loading}
          rowKey={(record) => `${record.fromCurrency}-${record.toCurrency}`}
          dataSource={fromCurrentRates}
          columns={getColumns(true)}
          pagination={false}
        />
      </Card>

      {/* From Other Currencies to Current Currency */}
      <Card 
        title={<Typography.Title level={5}>从其他货币到 {sourceCurrency?.name || currencyCode}</Typography.Title>}
      >
        <div style={{ marginBottom: 16 }}>
          <Typography.Text>
            共 {toCurrentRates.length} 条记录
          </Typography.Text>
        </div>

        <Table
          loading={loading}
          rowKey={(record) => `${record.fromCurrency}-${record.toCurrency}`}
          dataSource={toCurrentRates}
          columns={getColumns(false)}
          pagination={false}
        />
      </Card>

      {/* Rule Modal */}
      <Modal
        title="设置汇率规则"
        open={ruleModalVisible}
        onCancel={() => setRuleModalVisible(false)}
        onOk={handleRuleSubmit}
        confirmLoading={loading}
        width={600}
      >
        {currentRate && (
          <Form 
            form={ruleForm} 
            layout="vertical"
            onValuesChange={handleRuleFormValuesChange}
          >
            <div style={{ marginBottom: 16 }}>
              <Typography.Text>
                当前汇率: {currentRate.fromCurrency} 到 {currentRate.toCurrency} = {currentRate.rate.toFixed(6)}
              </Typography.Text>
              {calculatedRate !== null && (
                <div style={{ marginTop: 8 }}>
                  <Typography.Text strong style={{ color: 'blue' }}>
                    调整后汇率: {calculatedRate.toFixed(6)}
                  </Typography.Text>
                </div>
              )}
            </div>

            <Form.Item
              label="选择规则"
              required
            >
              <Select
                showSearch
                placeholder="选择一个规则或创建新规则"
                value={selectedRuleId}
                onChange={handleRuleSelectChange}
                onSearch={handleRuleSearch}
                filterOption={false}
                loading={ruleLoading}
                style={{ width: '100%' }}
                notFoundContent={ruleLoading ? <Spin size="small" /> : null}
                dropdownRender={menu => (
                  <div>
                    {menu}
                    <div style={{ display: 'flex', flexWrap: 'nowrap', padding: 8 }}>
                      <Button
                        type="link"
                        icon={<PlusOutlined />}
                        onClick={() => handleRuleSelectChange('custom')}
                        style={{ width: '100%', textAlign: 'left' }}
                      >
                        创建自定义规则
                      </Button>
                    </div>
                  </div>
                )}
              >
                {ruleList.map(rule => (
                  <Select.Option key={rule.id} value={rule.id}>
                    {rule.name} ({rule.ruleType === 'FIXED_RATE' ? '固定汇率' : 
                      rule.ruleType === 'PERCENTAGE_MARKUP' ? '百分比加价' :
                      rule.ruleType === 'PERCENTAGE_DISCOUNT' ? '百分比折扣' :
                      rule.ruleType === 'FIXED_MARKUP' ? '固定加价' :
                      rule.ruleType === 'FIXED_DISCOUNT' ? '固定折扣' : 
                      rule.ruleType}) - {rule.value}
                  </Select.Option>
                ))}
                <Select.Option value="custom">创建自定义规则</Select.Option>
              </Select>
            </Form.Item>

            {customRuleVisible && (
              <>
                <Form.Item
                  name="description"
                  label="规则描述"
                >
                  <Input.TextArea rows={2} placeholder="规则的详细描述" />
                </Form.Item>

                <Form.Item
                  name="ruleType"
                  label="规则类型"
                  rules={[{ required: true, message: '请选择规则类型' }]}
                >
                  <Select placeholder="选择规则类型">
                    <Select.Option value="FIXED_RATE">固定汇率</Select.Option>
                    <Select.Option value="PERCENTAGE_MARKUP">百分比加价</Select.Option>
                    <Select.Option value="PERCENTAGE_DISCOUNT">百分比折扣</Select.Option>
                    <Select.Option value="FIXED_MARKUP">固定加价</Select.Option>
                    <Select.Option value="FIXED_DISCOUNT">固定折扣</Select.Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="value"
                  label="规则值"
                  rules={[{ required: true, message: '请输入规则值' }]}
                >
                  <InputNumber 
                    style={{ width: '100%' }} 
                    placeholder="根据规则类型输入相应的值"
                    precision={6}
                  />
                </Form.Item>
              </>
            )}
          </Form>
        )}
      </Modal>
    </>
  );
};

// Add auth property to the component
export default ExchangeRateList;

ExchangeRateList.auth = ['apiCurrencyPageList', 'apiExchangeRatesByCurrency', 'apiExchangeRateSetActive', 'apiExchangeRateAddRule', 'apiExchangeRateApplyRule', 'apiExchangeRateRulePageList'];
