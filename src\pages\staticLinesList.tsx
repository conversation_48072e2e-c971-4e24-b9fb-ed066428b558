import { apiListStaticLines, apiUpdateStaticLine, apiGetStaticLineById } from '@/apis/gen.apis';
import { useFormModal } from '@/components/FormilyModal';
import useChannels from '@/hooks/useChannels';
import useLocations from '@/hooks/useLocations';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { App, Button, Space, Tag } from 'antd';
import { useRef } from 'react';

type TEditFormData = {
  id?: number;
  channelId?: number;
  locationId?: number;
  businessTypeId?: number;
  ispType?: TIspTypeEnum;
  amount?: number;
  name: string;
  description?: string;
  enabled?: boolean;
  quantity?: number;
  usedQuantity?: number;
  reservedQuantity?: number;
  protocols?: number;
  status?: TStaticProxyLineStatusEnum;
  priority?: number;
  qualityScore?: number;
  successRate?: number;
  avgResponseTime?: number;
  maxConcurrentConnections?: number;
  config?: string;
  remarks?: string;
};

function StaticLinesList() {
  const tableRef = useRef<ActionType>();
  const { message } = App.useApp();

  const { formModalRef: editFormModalRef, formModalHolder: editFormModalHolder } =
    useFormModal<TEditFormData>();

  const { channelOptions, channelEnum } = useChannels();
  const { locations, locationTreeOptions } = useLocations();

  console.log(locations, locationTreeOptions);

  const showEditModal = useMemoizedFn(async (record?: TStaticLineDTO) => {
    let initialValues = {};

    if (record?.id) {
      // 获取详细信息
      const detail = await apiGetStaticLineById(record.id.toString());
      initialValues = { ...detail };
    } else {
      initialValues = {
        enabled: true,
        status: 'ACTIVE',
        priority: 1,
        qualityScore: 0,
        successRate: 0,
        avgResponseTime: 0,
        maxConcurrentConnections: 100,
      };
    }

    editFormModalRef.current?.show({
      modalTitle: record ? '修改静态线路' : '查看静态线路',
      modalWidth: 1000,
      onAutoSubmit: async (values: any) => {
        if (record?.id) {
          await apiUpdateStaticLine(record.id.toString(), values);
          message.success('提交成功');
          tableRef.current?.reload();
        }
      },
      initialValues,
      schema: {
        type: 'object',
        properties: {
          name: {
            title: '名称',
            type: 'string',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
          },
          description: {
            title: '描述',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
          },
          channelId: {
            title: '渠道ID',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              options: channelOptions,
            },
          },
          locationId: {
            title: '位置ID',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'Cascader',
            'x-component-props': {
              options: locationTreeOptions,
            },
          },
          businessTypeId: {
            title: '业务类型ID',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
          },
          ispType: {
            title: 'ISP类型',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            enum: [
              { label: '无', value: 'NONE' },
              { label: '共享', value: 'SHARED' },
              { label: '高级', value: 'PREMIUM' },
            ],
          },
          amount: {
            title: '金额',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              precision: 2,
              min: 0,
            },
          },
          enabled: {
            title: '启用状态',
            type: 'boolean',
            'x-decorator': 'FormItem',
            'x-component': 'Switch',
          },
          quantity: {
            title: '总数量',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              min: 0,
            },
          },
          usedQuantity: {
            title: '已用数量',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              min: 0,
            },
          },
          reservedQuantity: {
            title: '保留数量',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              min: 0,
            },
          },
          status: {
            title: '状态',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            enum: [
              { label: '活跃', value: 'ACTIVE' },
              { label: '非活跃', value: 'INACTIVE' },
              { label: '维护中', value: 'MAINTENANCE' },
            ],
          },
          priority: {
            title: '优先级',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              min: 1,
              max: 10,
            },
          },
          qualityScore: {
            title: '质量分数',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              min: 0,
              max: 100,
              precision: 2,
            },
          },
          successRate: {
            title: '成功率',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              min: 0,
              max: 100,
              precision: 2,
            },
          },
          avgResponseTime: {
            title: '平均响应时间(ms)',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              min: 0,
            },
          },
          maxConcurrentConnections: {
            title: '最大并发连接数',
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              min: 1,
            },
          },
          config: {
            title: '配置',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4,
            },
          },
          remarks: {
            title: '备注',
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
          },
        },
      },
    });
  });

  return (
    <div>
      <ProTable<TStaticLineDTO>
        actionRef={tableRef}
        headerTitle='静态线路列表'
        rowKey='id'
        search={{
          defaultCollapsed: false,
        }}
        columns={[
          { title: 'ID', dataIndex: 'id', },
          // { title: '名称', dataIndex: 'name' },
          // { title: '描述', dataIndex: 'description', ellipsis: true },
          { title: '渠道ID', dataIndex: 'channelId', valueEnum: channelEnum, valueType: 'select' },
          { title: '位置ID', dataIndex: 'locationId', valueType: 'cascader', fieldProps: { options: locationTreeOptions } },
          {
            title: 'ISP类型',
            dataIndex: 'ispType',
            valueEnum: {
              1: '普通',
              2: '原生',
            },
          },
          {
            title: '金额',
            dataIndex: 'amount',
            search: false,
            render: (value) => value ? `¥${value}` : '-',
          },
          {
            title: '启用状态',
            dataIndex: 'enabled',
            valueEnum: {
              true: '启用',
              false: '禁用',
            },
            render: (enabled) => (
              <Tag color={enabled ? 'green' : 'red'}>
                {enabled ? '启用' : '禁用'}
              </Tag>
            ),
          },
          {
            title: '状态',
            dataIndex: 'status',
            valueEnum: {
              0: { text: '活跃', status: 'Success' },
              1: { text: '非活跃', status: 'Default' },
              2: { text: '维护中', status: 'Warning' },
            },
          },
          { title: '总数量', dataIndex: 'quantity', search: false },
          // { title: '已用', dataIndex: 'usedQuantity', },
          // { title: '保留', dataIndex: 'reservedQuantity', },
          // {
          //   title: '操作',
          //   valueType: 'option',
          //   key: 'option',
          //   render: (_text, record) => {
          //     return (
          //       <Space>
          //         <a onClick={() => showEditModal(record)}>编辑</a>
          //       </Space>
          //     );
          //   },
          // },
        ]}
        request={async (params) => {
          const filters: any[] = [];
          if (params.id) {
            filters.push({ field: 'id', operator: 'eq', value: params.id });
          }
          if (params.channelId) {
            filters.push({ field: 'channelId', operator: 'eq', value: params.channelId });
          }
          
          if (params.locationId && params.locationId?.length > 0) {
            filters.push({ field: 'locationId', operator: 'eq', value: params.locationId[params.locationId.length - 1] });
          }

          if (params.enabled) {
            filters.push({ field: 'enabled', operator: 'eq', value: params.enabled === 'true' });
          }
          if (params.status) {
            filters.push({ field: 'status', operator: 'eq', value: Number(params.status) });
          }
          if (params.ispType) {
            filters.push({ field: 'ispType', operator: 'eq', value: Number(params.ispType) });
          }


          const res = await apiListStaticLines({
            current: (params.current || 1) - 1,
            size: params.pageSize || 20,
            filters,
            sorts: [],
          });

          return {
            total: res.total,
            data: res.records,
            success: true,
          };
        }}
      />
      {editFormModalHolder}
    </div>
  );
}

export default StaticLinesList;

StaticLinesList.auth = ['apiListStaticLines', 'apiUpdateStaticLine', 'apiGetStaticLineById'];
