declare namespace API {
  interface ProductBrandBo {
    id: string;
    name: string;
    code: string;
    logo: string;
    website: string;
    description: string;
    isActive: boolean;
    isFeatured: boolean;
    sortOrder: number;
    categoryIds: string[];
    categories?: ProductCategoryBo[];
    createdAt: string;
    updatedAt: string;
  }
  interface ProductCategoryBo {
    id: string;
    name: string;
    code?: string;
  }

  interface PageQuery {
    current: number;
    size: number;
    filters?: TFilterItem<any>[];
    sorts?: TSortItem<any>[];
  }

  interface PageResultProductBrandBo {
    records: ProductBrandBo[];
    total: number;
    current: number;
    size: number;
  }
}
