import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import CacheUtils from './cache';
import router from '@/router';
import { message } from 'antd';
import { md5 } from 'js-md5';
import qs from 'qs';

const axiosInstance = axios.create();

export interface HttpOptions extends AxiosRequestConfig {
  url: string;
  method?: string;
  baseURL?: string;
  /**放在url上的参数 */
  params?: object;
  /**放在body上的参数 */
  data?: object;
  headers?: Record<string, any>;
  /** 是否自动处理响应 */
  autoHandleResponse?: boolean;
  /**是否自动处理错误 */
  autoHandleError?: boolean;
}

export type THttpRes<T = unknown> = {
  code: number;
  msg: string;
  data: T;
};

export default class HttpUtils {
  static TOKEN = CacheUtils.getItem<string>('TOKEN') || '';

  static API_SITE = '/api/admin';

  public static apiSecrect = 'TiKr4hWPTsI=';

  /**
   * 根据后端SignatureUtil逻辑生成签名
   * @param method HTTP方法
   * @param url 请求URL路径
   * @param query 查询字符串
   * @param body 请求体
   * @returns Base64编码的签名 (timestamp:md5)
   */
  public static sign(method: string, url: string, query?: string, body?: string): string {
    const timestamp = Date.now();
    const data = [method.toUpperCase(), url, timestamp.toString()];

    if (query) {
      data.push(query);
    }

    if (body) {
      data.push(body);
    }

    // 添加密钥
    data.push(this.apiSecrect);

    // 连接所有数据
    const signatureData = data.join('');

    // 格式化为 timestamp:md5
    const signatureValue = `${timestamp}:${md5(signatureData)}`;

    // Base64编码
    return btoa(signatureValue);
  }

  static qsStringify(params: Record<string, any>) {
    return qs.stringify(params);
  }
  static async request<T = unknown>(options: HttpOptions): Promise<T> {
    const {
      url: optionsUrl,
      baseURL = this.API_SITE,
      method = 'GET',
      params,
      data = {},
      headers = {},
      autoHandleError = true,
      autoHandleResponse = true,
      ...otherOptions
    } = options || {};

    headers['Authorization'] = this.TOKEN ? `Bearer ${this.TOKEN}` : '';

    const [url, urlSearch] = optionsUrl.split('?');
    const urlQuery = urlSearch ? qs.parse(urlSearch) : {};
    const query = { ...urlQuery, ...(params || {}) };
    const qsStr =
      Object.keys(query).length > 0
        ? `?${qs.stringify(query, {
            // encode: true, arrayFormat: 'brackets'
          })}`
        : '';
    // const bodyString = options.data ? JSON.stringify(options.data) : '';
    // const queryString = qsStr.startsWith('?') ? qsStr.substring(1) : qsStr;
    // headers['X-Signature'] = this.sign(options.method, url, queryString, bodyString);
    const bodyString = options.data ? JSON.stringify(options.data) : ([
      'POST',
      'PUT',
      'PATCH',
    ].includes(method) ? '{}' : '');
    const queryString = qsStr.startsWith('?') ? qsStr.substring(1) : qsStr;
    headers['X-Signature'] = this.sign(method, '/admin' + url, queryString, bodyString);

    const config: AxiosRequestConfig = {
      url: url + qsStr,
      baseURL,
      method,
      // params,
      data,
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        ...headers,
      },
      validateStatus: (status: number) => status >= 200 && status < 400,
      ...(otherOptions || {}),
    };
    try {
      const res = await axiosInstance(config);
      if (autoHandleResponse) {
        return res.data;
      } else {
        return res as T;
      }
    } catch (err) {
      const error = err as {
        code?: number;
        msg?: string;
        response?: AxiosResponse;
      };

      let messageContent = '';
      if (error && error.msg) {
        messageContent = error.msg;
      }
      console.log(error);

      if (error.response?.status === 401) {
        router.navigate('/login', {
          replace: true,
        });
        return;
      }

      messageContent =
        messageContent ||
        error.response?.data?.message ||
        {
          401: '登录状态过期，请重新进入',
          403: '权限不足',
          404: '内容不存在',
          422: '参数错误',
        }[error.response?.status || 400] ||
        '未知请求错误';

      if (autoHandleError) {
        message.error(messageContent);
      }

      return Promise.reject({
        code: error.code || error.response?.data?.code || error.response?.status,
        message: messageContent,
      });
    }
  }

  static get<T = unknown>(
    url: string,
    params?: object,
    otherOptions?: Omit<HttpOptions, 'url' | 'method' | 'params'>,
  ): Promise<T> {
    return this.request<T>({ url, method: 'GET', params, ...otherOptions });
  }

  static post<T = unknown>(
    url: string,
    data?: object,
    otherOptions?: Omit<HttpOptions, 'url' | 'method' | 'data'>,
  ): Promise<T> {
    return this.request<T>({ url, method: 'POST', data, ...otherOptions });
  }

  static put<T = unknown>(
    url: string,
    data?: object,
    otherOptions?: Omit<HttpOptions, 'url' | 'method' | 'data'>,
  ): Promise<T> {
    return this.request<T>({ url, method: 'PUT', data, ...otherOptions });
  }

  static delete<T = unknown>(
    url: string,
    params?: object,
    otherOptions?: Omit<HttpOptions, 'url' | 'method' | 'params'>,
  ): Promise<T> {
    return this.request<T>({ url, method: 'DELETE', params, ...otherOptions });
  }

  static patch<T = unknown>(
    url: string,
    data?: object,
    otherOptions?: Omit<HttpOptions, 'url' | 'method' | 'data'>,
  ): Promise<T> {
    return this.request<T>({ url, method: 'PATCH', data, ...otherOptions });
  }

  static setToken(token?: string, expiredAt?: number) {
    if (!token) {
      this.TOKEN = '';
      CacheUtils.removeItem('TOKEN');
    } else {
      this.TOKEN = token;
      CacheUtils.setItem('TOKEN', token, expiredAt);
    }
  }

  /**
   * 上传文件
   * @returns 上传后的文件url
   */
  static async uploadFile(file?: File) {
    const upload = async (f: File) => {
      const formData = new FormData();
      formData.append('file', f);
      const response = await this.request<TFileUploadResponse>({
        url: '/files',
        method: 'POST',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      const url = '/api' + response.url;
      return url;
    };
    if (file) {
      return upload(file);
    }

    return new Promise<string>((resolve, reject) => {
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.accept = 'image/*';
      fileInput.multiple = false;
      fileInput.onchange = async (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (!file) return;
        try {
          const url = await upload(file);
          resolve(url);
        } catch (error) {
          reject(error);
        } finally {
          fileInput.remove();
        }
      };
      fileInput.onabort = () => {
        reject('文件上传被中止');
      };
      fileInput.onerror = (e) => {
        reject(e);
      };
      fileInput.oncancel = () => {
        reject('文件上传被取消');
      };
      fileInput.click();
    });
  }
}
