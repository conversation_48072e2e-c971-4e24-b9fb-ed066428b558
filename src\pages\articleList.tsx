import React, { useRef, useState } from 'react';
import { Button, Space, Tag, message, Popconfirm } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn, useRequest } from 'ahooks';
import { 
  apiArticlePageList,
  apiArticleCreate,
  apiArticleUpdate,
  apiArticleDelete,
  apiArticleGetById,
  apiArticleCreateOrUpdate,
  apiLanguagePageList
} from '@/apis/apis.api';
import { useNavigate } from 'react-router-dom';
import { ToolsUtil } from '@/utils/tools';

// 文章列表组件
const ArticleList: React.FC = () => {
  const tableRef = useRef<ActionType>();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  // 处理编辑文章
  const handleEdit = useMemoizedFn((id: string) => {
    navigate('/article-edit', { state: { id } });
  });

  // 处理查看文章
  const handleView = useMemoizedFn(async (id: string) => {
    try {
      setLoading(true);
      // 跳转到编辑页面并设置为只读模式
      navigate('/article-edit', { state: { id, viewMode: true } });
    } catch (error) {
      console.error('获取文章详情失败:', error);
      message.error('获取文章详情失败');
    } finally {
      setLoading(false);
    }
  });

  // 处理创建文章
  const handleCreate = useMemoizedFn(() => {
    navigate('/article-edit');
  });

  // 处理删除文章
  const handleDelete = useMemoizedFn(async (id: string) => {
    try {
      await apiArticleDelete(id);
      message.success('文章删除成功');
      tableRef.current?.reload();
    } catch (error) {
      console.error('删除文章失败:', error);
      message.error('删除文章失败');
    }
  });

  // 获取所有激活的语言
  const { data: languages = [] } = useRequest(
    () => apiLanguagePageList({
      current: 0,
      size: 100,
      filters: [{ field: 'isActive', operator: 'eq', value: 1 }]
    }).then(res => res.records || []),
    {
      manual: false,
      cacheKey: 'active-languages',
    }
  );

  // 语言选项
  const languageOptions = languages.map(lang => ({
    label: `${lang.nativeName} (${lang.code})`,
    value: lang.code
  }));

  return (
    <>
      <ProTable<TArticleBo>
        actionRef={tableRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            type="primary"
            onClick={handleCreate}
          >
            新建
          </Button>,
        ]}
        request={async (params, sorter) => {
          const filters: TFilterItem<TArticleBo>[] = [];
          
          // 添加搜索条件
          if (params.title) {
            filters.push({ field: 'title', operator: 'like', value: params.title });
          }
          if (params.type) {
            filters.push({ field: 'type', operator: 'eq', value: params.type });
          }
          if (params.status) {
            filters.push({ field: 'status', operator: 'eq', value: params.status });
          }
          if (params.author) {
            filters.push({ field: 'author', operator: 'like', value: params.author });
          }
          if (params.languageCode) {
            filters.push({ field: 'languageCode', operator: 'eq', value: params.languageCode });
          }

          let sorts = ToolsUtil.tableSorterToPageListReqSort(sorter);
          
          // 添加默认排序，按创建时间降序
          if (!sorts || sorts.length === 0) {
            sorts = [{ field: 'createdAt', direction: 'DESC' }];
          }

          try {
            const res = await apiArticlePageList({
              current: params.current - 1,
              size: params.pageSize,
              filters,
              sorts,
            });

            return {
              total: res.total || 0,
              data: res.records || [],
              success: true,
            };
          } catch (error) {
            console.error('获取文章列表失败:', error);
            message.error('获取文章列表失败');
            return {
              total: 0,
              data: [],
              success: false,
            };
          }
        }}
        columns={[
          {
            title: '编码',
            dataIndex: 'code',
            width: 100,
            ellipsis: true,
          },
          {
            title: '标题',
            dataIndex: 'title',
            width: 180,
            ellipsis: true,
          },
          {
            title: '类型',
            dataIndex: 'type',
            width: 100,
            valueEnum: {
              0: { text: '服务条款/隐私政策' },
              1: { text: '新闻' },
              2: { text: '用户指南/教程' },
              3: { text: '公告' },
              4: { text: '常见问题' },
              5: { text: '关于我们' },
              6: { text: '其他' }
            }
          },
          {
            title: '语言',
            dataIndex: 'languageCode',
            width: 100,
            ellipsis: true,
            valueType: 'select',
            valueEnum: languages.reduce((acc, lang) => {
              acc[lang.code] = { text: `${lang.nativeName} (${lang.code})` };
              return acc;
            }, {} as Record<string, { text: string }>),
            fieldProps: {
              options: languageOptions,
            },
            render: (_, record) => {
              const language = languages.find(lang => lang.code === record.languageCode);
              return record.languageCode ? `${language?.nativeName || ''} (${record.languageCode})` : '默认';
            }
          },
          {
            title: '作者',
            dataIndex: 'author',
            width: 80,
            ellipsis: true,
          },
          {
            title: '状态',
            dataIndex: 'isActive',
            width: 80,
            valueEnum: {
              true: { text: '激活', status: 'Success' },
              false: { text: '禁用', status: 'Error' }
            }
          },
          {
            title: '浏览量',
            dataIndex: 'viewCount',
            width: 80,
          },
          {
            title: '发布日期',
            dataIndex: 'publishDate',
            width: 120,
            ellipsis: true,
          },
          {
            title: '操作',
            width: 120,
            valueType: 'option',
            fixed: 'right',
            render: (_, record) => (
              <Space>
                <Button type="link" size="small" onClick={() => handleView(record.id)}>
                  查看
                </Button>
                <Button type="link" size="small" onClick={() => handleEdit(record.id)}>
                  编辑
                </Button>
                <Popconfirm
                  title="确定删除该文章吗?"
                  onConfirm={() => handleDelete(record.id)}
                >
                  <Button type="link" size="small" danger>
                    删除
                  </Button>
                </Popconfirm>
              </Space>
            ),
          },
        ]}
        pagination={{
          showQuickJumper: true,
          pageSize: 10,
        }}
        scroll={{ x: 'max-content' }}
      />
    </>
  );
};


export default ArticleList;

ArticleList.auth = ['apiArticlePageList', 'apiArticleCreate', 'apiArticleUpdate', 'apiArticleDelete', 'apiArticleGetById', 'apiArticleCreateOrUpdate', 'apiLanguagePageList'];
