# Mid-Shop Admin 系统功能与模块分析

## 项目概述

Mid-Shop Admin 是一个基于 React 的电商管理后台系统，提供了完整的电商平台管理功能，包括用户管理、商品管理、内容管理、国际化设置等模块。该系统使用 TypeScript 开发，采用了现代化的前端技术栈，包括 React、Ant Design、Formily 等。

## 技术栈

- **前端框架**：React 18.2.0
- **UI 组件库**：Ant Design 5.19.3、Ant Design Pro Components 2.7.14
- **表单处理**：Formily (@formily/antd-v5, @formily/core, @formily/react)
- **状态管理**：Zustand 5.0.1、Foca 3.2.0
- **路由**：React Router DOM 6.25.1
- **HTTP 请求**：Axios 1.7.2
- **富文本编辑器**：@wangeditor/editor 5.1.23
- **构建工具**：Vite 5.3.5
- **CSS 预处理器**：Less、Sass
- **工具库**：Lodash、Dayjs、UUID 等

## 系统架构

系统采用模块化设计，主要目录结构如下：

- **src/apis**：API 接口定义
- **src/bin**：命令行工具，用于生成页面、组件和图标
- **src/components**：公共组件
- **src/hooks**：自定义 Hooks
- **src/layouts**：布局组件
- **src/pages**：页面组件
- **src/stores**：状态管理
- **src/types**：TypeScript 类型定义
- **src/utils**：工具函数

## 核心功能模块

### 1. 用户管理

管理系统的用户、角色和权限，实现细粒度的权限控制。

#### 主要功能：

- **管理员列表**：查看和管理系统管理员
- **角色列表**：创建和管理角色，为角色分配权限
- **权限管理**：管理系统权限项

#### 相关 API：

- `/users/page`：获取用户分页列表
- `/roles/page`：获取角色分页列表
- `/permissions`：获取所有权限列表
- `/role/setPermissions`：设置角色权限

### 2. 国际化设置

提供完整的国际化管理功能，支持多语言、多货币和多地区。

#### 主要功能：

- **货币管理**：创建、更新、设置默认货币、激活/停用货币
- **汇率管理**：管理不同货币之间的汇率
- **语言管理**：创建、更新、设置默认语言、激活/停用语言
- **翻译管理**：管理系统中的翻译文本
- **地区管理**：创建、更新、激活/停用地区，支持多级地区管理（国家、省/州、城市、区/县）

#### 相关 API：

- **货币相关**：

  - `/currencies/page`：货币分页列表
  - `/currencies`：创建货币
  - `/currencies/{code}`：更新货币
  - `/currencies/{code}/set-default`：设置默认货币
  - `/currencies/{code}/set-active`：设置货币激活状态

- **汇率相关**：

  - `/exchange-rates/page`：汇率分页列表
  - `/exchange-rates`：创建/更新汇率
  - `/exchange-rates/{id}/set-active`：设置汇率激活状态
  - `/currencies/{code}/exchange-rates`：获取指定货币的汇率列表
  - `/currencies/exchange-rates/{fromCurrency}/{toCurrency}`：获取两种货币之间的汇率

- **语言相关**：

  - `/languages/page`：语言分页列表
  - `/languages`：创建语言
  - `/languages/{code}`：更新语言
  - `/languages/{code}/set-default`：设置默认语言
  - `/languages/{code}/set-active`：设置语言激活状态
  - `/languages/active`：获取所有激活的语言

- **翻译相关**：

  - `/languages/{code}/translations`：获取语言的翻译列表
  - `/languages/translations`：创建或更新翻译
  - `/languages/translations/batch`：批量创建或更新翻译
  - `/translations/page`：翻译分页列表
  - `/translations/by-key`：根据键获取翻译

- **地区相关**：
  - `/regions/page`：地区分页列表
  - `/regions`：创建地区
  - `/regions/{code}`：更新地区
  - `/regions/{id}/set-active`：设置地区激活状态

### 3. 内容管理

管理系统中的文章和其他内容。

#### 主要功能：

- **文章管理**：创建、编辑、删除文章
- **文章分类**：管理文章分类

#### 文章类型：

- 政策（POLICY）：服务条款、隐私政策等
- 新闻（NEWS）：新闻文章
- 指南（GUIDE）：用户指南和教程
- 公告（ANNOUNCEMENT）：公告
- 常见问题（FAQ）：常见问题解答
- 关于我们（ABOUT）：关于我们
- 其他（OTHER）：其他类型

#### 相关 API：

- `/articles/page`：获取文章分页列表
- `/articles/{id}`：根据 ID 获取/更新/删除文章
- `/articles`：创建文章

### 4. 商品管理

管理电商平台的商品相关信息。

#### 主要功能：

- **品牌管理**：创建、编辑、删除品牌，设置品牌激活状态和推荐状态
- **类别管理**：创建、编辑、删除商品类别，支持多级类别结构

#### 相关 API：

- **品牌相关**：

  - `/brands/page`：品牌分页列表
  - `/brands/{id}`：根据 ID 获取/更新/删除品牌
  - `/brands`：创建品牌
  - `/brands/{id}/set-active`：设置品牌激活状态
  - `/brands/{id}/set-featured`：设置品牌推荐状态
  - `/brands/search`：搜索品牌
  - `/brands/featured`：获取推荐品牌
  - `/brands/active`：获取激活品牌
  - `/brands/category/{categoryId}`：根据类别获取品牌

- **类别相关**：
  - `/categories/page`：类别分页列表
  - `/categories/{id}`：根据 ID 获取/更新/删除类别
  - `/categories`：创建类别
  - `/categories/tree`：获取类别树
  - `/categories/{id}/subtree`：获取指定根节点的类别子树
  - `/categories/code/{code}`：根据编码获取类别
  - `/categories/search`：搜索类别
  - `/categories/roots`：获取根类别
  - `/categories/parent/{parentId}`：根据父 ID 获取子类别
  - `/categories/level/{level}`：根据层级获取类别
  - `/categories/{id}/set-active`：设置类别激活状态

### 5. 收藏管理

管理用户的收藏商品和收藏分组。

#### 主要功能：

- **收藏商品管理**：创建、编辑、删除收藏商品
- **收藏分组管理**：创建、编辑、删除收藏分组
- **收藏备注功能**：为收藏商品添加备注
- **收藏排序功能**：对收藏商品进行排序

#### 相关 API：

- `/favorites/page`：获取收藏分页列表
- `/favorites/{id}`：根据 ID 获取/更新/删除收藏
- `/favorites`：创建收藏
- `/favorites/groups`：获取收藏分组列表
- `/favorites/groups/{id}`：根据 ID 获取/更新/删除收藏分组
- `/favorites/groups`：创建收藏分组

## 数据模型

### 用户权限相关

- **TAdminUser**：管理员用户
- **TAdminRole**：角色
- **TAdminPermission**：权限

### 国际化相关

- **TCurrency**：货币
- **TLanguage**：语言
- **TTranslation**：翻译
- **TRegion**：地区
- **TExchangeRate**：汇率

### 内容相关

- **TArticleBo**：文章
- **ArticleTypeEnum**：文章类型枚举

### 商品相关

- **ProductBrandBo**：品牌
- **ProductCategoryBo**：商品类别

### 收藏相关

- **FavoriteBo**：收藏
- **FavoriteGroupBo**：收藏分组

## 系统特点

1. **完整的权限控制**：基于角色的权限控制系统，可以精细控制用户对各功能的访问权限
2. **国际化支持**：支持多语言、多货币、多地区，适合国际化电商平台
3. **模块化设计**：系统各功能模块划分清晰，便于维护和扩展
4. **响应式 UI**：使用 Ant Design 组件库，提供现代化的用户界面
5. **丰富的表单组件**：使用 Formily 表单解决方案，支持复杂表单场景
6. **富文本编辑**：集成 wangEditor 富文本编辑器，支持文章内容的富文本编辑

## 开发工具

系统提供了一些便捷的开发工具：

- **genPage**：生成页面模板
- **genComponent**：生成组件模板
- **genIcon**：生成图标组件

## 部署相关

系统支持通过 Docker 部署，提供了 Dockerfile 和 nginx.conf 配置文件。支持不同环境的配置（开发环境、生产环境）。
