import { useState, useEffect } from 'react'
import { Editor, Toolbar } from '@wangeditor/editor-for-react'
import { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor'
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import HttpUtils from '@/utils/http'



export interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
}

const uploadConf = (type: 'image' | 'video') => ({
  fieldName: 'file',
  // 单个文件的最大体积限制，默认为 50M
  maxFileSize: 50 * 1024 * 1024, 
  
  server: `${HttpUtils.API_SITE}/upload`,
  headers: {
    Authorization: `Bearer ${HttpUtils.TOKEN}`,
  },
  customInsert(res: any, insertFn: (url: string, videoCover?: string) => void) {
    const baseUrl = res.data.url
    console.log(baseUrl)
    switch (type) {
      case 'image':
        insertFn(`${baseUrl}?x-oss-process=image/resize,w_750`)
        break
      case 'video':
        insertFn(baseUrl, `${baseUrl}?x-oss-process=video/snapshot,t_1000,f_jpg,w_750,m_fast`)
        break
      default:
        insertFn(baseUrl)
    }
  },
});

function RichTextEditor(props: RichTextEditorProps) { // editor 实例
  const [editor, setEditor] = useState<IDomEditor | null>(null)   // TS 语法

  // 工具栏配置
  const toolbarConfig: Partial<IToolbarConfig> = {}

  // 编辑器配置
  const editorConfig: Partial<IEditorConfig> = {
    placeholder: '请输入内容...',
    MENU_CONF: {
      uploadImage: uploadConf('image'),
      uploadVideo: uploadConf('video'),
    }
  }

  useEffect(() => {
    return () => {
      if (editor == null) return
      editor.destroy()
      setEditor(null)
    }
  }, [editor])

  return (
    <div className='rounded-md overflow-hidden border border-solid border-gray-200'>
      <Toolbar
        editor={editor}
        defaultConfig={toolbarConfig}
        mode="default"
        style={{ borderBottom: '1px solid #ccc' }}
      />
      <Editor
        defaultConfig={editorConfig}
        value={props.value}
        onCreated={setEditor}
        onChange={editor => props.onChange(editor.getHtml())}
        mode="default"
        className="h-[500px] overflow-y-hidden"
      />
    </div>
  )
}

export default RichTextEditor
