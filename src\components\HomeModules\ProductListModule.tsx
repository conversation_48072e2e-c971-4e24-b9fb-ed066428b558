import React, { useState, useEffect } from 'react';
import { Button, Modal, Form, Input, List, Space, Image, Card, Row, Col, message } from 'antd';
import { DeleteOutlined, RightOutlined } from '@ant-design/icons';
import { TProductListModule } from './types';
import BaseModuleWrapper from './BaseModuleWrapper';
import { apiProductPageList } from '@/apis/apis.api';
import { useModel } from 'foca';
import { homeSettingModel } from '@/stores/homeSettingModel';
import I18nField from '../I18nField';

interface ProductListModuleProps {
  module: TProductListModule;
  isFirst: boolean;
  isLast: boolean;
  onUpdate: (updatedModule: TProductListModule) => void;
  onDelete: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onInsertBefore: () => void;
  isEditing?: boolean;
}

const ProductListModuleComponent: React.FC<ProductListModuleProps> = ({
  module,
  isFirst,
  isLast,
  onUpdate,
  onDelete,
  onMoveUp,
  onMoveDown,
  onInsertBefore,
  isEditing: initialIsEditing
}) => {
  const [isEditing, setIsEditing] = useState(initialIsEditing || false);
  const homeSettingModalState = useModel(homeSettingModel);
  const [isModuleSettingVisible, setIsModuleSettingVisible] = useState(false);
  const [moduleForm] = Form.useForm();
  const [productSelectVisible, setProductSelectVisible] = useState(false);
  const [productList, setProductList] = useState<any[]>([]);
  const [productLoading, setProductLoading] = useState(false);
  const [selectedProductIds, setSelectedProductIds] = useState<string[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  useEffect(() => {
    homeSettingModel.loadProductByIds(module.products);
  }, [module.products])

  const handleModuleSettings = () => {
    moduleForm.setFieldsValue({
      title: module.title,
      moreText: module.moreText,
      moreLink: module.moreLink
    });
    setIsModuleSettingVisible(true);
  };

  const handleModuleSettingsSubmit = () => {
    moduleForm.validateFields().then(values => {
      onUpdate({
        ...module,
        title: values.title,
        moreText: values.moreText,
        moreLink: values.moreLink
      });
      setIsModuleSettingVisible(false);
    });
  };

  const loadProducts = async (options?: {
    page?: number, pageSize?: number, keyword?: string
  }) => {
    const { page = 1, pageSize = 6, keyword } = options || {};
    setProductLoading(true);
    try {

      const res = await apiProductPageList({
        current: page - 1,
        size: pageSize,
        filters: keyword ? [{ field: 'name', operator: 'like', value: keyword }] : [],
        sorts: [],
      });
      setProductList(res.records || []);
      setPagination({
        current: page,
        pageSize,
        total: res.total || 0
      });
      homeSettingModel.updateProductMapByProducts(res.records || []);
    } catch (error) {
      console.error('加载商品列表失败', error);
      message.error('加载商品列表失败');
    } finally {
      setProductLoading(false);
    }
  };

  const handleOpenProductSelector = () => {
    setProductSelectVisible(true);
    loadProducts();
    setSelectedProductIds([...module.products]);
  };

  const handleProductSelect = (productId: string) => {
    if (selectedProductIds.includes(productId)) {
      setSelectedProductIds(selectedProductIds.filter(id => id !== productId));
    } else {
      setSelectedProductIds([...selectedProductIds, productId]);
    }
  };

  const handleConfirmProductSelection = () => {
    onUpdate({
      ...module,
      products: Array.from(new Set([...selectedProductIds]))
    });

    setProductSelectVisible(false);
    setSelectedProductIds([]);
    console.log("handleConfirmProductSelection 12", isEditing);
  };

  useEffect(() => {
    console.log("isEditing", isEditing);
  }, [isEditing]);

  const handleDeleteItem = (itemId: string) => {
    const updatedItems = module.products.filter(item => item !== itemId);
    onUpdate({
      ...module,
      products: updatedItems
    });
  };

  const handleSearch = (key: string) => {
    loadProducts({ keyword: key });
  };

  const renderProductListContent = () => {
    return (
      <div>
        <div className="flex justify-between mb-4">
          <div className="text-lg font-medium"  >{module.title['zh-CN'] || '商品列表'}</div>
          {module.moreText && (
            <a href={module.moreLink} className="text-blue-500 flex items-center">
              {module.moreText['zh-CN']} <RightOutlined />
            </a>
          )}
        </div>

        {module.products.length === 0 ? (
          <div className="h-40 flex items-center justify-center bg-gray-100">
            <p className="text-gray-500">暂无商品</p>
          </div>
        ) : (
          <Row gutter={[16, 16]}>
            {module.products.map(productId => {
              const product = homeSettingModalState.productMap[productId];
              return (
                product ? <Col span={4} key={productId}>
                  <Card
                    hoverable
                    cover={
                      <Image
                        alt={product.name}
                        src={product.mainImage}
                        className="h-40 object-cover"
                        preview={false}
                      />
                    }
                  >
                    <Card.Meta
                      title={product.name}
                      description={<div className="text-red-500">¥{product.price?.toFixed(2)}</div>}
                    />
                  </Card>
                </Col> : null
              );
            })}
          </Row>
        )}
      </div>
    );
  };

  const renderEditContent = () => {
    return (
      <div>
        <div className="flex justify-between mb-4">
          <div className="text-lg font-medium">商品列表模块</div>
          <Space>
            <Button onClick={handleModuleSettings}>模块设置</Button>
            <Button type="primary" onClick={handleOpenProductSelector}>
              从商品库选择
            </Button>

          </Space>
        </div>

        <List
          itemLayout="horizontal"
          dataSource={module.products}
          renderItem={item => {
            const product = homeSettingModel.state.productMap[item];
            return product ? (
              <List.Item
                actions={[
                  <Button danger icon={<DeleteOutlined />} onClick={() => handleDeleteItem(item)}>删除</Button>
                ]}
              >
                <List.Item.Meta
                  avatar={<Image src={product.mainImage} width={80} height={80} />}
                  title={product.name}
                  description={<div className="text-red-500">¥{product.price?.toFixed(2)}</div>}
                />
              </List.Item>
            ) : null;
          }}
        />

        <Modal
          title="选择商品"
          open={productSelectVisible}
          onOk={handleConfirmProductSelection}
          onCancel={() => setProductSelectVisible(false)}
          width={800}
          okButtonProps={{ disabled: selectedProductIds.length === 0 }}
        >
          <div className="mb-4 flex justify-between">

            <div>已选择 {selectedProductIds.length} 个商品</div>
            <Input.Search
              className="w-1/2"
              placeholder="搜索商品"
              onSearch={handleSearch}
            />
          </div>
          <List
            itemLayout="horizontal"
            dataSource={productList}
            loading={productLoading}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              onChange: (page) => loadProducts({ page })
            }}
            renderItem={item => (
              <List.Item
                actions={[
                  <Button
                    type={selectedProductIds.includes(item.id) ? "primary" : "default"}
                    onClick={() => handleProductSelect(item.id)}
                  >
                    {selectedProductIds.includes(item.id) ? "已选择" : "选择"}
                  </Button>
                ]}
              >
                <List.Item.Meta
                  avatar={<Image src={item.mainImage} width={60} height={60} />}
                  title={item.name}
                  description={`价格: ¥${item.price?.toFixed(2) || '0.00'} | 库存: ${item.stock || 0}`}
                />
              </List.Item>
            )}
          />
        </Modal>

        <Modal
          title="模块设置"
          open={isModuleSettingVisible}
          onOk={handleModuleSettingsSubmit}
          onCancel={() => setIsModuleSettingVisible(false)}
        >
          <Form form={moduleForm} layout="vertical">
            <Form.Item
              name="title"
              label="模块标题"
              rules={[{ required: true, message: '请输入模块标题' }]}
            >
              <I18nField>
                <Input placeholder="请输入模块标题" />
              </I18nField>
            </Form.Item>
            <Form.Item
              name="moreText"
              label="更多按钮文本"
            >
              <I18nField>
                <Input placeholder="请输入更多按钮文本" />
              </I18nField>
            </Form.Item>
            <Form.Item
              name="moreLink"
              label="更多按钮链接"
            >
              <Input placeholder="请输入更多按钮链接" />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    );
  };

  return (
    <BaseModuleWrapper
      isFirst={isFirst}
      isLast={isLast}
      onDelete={onDelete}
      onMoveUp={onMoveUp}
      onMoveDown={onMoveDown}
      onInsertBefore={onInsertBefore}
      isEditing={isEditing}
      onEditingChange={setIsEditing}
    >
      {isEditing ? renderEditContent() : renderProductListContent()}
    </BaseModuleWrapper>
  );
};

export default ProductListModuleComponent;
