import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  Form,
  Input,
  Modal,
  message,
  Divider,
  Select
} from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useMemoizedFn } from 'ahooks';
import {
  apiOrderGetOrderDetails,
  apiOrderUpdateOrderStatus,
  apiOrderUpdatePaymentStatus,
  apiOrderAddAdminNotes,
  apiOrderPlatformReceive,
  apiOrderPlatformShip,
  apiOrderPlatformVerify,
  apiOrderPlatformReceiveReturn
} from '@/apis/apis.api';
import ClientUserObj from '@/components/ClientUserObj';

// Order status options
const ORDER_STATUS_OPTIONS = [
  { label: '待付款', value: 'PENDING_PAYMENT' },
  { label: '已付款', value: 'PAID' },
  { label: '处理中', value: 'PROCESSING' },
  { label: '待卖家发货', value: 'PENDING_SELLER_SHIPPING' },
  { label: '运送至平台中', value: 'SHIPPING_TO_PLATFORM' },
  { label: '平台验证中', value: 'PLATFORM_VERIFICATION' },
  { label: '待平台发货', value: 'PENDING_PLATFORM_SHIPPING' },
  { label: '已发货', value: 'SHIPPED' },
  { label: '运送至买家中', value: 'SHIPPING_TO_BUYER' },
  { label: '已送达', value: 'DELIVERED' },
  { label: '已完成', value: 'COMPLETED' },
  { label: '已取消', value: 'CANCELLED' },
  { label: '已退款', value: 'REFUNDED' },
  { label: '有争议', value: 'DISPUTED' },
  { label: '待审批', value: 'PENDING_APPROVAL' },
  { label: '暂停', value: 'ON_HOLD' },
  { label: '退回平台中', value: 'RETURNING_TO_PLATFORM' },
  { label: '退回卖家中', value: 'RETURNING_TO_SELLER' }
];

// Payment status options
const PAYMENT_STATUS_OPTIONS = [
  { label: '待处理', value: 'PENDING' },
  { label: '处理中', value: 'PROCESSING' },
  { label: '已完成', value: 'COMPLETED' },
  { label: '失败', value: 'FAILED' },
  { label: '已退款', value: 'REFUNDED' },
  { label: '有争议', value: 'DISPUTED' },
  { label: '已取消', value: 'CANCELLED' },
  { label: '需要验证', value: 'VERIFICATION_REQUIRED' }
];

// Order status tag colors
const ORDER_STATUS_COLORS = {
  'PENDING_PAYMENT': 'orange',
  'PAID': 'green',
  'PROCESSING': 'blue',
  'PENDING_SELLER_SHIPPING': 'gold',
  'SHIPPING_TO_PLATFORM': 'purple',
  'PLATFORM_VERIFICATION': 'blue',
  'PENDING_PLATFORM_SHIPPING': 'gold',
  'SHIPPED': 'cyan',
  'SHIPPING_TO_BUYER': 'purple',
  'DELIVERED': 'geekblue',
  'COMPLETED': 'green',
  'CANCELLED': 'red',
  'REFUNDED': 'purple',
  'DISPUTED': 'magenta',
  'PENDING_APPROVAL': 'gold',
  'ON_HOLD': 'gray',
  'RETURNING_TO_PLATFORM': 'orange',
  'RETURNING_TO_SELLER': 'orange'
};

// Payment status tag colors
const PAYMENT_STATUS_COLORS = {
  'PENDING': 'orange',
  'PROCESSING': 'blue',
  'COMPLETED': 'green',
  'FAILED': 'red',
  'REFUNDED': 'purple',
  'DISPUTED': 'magenta',
  'CANCELLED': 'red',
  'VERIFICATION_REQUIRED': 'gold'
};

const OrderDetail: React.FC & { auth?: string[] } = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Forms
  const [statusForm] = Form.useForm();
  const [paymentStatusForm] = Form.useForm();
  const [adminNotesForm] = Form.useForm();
  const [platformShipForm] = Form.useForm();
  const [platformVerifyForm] = Form.useForm();
  const [platformReceiveReturnForm] = Form.useForm();

  // Modal states
  const [isStatusModalVisible, setIsStatusModalVisible] = useState(false);
  const [isPaymentStatusModalVisible, setIsPaymentStatusModalVisible] = useState(false);
  const [isAdminNotesModalVisible, setIsAdminNotesModalVisible] = useState(false);
  const [isPlatformReceiveModalVisible, setIsPlatformReceiveModalVisible] = useState(false);
  const [isPlatformShipModalVisible, setIsPlatformShipModalVisible] = useState(false);
  const [isPlatformVerifyModalVisible, setIsPlatformVerifyModalVisible] = useState(false);
  const [isPlatformReceiveReturnModalVisible, setIsPlatformReceiveReturnModalVisible] = useState(false);

  // Fetch order details
  const fetchOrderDetails = useMemoizedFn(async () => {
    if (!id) return;

    setLoading(true);
    try {
      const orderData = await apiOrderGetOrderDetails(id);
      setOrder(orderData);
    } catch (error) {
      console.error('获取订单详情失败:', error);
      message.error('获取订单详情失败');
    } finally {
      setLoading(false);
    }
  });

  useEffect(() => {
    fetchOrderDetails();
  }, [id, fetchOrderDetails]);

  // Open modal for updating order status
  const handleOpenStatusModal = useMemoizedFn(() => {
    if (!order) return;

    statusForm.setFieldsValue({
      status: order.status
    });
    setIsStatusModalVisible(true);
  });

  // Handle updating order status
  const handleUpdateStatus = useMemoizedFn(async () => {
    try {
      if (!order) return;

      const values = await statusForm.validateFields();
      await apiOrderUpdateOrderStatus(order.id, values.status);

      message.success('订单状态更新成功');
      setIsStatusModalVisible(false);
      fetchOrderDetails();
    } catch (error) {
      console.error('更新订单状态失败:', error);
      message.error('更新订单状态失败');
    }
  });

  // Open modal for updating payment status
  const handleOpenPaymentStatusModal = useMemoizedFn(() => {
    if (!order) return;

    paymentStatusForm.setFieldsValue({
      paymentStatus: order.paymentStatus
    });
    setIsPaymentStatusModalVisible(true);
  });

  // Handle updating payment status
  const handleUpdatePaymentStatus = useMemoizedFn(async () => {
    try {
      if (!order) return;

      const values = await paymentStatusForm.validateFields();
      await apiOrderUpdatePaymentStatus(order.id, values.paymentStatus);

      message.success('支付状态更新成功');
      setIsPaymentStatusModalVisible(false);
      fetchOrderDetails();
    } catch (error) {
      console.error('更新支付状态失败:', error);
      message.error('更新支付状态失败');
    }
  });

  // Open modal for adding admin notes
  const handleOpenAdminNotesModal = useMemoizedFn(() => {
    if (!order) return;

    adminNotesForm.setFieldsValue({
      adminNotes: order.adminNotes || ''
    });
    setIsAdminNotesModalVisible(true);
  });

  // Handle adding admin notes
  const handleAddAdminNotes = useMemoizedFn(async () => {
    try {
      if (!order) return;

      const values = await adminNotesForm.validateFields();
      await apiOrderAddAdminNotes(order.id, values.adminNotes);

      message.success('管理员备注添加成功');
      setIsAdminNotesModalVisible(false);
      fetchOrderDetails();
    } catch (error) {
      console.error('添加管理员备注失败:', error);
      message.error('添加管理员备注失败');
    }
  });

  // Open modal for platform receive
  const handleOpenPlatformReceiveModal = useMemoizedFn(() => {
    if (!order) return;
    setIsPlatformReceiveModalVisible(true);
  });

  // Handle platform receive
  const handlePlatformReceive = useMemoizedFn(async () => {
    try {
      if (!order) return;

      await apiOrderPlatformReceive(order.id);

      message.success('平台收货操作成功');
      setIsPlatformReceiveModalVisible(false);
      fetchOrderDetails();
    } catch (error) {
      console.error('平台收货操作失败:', error);
      message.error('平台收货操作失败');
    }
  });

  // Open modal for platform ship
  const handleOpenPlatformShipModal = useMemoizedFn(() => {
    if (!order) return;
    platformShipForm.resetFields();
    setIsPlatformShipModalVisible(true);
  });

  // Handle platform ship
  const handlePlatformShip = useMemoizedFn(async () => {
    try {
      if (!order) return;

      const values = await platformShipForm.validateFields();
      await apiOrderPlatformShip(order.id, {
        trackingNumber: values.trackingNumber,
        shippingCarrier: values.shippingCarrier
      });

      message.success('平台发货操作成功');
      setIsPlatformShipModalVisible(false);
      fetchOrderDetails();
    } catch (error) {
      console.error('平台发货操作失败:', error);
      message.error('平台发货操作失败');
    }
  });

  // Open modal for platform verify
  const handleOpenPlatformVerifyModal = useMemoizedFn(() => {
    if (!order) return;
    platformVerifyForm.resetFields();
    setIsPlatformVerifyModalVisible(true);
  });

  // Handle platform verify
  const handlePlatformVerify = useMemoizedFn(async () => {
    try {
      if (!order) return;

      const values = await platformVerifyForm.validateFields();
      await apiOrderPlatformVerify(order.id, {
        verificationNotes: values.verificationNotes
      });

      message.success('平台验证操作成功');
      setIsPlatformVerifyModalVisible(false);
      fetchOrderDetails();
    } catch (error) {
      console.error('平台验证操作失败:', error);
      message.error('平台验证操作失败');
    }
  });

  // Open modal for platform receive return
  const handleOpenPlatformReceiveReturnModal = useMemoizedFn(() => {
    if (!order) return;
    platformReceiveReturnForm.resetFields();
    setIsPlatformReceiveReturnModalVisible(true);
  });

  // Handle platform receive return
  const handlePlatformReceiveReturn = useMemoizedFn(async () => {
    try {
      if (!order) return;

      const values = await platformReceiveReturnForm.validateFields();
      await apiOrderPlatformReceiveReturn(order.id, {
        returnNotes: values.returnNotes
      });

      message.success('平台收货退货操作成功');
      setIsPlatformReceiveReturnModalVisible(false);
      fetchOrderDetails();
    } catch (error) {
      console.error('平台收货退货操作失败:', error);
      message.error('平台收货退货操作失败');
    }
  });

  // Go back to order list
  const handleGoBack = () => {
    navigate('/client/orders');
  };

  // Render action buttons based on order status
  const renderActionButtons = () => {
    if (!order) return null;

    const buttons = [
      <Button key="status" type="primary" onClick={handleOpenStatusModal}>更新订单状态</Button>,
      <Button key="payment" onClick={handleOpenPaymentStatusModal}>更新支付状态</Button>,
      <Button key="notes" onClick={handleOpenAdminNotesModal}>添加管理员备注</Button>
    ];

    // Add conditional buttons based on order status
    if (order.status === 'SHIPPING_TO_PLATFORM') {
      buttons.push(
        <Button key="receive" type="primary" onClick={handleOpenPlatformReceiveModal}>平台收货</Button>
      );
    }

    if (order.status === 'PLATFORM_VERIFICATION') {
      buttons.push(
        <Button key="verify" type="primary" onClick={handleOpenPlatformVerifyModal}>平台验证</Button>
      );
    }

    if (order.status === 'PENDING_PLATFORM_SHIPPING') {
      buttons.push(
        <Button key="ship" type="primary" onClick={handleOpenPlatformShipModal}>平台发货</Button>
      );
    }

    if (order.status === 'RETURNING_TO_PLATFORM') {
      buttons.push(
        <Button key="receiveReturn" type="primary" onClick={handleOpenPlatformReceiveReturnModal}>平台收货退货</Button>
      );
    }

    return (
      <Card title="订单操作" style={{ marginBottom: 16 }}>
        <Space wrap>
          {buttons}
        </Space>
      </Card>
    );
  };

  return (
    <div>
      <Card
        title={
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleGoBack}>返回</Button>
            <span>订单详情</span>
          </Space>
        }
        loading={loading}
      >
        {order && (
          <>
            {renderActionButtons()}

            <Card title="基本信息" style={{ marginBottom: 16 }}>
              <Descriptions bordered column={2}>
                <Descriptions.Item label="订单号" span={2}>{order.orderNumber}</Descriptions.Item>
                <Descriptions.Item label="订单状态">
                  <Tag color={ORDER_STATUS_COLORS[order.status] || 'default'}>
                    {ORDER_STATUS_OPTIONS.find(option => option.value === order.status)?.label || order.status}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="支付状态">
                  <Tag color={PAYMENT_STATUS_COLORS[order.paymentStatus] || 'default'}>
                    {PAYMENT_STATUS_OPTIONS.find(option => option.value === order.paymentStatus)?.label || order.paymentStatus}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="创建时间" span={2}>{order.createdAt}</Descriptions.Item>
                <Descriptions.Item label="更新时间" span={2}>{order.updatedAt}</Descriptions.Item>
              </Descriptions>
            </Card>

            <Card title="用户信息" style={{ marginBottom: 16 }}>
              <Descriptions bordered column={2}>
                <Descriptions.Item label="买家" span={2}>
                  <ClientUserObj user={order.buyerInfo} />
                </Descriptions.Item>
                <Descriptions.Item label="卖家" span={2}>
                  <ClientUserObj user={order.sellerInfo} />
                </Descriptions.Item>
              </Descriptions>
            </Card>

            <Card title="商品信息" style={{ marginBottom: 16 }}>
              <Descriptions bordered column={2}>
                <Descriptions.Item label="产品名称" span={2}>{order.productName}</Descriptions.Item>
                <Descriptions.Item label="产品变体">{order.variantName || '无'}</Descriptions.Item>
                <Descriptions.Item label="SKU">{order.sku || '无'}</Descriptions.Item>
                <Descriptions.Item label="数量">{order.quantity}</Descriptions.Item>
                <Descriptions.Item label="单价">{`${order.unitPrice} ${order.currencyCode}`}</Descriptions.Item>
              </Descriptions>
            </Card>

            <Card title="金额信息" style={{ marginBottom: 16 }}>
              <Descriptions bordered column={2}>
                <Descriptions.Item label="总金额">{`${order.totalAmount} ${order.currencyCode}`}</Descriptions.Item>
                <Descriptions.Item label="原始金额">{`${order.originalAmount} ${order.currencyCode}`}</Descriptions.Item>
                <Descriptions.Item label="折扣金额">{`${order.discountAmount || 0} ${order.currencyCode}`}</Descriptions.Item>
                <Descriptions.Item label="税费">{`${order.taxAmount || 0} ${order.currencyCode}`}</Descriptions.Item>
                <Descriptions.Item label="运费">{`${order.shippingAmount || 0} ${order.currencyCode}`}</Descriptions.Item>
                <Descriptions.Item label="平台费用">{`${order.platformFeeAmount || 0} ${order.currencyCode}`}</Descriptions.Item>
                <Descriptions.Item label="卖家净收入">{`${order.netSellerAmount || 0} ${order.currencyCode}`}</Descriptions.Item>
                <Descriptions.Item label="支付方式">{order.paymentMethod || '未指定'}</Descriptions.Item>
              </Descriptions>
            </Card>

            <Card title="物流信息" style={{ marginBottom: 16 }}>
              <Descriptions bordered column={2}>
                <Descriptions.Item label="物流方式" span={2}>{order.shippingMethod || '未指定'}</Descriptions.Item>
                <Descriptions.Item label="卖家发货到平台运单号" span={2}>{order.sellerToPlatformTrackingNumber || '未指定'}</Descriptions.Item>
                <Descriptions.Item label="卖家发货到平台物流公司" span={2}>{order.sellerToPlatformShippingCarrier || '未指定'}</Descriptions.Item>
                <Descriptions.Item label="平台发货到买家运单号" span={2}>{order.trackingNumber || '未指定'}</Descriptions.Item>
                <Descriptions.Item label="平台发货到买家物流公司" span={2}>{order.shippingCarrier || '未指定'}</Descriptions.Item>
              </Descriptions>
            </Card>

            <Card title="备注信息" style={{ marginBottom: 16 }}>
              <Descriptions bordered column={2}>
                <Descriptions.Item label="客户备注" span={2}>{order.customerNotes || '无'}</Descriptions.Item>
                <Descriptions.Item label="管理员备注" span={2}>{order.adminNotes || '无'}</Descriptions.Item>
                <Descriptions.Item label="平台验证备注" span={2}>{order.verificationNotes || '无'}</Descriptions.Item>
                {order.returnReason && <Descriptions.Item label="退货原因" span={2}>{order.returnReason}</Descriptions.Item>}
                {order.returnNotes && <Descriptions.Item label="退货备注" span={2}>{order.returnNotes}</Descriptions.Item>}
              </Descriptions>
            </Card>

            {order.history && order.history.length > 0 && (
              <Card title="订单历史" style={{ marginBottom: 16 }}>
                {order.history.map((historyItem, index) => (
                  <div key={historyItem.id} style={{ marginBottom: 8 }}>
                    <p>
                      <Tag color="blue">{historyItem.createdTime}</Tag>
                      <Tag color="green">{historyItem.userName || '系统'}</Tag>
                      <span>将订单状态从</span>
                      <Tag color={ORDER_STATUS_COLORS[historyItem.statusFrom] || 'default'}>
                        {ORDER_STATUS_OPTIONS.find(option => option.value === historyItem.statusFrom)?.label || historyItem.statusFrom}
                      </Tag>
                      <span>更改为</span>
                      <Tag color={ORDER_STATUS_COLORS[historyItem.statusTo] || 'default'}>
                        {ORDER_STATUS_OPTIONS.find(option => option.value === historyItem.statusTo)?.label || historyItem.statusTo}
                      </Tag>
                    </p>
                    {historyItem.comment && <p>备注: {historyItem.comment}</p>}
                    {index < order.history.length - 1 && <Divider style={{ margin: '8px 0' }} />}
                  </div>
                ))}
              </Card>
            )}
          </>
        )}
      </Card>

      {/* Update Order Status Modal */}
      <Modal
        title="更新订单状态"
        open={isStatusModalVisible}
        onOk={handleUpdateStatus}
        onCancel={() => setIsStatusModalVisible(false)}
      >
        <Form
          form={statusForm}
          layout="vertical"
        >
          <Form.Item
            name="status"
            label="订单状态"
            rules={[{ required: true, message: '请选择订单状态' }]}
          >
            <Select
              placeholder="请选择订单状态"
              options={ORDER_STATUS_OPTIONS}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Update Payment Status Modal */}
      <Modal
        title="更新支付状态"
        open={isPaymentStatusModalVisible}
        onOk={handleUpdatePaymentStatus}
        onCancel={() => setIsPaymentStatusModalVisible(false)}
      >
        <Form
          form={paymentStatusForm}
          layout="vertical"
        >
          <Form.Item
            name="paymentStatus"
            label="支付状态"
            rules={[{ required: true, message: '请选择支付状态' }]}
          >
            <Select
              placeholder="请选择支付状态"
              options={PAYMENT_STATUS_OPTIONS}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Add Admin Notes Modal */}
      <Modal
        title="添加管理员备注"
        open={isAdminNotesModalVisible}
        onOk={handleAddAdminNotes}
        onCancel={() => setIsAdminNotesModalVisible(false)}
      >
        <Form
          form={adminNotesForm}
          layout="vertical"
        >
          <Form.Item
            name="adminNotes"
            label="管理员备注"
            rules={[{ required: true, message: '请输入管理员备注' }]}
          >
            <Input.TextArea rows={4} placeholder="请输入管理员备注" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Platform Receive Modal */}
      <Modal
        title="平台收货"
        open={isPlatformReceiveModalVisible}
        onOk={handlePlatformReceive}
        onCancel={() => setIsPlatformReceiveModalVisible(false)}
      >
        <p>确认已收到卖家发来的货物？</p>
        {order && (
          <Descriptions column={1} size="small">
            <Descriptions.Item label="订单号">{order.orderNumber}</Descriptions.Item>
            <Descriptions.Item label="商品">{order.productName}</Descriptions.Item>
            <Descriptions.Item label="卖家运单号">{order.sellerToPlatformTrackingNumber || '无'}</Descriptions.Item>
            <Descriptions.Item label="卖家物流公司">{order.sellerToPlatformShippingCarrier || '无'}</Descriptions.Item>
          </Descriptions>
        )}
      </Modal>

      {/* Platform Ship Modal */}
      <Modal
        title="平台发货"
        open={isPlatformShipModalVisible}
        onOk={handlePlatformShip}
        onCancel={() => setIsPlatformShipModalVisible(false)}
      >
        <Form
          form={platformShipForm}
          layout="vertical"
        >
          {order && (
            <Descriptions column={1} size="small" style={{ marginBottom: 16 }}>
              <Descriptions.Item label="订单号">{order.orderNumber}</Descriptions.Item>
              <Descriptions.Item label="商品">{order.productName}</Descriptions.Item>
            </Descriptions>
          )}
          <Form.Item
            name="trackingNumber"
            label="运单号"
            rules={[{ required: true, message: '请输入运单号' }]}
          >
            <Input placeholder="请输入运单号" />
          </Form.Item>
          <Form.Item
            name="shippingCarrier"
            label="物流公司"
            rules={[{ required: true, message: '请输入物流公司' }]}
          >
            <Input placeholder="请输入物流公司" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Platform Verify Modal */}
      <Modal
        title="平台验证"
        open={isPlatformVerifyModalVisible}
        onOk={handlePlatformVerify}
        onCancel={() => setIsPlatformVerifyModalVisible(false)}
      >
        <Form
          form={platformVerifyForm}
          layout="vertical"
        >
          {order && (
            <Descriptions column={1} size="small" style={{ marginBottom: 16 }}>
              <Descriptions.Item label="订单号">{order.orderNumber}</Descriptions.Item>
              <Descriptions.Item label="商品">{order.productName}</Descriptions.Item>
            </Descriptions>
          )}
          <Form.Item
            name="verificationNotes"
            label="验证备注"
            rules={[{ required: true, message: '请输入验证备注' }]}
          >
            <Input.TextArea rows={4} placeholder="请输入验证备注" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Platform Receive Return Modal */}
      <Modal
        title="平台收货退货"
        open={isPlatformReceiveReturnModalVisible}
        onOk={handlePlatformReceiveReturn}
        onCancel={() => setIsPlatformReceiveReturnModalVisible(false)}
      >
        <Form
          form={platformReceiveReturnForm}
          layout="vertical"
        >
          {order && (
            <Descriptions column={1} size="small" style={{ marginBottom: 16 }}>
              <Descriptions.Item label="订单号">{order.orderNumber}</Descriptions.Item>
              <Descriptions.Item label="商品">{order.productName}</Descriptions.Item>
              <Descriptions.Item label="退货原因">{order.returnReason || '无'}</Descriptions.Item>
            </Descriptions>
          )}
          <Form.Item
            name="returnNotes"
            label="退货备注"
            rules={[{ required: true, message: '请输入退货备注' }]}
          >
            <Input.TextArea rows={4} placeholder="请输入退货备注" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

// Add auth property to the component
OrderDetail.auth = [
  "apiOrderGetOrderDetails",
  "apiOrderUpdateOrderStatus",
  "apiOrderUpdatePaymentStatus",
  "apiOrderAddAdminNotes",
  "apiOrderPlatformReceive",
  "apiOrderPlatformShip",
  "apiOrderPlatformVerify",
  "apiOrderPlatformReceiveReturn"
];

export default OrderDetail;

OrderDetail.auth = ['apiOrderGetOrderDetails', 'apiOrderUpdateOrderStatus', 'apiOrderUpdatePaymentStatus', 'apiOrderAddAdminNotes', 'apiOrderPlatformReceive', 'apiOrderPlatformShip', 'apiOrderPlatformVerify', 'apiOrderPlatformReceiveReturn'];
