declare interface Order {
  id: number;
  orderNumber: string;
  buyerId: number;
  sellerId: number;
  productId: number;
  productName: string;
  variantId?: number;
  variantName?: string;
  sku?: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  originalAmount: number;
  discountAmount?: number;
  taxAmount?: number;
  shippingAmount?: number;
  platformFeeAmount?: number;
  netSellerAmount?: number;
  currencyCode: string;
  status: string; // PENDING_PAYMENT, PAID, PROCESSING, SHIPPED, DELIVERED, COMPLETED, CANCELLED, REFUNDED, DISPUTED, PENDING_APPROVAL, ON_HOLD
  paymentStatus: string; // PENDING, PROCESSING, COMPLETED, FAILED, REFUNDED, DISPUTED, CA<PERSON><PERSON>LED, VERIFICATION_REQUIRED
  paymentMethod?: string;
  paymentId?: string;
  shippingMethod?: string;
  trackingNumber?: string;
  shippingCarrier?: string;
  sellerToPlatformTrackingNumber?: string;
  sellerToPlatformShippingCarrier?: string;
  customerNotes?: string;
  adminNotes?: string;
  verificationNotes?: string;
  returnReason?: string;
  returnNotes?: string;
  createdAt: string;
  updatedAt: string;
  buyerInfo: TClientUser;
  sellerInfo: TClientUser;
}

declare interface OrderBo {
  id: number;
  orderNumber: string;
  orderType: string;
  relatedOrderId: number;
  status: string;
  totalAmount: number;
  originalAmount: number;
  discountAmount: number;
  taxAmount: number;
  shippingAmount: number;
  currencyCode: string;
  paymentMethod: string;
  paymentTransactionId: string;
  paymentStatus: string;
  shippingMethod: string;
  trackingNumber: string;
  shippingCarrier: string;
  sellerToPlatformTrackingNumber?: string;
  sellerToPlatformShippingCarrier?: string;
  shippingAddressId: number;
  billingAddressId: number;
  customerNotes: string;
  adminNotes: string;
  verificationNotes?: string;
  returnReason?: string;
  returnNotes?: string;
  isPriceNegotiable: boolean;
  negotiationStatus: string;
  lastNegotiationPrice: number;
  lastNegotiationTime: string;
  negotiationExpirationTime: string;
  createdAt: string;
  updatedAt: string;
  productId: number;
  productVariantId: number;
  productName: string;
  variantName: string;
  sku: string;
  quantity: number;
  unitPrice: number;
  imageUrl: string;
  history: OrderHistoryBo[];
  negotiations: OrderNegotiationBo[];
  priceQuoteId: number;
  platformFeeAmount: number;
  platformFeePercentage: number;
  netSellerAmount: number;
  statusText: string;
  paymentStatusText: string;
  negotiationStatusText: string;
  relatedOrder: OrderBo;
  isBuyer: boolean;
}

declare interface OrderHistoryBo {
  id: number;
  orderId: number;
  statusFrom: string;
  statusTo: string;
  action: string;
  userId: number;
  userType: string;
  comment: string;
  additionalData: string;
  createdTime: string;
  statusFromText: string;
  statusToText: string;
  actionText: string;
  userName: string;
}

declare interface OrderNegotiationBo {
  id: number;
  orderId: number;
  userId: number;
  userType: string;
  proposedPrice: number;
  originalPrice: number;
  status: string;
  message: string;
  responseMessage: string;
  responseUserId: number;
  responseTime: string;
  expirationTime: string;
  createdTime: string;
  statusText: string;
  userName: string;
  responseUserName: string;
  priceDifference: number;
  percentageChange: number;
  isExpired: boolean;
  canRespond: boolean;
}
