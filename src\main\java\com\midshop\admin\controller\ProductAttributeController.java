package com.midshop.admin.controller;

import com.midshop.admin.dto.ProductAttributeBo;
import com.midshop.admin.dto.TPageListRes;
import com.midshop.admin.dto.request.ProductAttributePageQuery;
import com.midshop.admin.service.ProductAttributeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/product-attributes")
@Tag(name = "Product Attribute Management")
public class ProductAttributeController {

    @Autowired
    private ProductAttributeService productAttributeService;

    @GetMapping("/{id}")
    @Operation(summary = "Get product attribute by ID")
    public ProductAttributeBo getAttributeById(@PathVariable Long id) {
        return productAttributeService.getAttributeById(id);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update product attribute")
    public ProductAttributeBo updateAttribute(@PathVariable Long id, @RequestBody ProductAttributeBo attributeBo) {
        return productAttributeService.updateAttribute(id, attributeBo);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete product attribute")
    public void deleteAttribute(@PathVariable Long id) {
        productAttributeService.deleteAttribute(id);
    }

    @PutMapping("/{id}/active/{isActive}")
    @Operation(summary = "Set product attribute active status")
    public ProductAttributeBo setAttributeActive(@PathVariable Long id, @PathVariable Boolean isActive) {
        return productAttributeService.setAttributeActive(id, isActive);
    }

    @PostMapping
    @Operation(summary = "Create product attribute")
    public ProductAttributeBo createAttribute(@RequestBody ProductAttributeBo attributeBo) {
        return productAttributeService.createAttribute(attributeBo);
    }

    @PostMapping("/page")
    @Operation(summary = "Get paginated list of product attributes")
    public TPageListRes<ProductAttributeBo> getAttributesPage(@RequestBody ProductAttributePageQuery query) {
        return productAttributeService.getAttributesPage(query);
    }
}
