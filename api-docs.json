{"openapi": "3.1.0", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://localhost:8888", "description": "Generated server url"}], "paths": {"/user/i18n": {"put": {"tags": ["User Management"], "summary": "Update user internationalization settings (region, language, currency)", "operationId": "updateI18nSettings", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/I18nSettingsRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientUserBo"}}}}}}}, "/admin/users/{id}": {"get": {"tags": ["Admin User Management"], "summary": "Get admin user by ID", "operationId": "getUserById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminUserBo"}}}}}}, "put": {"tags": ["Admin User Management"], "summary": "Update an admin user", "operationId": "updateUser", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAdminUserRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminUserBo"}}}}}}}, "/admin/roles/{id}": {"get": {"tags": ["Admin Role Management"], "summary": "Get admin role by ID", "operationId": "getRoleById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminRoleBo"}}}}}}, "put": {"tags": ["Admin Role Management"], "summary": "Update an admin role", "operationId": "updateRole", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAdminRoleRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminRoleBo"}}}}}}}, "/admin/regions/{id}": {"get": {"tags": ["Admin Region Management"], "summary": "Get a region by its ID", "operationId": "getRegionById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminRegionBo"}}}}}}, "put": {"tags": ["Admin Region Management"], "summary": "Update a region", "operationId": "updateRegion", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminRegionBo"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminRegionBo"}}}}}}, "delete": {"tags": ["Admin Region Management"], "summary": "Delete a region", "operationId": "deleteRegion", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK"}}}}, "/admin/permissions/{id}": {"get": {"tags": ["Admin Permission Management"], "summary": "Get admin permission by ID", "operationId": "getPermissionById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminPermissionBo"}}}}}}, "put": {"tags": ["Admin Permission Management"], "summary": "Update an admin permission", "operationId": "updatePermission", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAdminPermissionRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminPermissionBo"}}}}}}}, "/admin/languages/{code}": {"get": {"tags": ["Admin Language Management"], "summary": "Get a language by its code", "operationId": "getLanguageByCode", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminLanguageBo"}}}}}}, "put": {"tags": ["Admin Language Management"], "summary": "Update an existing language", "operationId": "updateLanguage", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LanguageBo"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminLanguageBo"}}}}}}}, "/admin/languages/{code}/set-default": {"put": {"tags": ["Admin Language Management"], "summary": "Set a language as the default language", "operationId": "setDefaultLanguage", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminLanguageBo"}}}}}}}, "/admin/languages/{code}/set-active": {"put": {"tags": ["Admin Language Management"], "summary": "Activate or deactivate a language", "operationId": "setLanguageActive", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "active", "in": "query", "required": true, "schema": {"type": "boolean"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminLanguageBo"}}}}}}}, "/admin/currencies/{code}": {"get": {"tags": ["Admin Currency Management"], "summary": "Get a currency by its code", "operationId": "getCurrencyByCode", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminCurrencyBo"}}}}}}, "put": {"tags": ["Admin Currency Management"], "summary": "Update an existing currency", "operationId": "updateCurrency", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyBo"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminCurrencyBo"}}}}}}}, "/admin/currencies/{code}/set-default": {"put": {"tags": ["Admin Currency Management"], "summary": "Set a currency as the default currency", "operationId": "setDefaultCurrency", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminCurrencyBo"}}}}}}}, "/admin/currencies/{code}/set-active": {"put": {"tags": ["Admin Currency Management"], "summary": "Activate or deactivate a currency", "operationId": "setCurrencyActive", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "active", "in": "query", "required": true, "schema": {"type": "boolean"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminCurrencyBo"}}}}}}}, "/admin/currencies/exchange-rates": {"put": {"tags": ["Admin Currency Management"], "summary": "Update an exchange rate", "operationId": "updateExchangeRate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExchangeRateBo"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExchangeRateBo"}}}}}}}, "/security/reset-password": {"post": {"tags": ["Security Management"], "summary": "Reset password with token", "operationId": "resetPassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientUserBo"}}}}}}}, "/security/resend-verification": {"post": {"tags": ["Security Management"], "summary": "Resend verification email", "operationId": "resendVerificationEmail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK"}}}}, "/security/register": {"post": {"tags": ["Security Management"], "summary": "Register a new user", "operationId": "register", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegistrationRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "201": {"description": "Created", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientUserBo"}}}}}}}, "/security/logout": {"post": {"tags": ["Security Management"], "summary": "Logout current user", "operationId": "logout", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK"}}}}, "/security/login/email": {"post": {"tags": ["Security Management"], "summary": "Login with email and password", "operationId": "loginByEmail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailLoginRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SessionInfoLongClientUserBo"}}}}}}}, "/security/forgot-password": {"post": {"tags": ["Security Management"], "summary": "Request password reset", "operationId": "requestPasswordReset", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK"}}}}, "/currencies/{baseCurrencyCode}/update-rates-from-api": {"post": {"tags": ["Client Currency API"], "summary": "Update exchange rates from external API for a base currency", "operationId": "updateExchangeRatesFromExternalApi", "parameters": [{"name": "baseCurrencyCode", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/currencies/update-all-rates": {"post": {"tags": ["Client Currency API"], "summary": "Update all exchange rates from the external API", "operationId": "updateAllExchangeRates", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/currencies/create-from-api": {"post": {"tags": ["Client Currency API"], "summary": "Create currencies from the external API", "operationId": "createCurrenciesFromExternalApi", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/admin/users": {"get": {"tags": ["Admin User Management"], "summary": "Get all admin users", "operationId": "getAllUsers", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AdminUserBo"}}}}}}}, "post": {"tags": ["Admin User Management"], "summary": "Create a new admin user", "operationId": "createUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAdminUserRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminUserBo"}}}}}}}, "/admin/users/{userId}/roles/{roleId}": {"post": {"tags": ["Admin User Management"], "summary": "Add a role to admin user", "operationId": "addRoleToUser", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "roleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminUserBo"}}}}}}, "delete": {"tags": ["Admin User Management"], "summary": "Remove a role from admin user", "operationId": "removeRoleFromUser", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "roleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminUserBo"}}}}}}}, "/admin/users/{id}/roles": {"post": {"tags": ["Admin User Management"], "summary": "Assign roles to admin user", "operationId": "assignRolesToUser", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}, "uniqueItems": true}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminUserBo"}}}}}}}, "/admin/users/page": {"post": {"tags": ["Admin User Management"], "summary": "Get admin users with pagination", "operationId": "getUsers", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageQuery"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageResultAdminUserBo"}}}}}}}, "/admin/translations/update": {"post": {"tags": ["Admin Translation Management"], "summary": "Update or create translations for a specific key across multiple languages", "operationId": "updateTranslationsByKey", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTranslationsByKeyRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TranslationBo"}}}}}}}}, "/admin/translations/page": {"post": {"tags": ["Admin Translation Management"], "summary": "Get all translations with pagination", "operationId": "getTranslationsPage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageQuery"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageResultTranslationBo"}}}}}}}, "/admin/translations/create": {"post": {"tags": ["Admin Translation Management"], "summary": "Create a new translation key with translations for multiple languages", "operationId": "createTranslationKey", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTranslationsByKeyRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TranslationBo"}}}}}}}}, "/admin/session": {"post": {"tags": ["管理员安全接口"], "summary": "获取当前登录用户", "operationId": "getSessionUser", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SessionInfoLongAdminUserBo"}}}}}}}, "/admin/roles": {"get": {"tags": ["Admin Role Management"], "summary": "Get all admin roles", "operationId": "getAllRoles", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AdminRoleBo"}}}}}}}, "post": {"tags": ["Admin Role Management"], "summary": "Create a new admin role", "operationId": "createRole", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAdminRoleRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminRoleBo"}}}}}}}, "/admin/roles/{roleId}/permissions/{permissionId}": {"post": {"tags": ["Admin Role Management"], "summary": "Add a permission to admin role", "operationId": "addPermissionToRole", "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "permissionId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminRoleBo"}}}}}}, "delete": {"tags": ["Admin Role Management"], "summary": "Remove a permission from admin role", "operationId": "removePermissionFromRole", "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "permissionId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminRoleBo"}}}}}}}, "/admin/roles/{id}/permissions": {"get": {"tags": ["Admin Role Management"], "summary": "Get admin role permissions by ID", "operationId": "getRolePermissions", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}, "post": {"tags": ["Admin Role Management"], "summary": "Assign permissions to admin role", "operationId": "assignPermissionsToRole", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}, "uniqueItems": true}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminRoleBo"}}}}}}}, "/admin/roles/page": {"post": {"tags": ["Admin Role Management"], "summary": "Get admin roles with pagination", "operationId": "getRoles", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageQuery"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageResultAdminRoleBo"}}}}}}}, "/admin/regions": {"post": {"tags": ["Admin Region Management"], "summary": "Create a new region", "operationId": "createRegion", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminRegionBo"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminRegionBo"}}}}}}}, "/admin/regions/page": {"post": {"tags": ["Admin Region Management"], "summary": "Get all regions with pagination", "operationId": "getRegions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageQuery"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageResultAdminRegionBo"}}}}}}}, "/admin/permissions": {"get": {"tags": ["Admin Permission Management"], "summary": "Get all admin permissions", "operationId": "getAllPermissions", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AdminPermissionBo"}}}}}}}, "post": {"tags": ["Admin Permission Management"], "summary": "Create a new admin permission", "operationId": "createPermission", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAdminPermissionRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminPermissionBo"}}}}}}}, "/admin/permissions/page": {"post": {"tags": ["Admin Permission Management"], "summary": "Get admin permissions with pagination", "operationId": "getPermissions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageQuery"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageResultAdminPermissionBo"}}}}}}}, "/admin/logout": {"post": {"tags": ["管理员安全接口"], "summary": "账号登出", "operationId": "logout_1", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK"}}}}, "/admin/login": {"post": {"tags": ["管理员安全接口"], "summary": "账号登录", "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsernameLoginRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SessionInfoLongAdminUserBo"}}}}}}}, "/admin/languages": {"post": {"tags": ["Admin Language Management"], "summary": "Create a new language", "operationId": "createLanguage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LanguageBo"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminLanguageBo"}}}}}}}, "/admin/languages/translations": {"post": {"tags": ["Admin Language Management"], "summary": "Create or update a translation", "operationId": "createOrUpdateTranslation", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TranslationBo"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TranslationBo"}}}}}}}, "/admin/languages/translations/batch": {"post": {"tags": ["Admin Language Management"], "summary": "Batch create or update translations", "operationId": "batchCreateOrUpdateTranslations", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TranslationBo"}}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TranslationBo"}}}}}}}}, "/admin/languages/page": {"post": {"tags": ["Admin Language Management"], "summary": "Get all languages with pagination", "operationId": "getLanguages", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageQuery"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageResultAdminLanguageBo"}}}}}}}, "/admin/currencies": {"post": {"tags": ["Admin Currency Management"], "summary": "Create a new currency", "operationId": "createCurrency", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyBo"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminCurrencyBo"}}}}}}}, "/admin/currencies/{code}/update-rates-from-api": {"post": {"tags": ["Admin Currency Management"], "summary": "Update exchange rates from the Exchange Rate API for a specific currency", "operationId": "updateExchangeRatesFromExternalApi_1", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/admin/currencies/update-all-rates": {"post": {"tags": ["Admin Currency Management"], "summary": "Update all exchange rates from the Exchange Rate API", "operationId": "updateAllExchangeRates_1", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/admin/currencies/page": {"post": {"tags": ["Admin Currency Management"], "summary": "Get all currencies with pagination", "operationId": "getCurrencies", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageQuery"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageResultAdminCurrencyBo"}}}}}}}, "/admin/currencies/create-from-api": {"post": {"tags": ["Admin Currency Management"], "summary": "Create currencies from the Exchange Rate API", "operationId": "createCurrenciesFromExternalApi_1", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/admin/users/{id}/active": {"patch": {"tags": ["Admin User Management"], "summary": "Set admin user active status", "operationId": "setUserActive", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "active", "in": "query", "required": true, "schema": {"type": "boolean"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminUserBo"}}}}}}}, "/admin/roles/{id}/active": {"patch": {"tags": ["Admin Role Management"], "summary": "Set admin role active status", "operationId": "setRoleActive", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "active", "in": "query", "required": true, "schema": {"type": "boolean"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminRoleBo"}}}}}}}, "/admin/permissions/{id}/active": {"patch": {"tags": ["Admin Permission Management"], "summary": "Set admin permission active status", "operationId": "setPermissionActive", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "active", "in": "query", "required": true, "schema": {"type": "boolean"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminPermissionBo"}}}}}}}, "/security/verify-email": {"get": {"tags": ["Security Management"], "summary": "Verify email with token", "operationId": "verifyEmail", "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SessionInfoLongClientUserBo"}}}}}}}, "/security/session": {"get": {"tags": ["Security Management"], "summary": "Get current user information", "operationId": "getCurrentUser", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientUserBo"}}}}}}}, "/regions": {"get": {"tags": ["Client Region API"], "summary": "Get all active regions", "operationId": "getAllActiveRegions", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RegionBo"}}}}}}}}, "/regions/{code}": {"get": {"tags": ["Client Region API"], "summary": "Get region by code", "operationId": "getRegionByCode", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RegionBo"}}}}}}}, "/languages": {"get": {"tags": ["Client Language API"], "summary": "Get all active languages", "operationId": "getActiveLanguages", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LanguageBo"}}}}}}}}, "/languages/{languageCode}/translations": {"get": {"tags": ["Client Language API"], "summary": "Get all translations for a specific language", "operationId": "getTranslations", "parameters": [{"name": "languageCode", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}}}, "/languages/{languageCode}/translations/{module}": {"get": {"tags": ["Client Language API"], "summary": "Get translations for a specific language and module", "operationId": "getTranslationsForModule", "parameters": [{"name": "languageCode", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "module", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}}}, "/languages/{languageCode}/translations/grouped": {"get": {"tags": ["Client Language API"], "summary": "Get translations grouped by module for a specific language", "operationId": "getTranslationsGroupedByModule", "parameters": [{"name": "languageCode", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"type": "string"}}}}}}}}}, "/languages/default": {"get": {"tags": ["Client Language API"], "summary": "Get the default language", "operationId": "getDefaultLanguage", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/LanguageBo"}}}}}}}, "/currencies": {"get": {"tags": ["Client Currency API"], "summary": "Get all active currencies", "operationId": "getAllActiveCurrencies", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CurrencyBo"}}}}}}}}, "/currencies/{code}/exchange-rates": {"get": {"tags": ["Client Currency API"], "summary": "Get all exchange rates for a specific currency", "operationId": "getExchangeRatesForCurrency", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ExchangeRateBo"}}}}}}}}, "/currencies/exchange-rates/{fromCurrency}/{toCurrency}": {"get": {"tags": ["Client Currency API"], "summary": "Get exchange rate between two currencies", "operationId": "getExchangeRate", "parameters": [{"name": "fromCurrency", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "to<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExchangeRateBo"}}}}}}}, "/currencies/default": {"get": {"tags": ["Client Currency API"], "summary": "Get the default currency", "operationId": "getDefaultCurrency", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CurrencyBo"}}}}}}}, "/currencies/convert": {"get": {"tags": ["Client Currency API"], "summary": "Convert an amount from one currency to another", "operationId": "convertCurrency", "parameters": [{"name": "amount", "in": "query", "required": true, "schema": {"type": "number"}}, {"name": "fromCurrency", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "to<PERSON><PERSON><PERSON><PERSON>", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "number"}}}}}}}, "/admin/translations/keys": {"get": {"tags": ["Admin Translation Management"], "summary": "Get all translation keys", "operationId": "getAllTranslationKeys", "parameters": [{"name": "module", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/admin/translations/by-key/{key}": {"get": {"tags": ["Admin Translation Management"], "summary": "Get translations for a specific key across all languages", "operationId": "getTranslationsByKey", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TranslationsByKeyDto"}}}}}}}, "/admin/roles/code/{code}": {"get": {"tags": ["Admin Role Management"], "summary": "Get admin role by code", "operationId": "getRoleByCode", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminRoleBo"}}}}}}}, "/admin/permissions/code/{code}": {"get": {"tags": ["Admin Permission Management"], "summary": "Get admin permission by code", "operationId": "getPermissionByCode", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AdminPermissionBo"}}}}}}}, "/admin/currencies/{code}/exchange-rates": {"get": {"tags": ["Admin Currency Management"], "summary": "Get exchange rates for a specific currency", "operationId": "getExchangeRatesForCurrency_1", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ExchangeRateBo"}}}}}}}}, "/admin/currencies/api/supported": {"get": {"tags": ["Admin Currency Management"], "summary": "Get supported currencies from the Exchange Rate API", "operationId": "getSupportedCurrencies", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}}}, "/admin/languages/translations/{id}": {"delete": {"tags": ["Admin Language Management"], "summary": "Delete a translation", "operationId": "deleteTranslation", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultObject"}}}}, "200": {"description": "OK"}}}}}, "components": {"schemas": {"ResultObject": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}}}, "I18nSettingsRequest": {"type": "object", "properties": {"region": {"type": "string"}, "language": {"type": "string"}, "currency": {"type": "string"}}}, "ClientUserBo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "region": {"type": "string"}, "language": {"type": "string"}, "currency": {"type": "string"}, "email": {"type": "string"}, "isActive": {"type": "boolean"}, "isEmailVerified": {"type": "boolean"}, "createdAt": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "integer", "format": "int64"}, "permissions": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "roles": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "UpdateAdminUserRequest": {"type": "object", "properties": {"email": {"type": "string"}, "phone": {"type": "string"}, "nickname": {"type": "string"}, "avatar": {"type": "string"}, "password": {"type": "string", "maxLength": 2147483647, "minLength": 6}}}, "AdminUserBo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "username": {"type": "string"}, "realname": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "password": {"type": "string"}, "avatar": {"type": "string"}, "nickname": {"type": "string"}, "lastLoginAt": {"type": "string", "format": "date-time"}, "lastLoginIp": {"type": "string"}, "roles": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "permissions": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "CreateAdminRoleRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 100, "minLength": 0}, "code": {"type": "string", "maxLength": 50, "minLength": 0}, "description": {"type": "string"}, "permissionIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "uniqueItems": true}}, "required": ["code", "name"]}, "AdminPermissionBo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "AdminRoleBo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "isActive": {"type": "boolean"}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/AdminPermissionBo"}, "uniqueItems": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "AdminRegionBo": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "defaultLanguage": {"type": "string"}, "defaultCurrency": {"type": "string"}, "timeZone": {"type": "string"}, "phoneCode": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "integer", "format": "int64"}, "createdBy": {"type": "integer", "format": "int64"}, "updatedBy": {"type": "integer", "format": "int64"}}}, "CreateAdminPermissionRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 100, "minLength": 0}, "code": {"type": "string", "maxLength": 50, "minLength": 0}, "description": {"type": "string"}}, "required": ["code", "name"]}, "LanguageBo": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "nativeName": {"type": "string"}, "isDefault": {"type": "boolean"}}}, "AdminLanguageBo": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "nativeName": {"type": "string"}, "isDefault": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "isActive": {"type": "boolean"}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "createdAt": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "integer", "format": "int64"}}}, "CurrencyBo": {"type": "object", "properties": {"code": {"type": "string"}, "symbol": {"type": "string"}, "name": {"type": "string"}, "number": {"type": "integer", "format": "int32"}, "isDefault": {"type": "boolean"}}}, "AdminCurrencyBo": {"type": "object", "properties": {"code": {"type": "string"}, "symbol": {"type": "string"}, "name": {"type": "string"}, "number": {"type": "integer", "format": "int32"}, "isDefault": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "isActive": {"type": "boolean"}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "createdAt": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "integer", "format": "int64"}}}, "ExchangeRateBo": {"type": "object", "properties": {"fromCurrency": {"type": "string"}, "toCurrency": {"type": "string"}, "rate": {"type": "number"}, "active": {"type": "boolean"}, "ruleId": {"type": "integer", "format": "int64"}}}, "ResetPasswordRequest": {"type": "object", "properties": {"token": {"type": "string"}, "newPassword": {"type": "string", "maxLength": 2147483647, "minLength": 6}}, "required": ["newPassword", "token"]}, "EmailRequest": {"type": "object", "properties": {"email": {"type": "string"}}, "required": ["email"]}, "RegistrationRequest": {"type": "object", "properties": {"firstName": {"type": "string", "maxLength": 50, "minLength": 2}, "lastName": {"type": "string", "maxLength": 50, "minLength": 2}, "email": {"type": "string"}, "password": {"type": "string", "maxLength": 2147483647, "minLength": 6}, "region": {"type": "string"}, "language": {"type": "string"}, "currency": {"type": "string"}}, "required": ["currency", "email", "firstName", "language", "lastName", "password", "region"]}, "EmailLoginRequest": {"type": "object", "properties": {"email": {"type": "string"}, "password": {"type": "string"}}, "required": ["email", "password"]}, "SessionInfoLongClientUserBo": {"type": "object", "properties": {"name": {"type": "string"}, "token": {"type": "string"}, "expiredAt": {"type": "string", "format": "date-time"}, "user": {"$ref": "#/components/schemas/ClientUserBo"}, "order": {"type": "integer", "format": "int64"}, "kicked": {"type": "boolean"}, "permissions": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "roles": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "userId": {"type": "integer", "format": "int64"}}}, "CreateAdminUserRequest": {"type": "object", "properties": {"username": {"type": "string", "maxLength": 50, "minLength": 3}, "password": {"type": "string", "maxLength": 2147483647, "minLength": 6}, "email": {"type": "string"}, "phone": {"type": "string"}, "nickname": {"type": "string"}, "roleIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "uniqueItems": true}}, "required": ["password", "username"]}, "PageQuery": {"type": "object", "properties": {"filters": {"type": "array", "items": {"$ref": "#/components/schemas/QueryFilter"}}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/QuerySort"}}, "distinct": {"type": "boolean"}, "distinctField": {"type": "string"}, "current": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}}}, "QueryFilter": {"type": "object", "properties": {"field": {"type": "string"}, "operator": {"type": "string"}, "value": {"type": "object"}}}, "QuerySort": {"type": "object", "properties": {"field": {"type": "string"}, "direction": {"type": "string"}}}, "PageResultAdminUserBo": {"type": "object", "properties": {"size": {"type": "integer", "format": "int32"}, "current": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/AdminUserBo"}}, "offset": {"type": "integer", "format": "int32"}}}, "UpdateTranslationsByKeyRequest": {"type": "object", "properties": {"key": {"type": "string"}, "module": {"type": "string"}, "description": {"type": "string"}, "translations": {"type": "object", "additionalProperties": {"type": "string"}}}}, "TranslationBo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "language": {"type": "string"}, "translationKey": {"type": "string"}, "value": {"type": "string"}, "isActive": {"type": "boolean"}, "module": {"type": "string"}, "description": {"type": "string"}}}, "PageResultTranslationBo": {"type": "object", "properties": {"size": {"type": "integer", "format": "int32"}, "current": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/TranslationBo"}}, "offset": {"type": "integer", "format": "int32"}}}, "SessionInfoLongAdminUserBo": {"type": "object", "properties": {"name": {"type": "string"}, "token": {"type": "string"}, "expiredAt": {"type": "string", "format": "date-time"}, "user": {"$ref": "#/components/schemas/AdminUserBo"}, "order": {"type": "integer", "format": "int64"}, "kicked": {"type": "boolean"}, "permissions": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "roles": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "userId": {"type": "integer", "format": "int64"}}}, "PageResultAdminRoleBo": {"type": "object", "properties": {"size": {"type": "integer", "format": "int32"}, "current": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/AdminRoleBo"}}, "offset": {"type": "integer", "format": "int32"}}}, "PageResultAdminRegionBo": {"type": "object", "properties": {"size": {"type": "integer", "format": "int32"}, "current": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/AdminRegionBo"}}, "offset": {"type": "integer", "format": "int32"}}}, "PageResultAdminPermissionBo": {"type": "object", "properties": {"size": {"type": "integer", "format": "int32"}, "current": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/AdminPermissionBo"}}, "offset": {"type": "integer", "format": "int32"}}}, "UsernameLoginRequest": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "required": ["password", "username"]}, "PageResultAdminLanguageBo": {"type": "object", "properties": {"size": {"type": "integer", "format": "int32"}, "current": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/AdminLanguageBo"}}, "offset": {"type": "integer", "format": "int32"}}}, "PageResultAdminCurrencyBo": {"type": "object", "properties": {"size": {"type": "integer", "format": "int32"}, "current": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/AdminCurrencyBo"}}, "offset": {"type": "integer", "format": "int32"}}}, "RegionBo": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "defaultLanguage": {"type": "string"}, "defaultCurrency": {"type": "string"}, "timeZone": {"type": "string"}, "phoneCode": {"type": "string"}}}, "TranslationsByKeyDto": {"type": "object", "properties": {"key": {"type": "string"}, "module": {"type": "string"}, "description": {"type": "string"}, "translations": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/TranslationBo"}}}}}}}