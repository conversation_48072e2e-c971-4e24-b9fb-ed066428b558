import React, { useRef } from 'react';
import { message, Space, Tag, Button } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { useNavigate } from 'react-router-dom';
import {
  apiOrderGetAllOrders
} from '@/apis/apis.api';
import { ToolsUtil } from '@/utils/tools';
import UserObj from '@/components/UserObj';
import ClientUserObj from '@/components/ClientUserObj';

// Order status options
const ORDER_STATUS_OPTIONS = [
  { label: '待付款', value: 'PENDING_PAYMENT' },
  { label: '已付款', value: 'PAID' },
  { label: '处理中', value: 'PROCESSING' },
  { label: '待卖家发货', value: 'PENDING_SELLER_SHIPPING' },
  { label: '运送至平台中', value: 'SHIPPING_TO_PLATFORM' },
  { label: '平台验证中', value: 'PLATFORM_VERIFICATION' },
  { label: '待平台发货', value: 'PENDING_PLATFORM_SHIPPING' },
  { label: '已发货', value: 'SHIPPED' },
  { label: '运送至买家中', value: 'SHIPPING_TO_BUYER' },
  { label: '已送达', value: 'DELIVERED' },
  { label: '已完成', value: 'COMPLETED' },
  { label: '已取消', value: 'CANCELLED' },
  { label: '已退款', value: 'REFUNDED' },
  { label: '有争议', value: 'DISPUTED' },
  { label: '待审批', value: 'PENDING_APPROVAL' },
  { label: '暂停', value: 'ON_HOLD' },
  { label: '退回平台中', value: 'RETURNING_TO_PLATFORM' },
  { label: '退回卖家中', value: 'RETURNING_TO_SELLER' }
];

// Payment status options
const PAYMENT_STATUS_OPTIONS = [
  { label: '待处理', value: 'PENDING' },
  { label: '处理中', value: 'PROCESSING' },
  { label: '已完成', value: 'COMPLETED' },
  { label: '失败', value: 'FAILED' },
  { label: '已退款', value: 'REFUNDED' },
  { label: '有争议', value: 'DISPUTED' },
  { label: '已取消', value: 'CANCELLED' },
  { label: '需要验证', value: 'VERIFICATION_REQUIRED' }
];

// Order status tag colors
const ORDER_STATUS_COLORS = {
  'PENDING_PAYMENT': 'orange',
  'PAID': 'green',
  'PROCESSING': 'blue',
  'PENDING_SELLER_SHIPPING': 'gold',
  'SHIPPING_TO_PLATFORM': 'purple',
  'PLATFORM_VERIFICATION': 'blue',
  'PENDING_PLATFORM_SHIPPING': 'gold',
  'SHIPPED': 'cyan',
  'SHIPPING_TO_BUYER': 'purple',
  'DELIVERED': 'geekblue',
  'COMPLETED': 'green',
  'CANCELLED': 'red',
  'REFUNDED': 'purple',
  'DISPUTED': 'magenta',
  'PENDING_APPROVAL': 'gold',
  'ON_HOLD': 'gray',
  'RETURNING_TO_PLATFORM': 'orange',
  'RETURNING_TO_SELLER': 'orange'
};

// Payment status tag colors
const PAYMENT_STATUS_COLORS = {
  'PENDING': 'orange',
  'PROCESSING': 'blue',
  'COMPLETED': 'green',
  'FAILED': 'red',
  'REFUNDED': 'purple',
  'DISPUTED': 'magenta',
  'CANCELLED': 'red',
  'VERIFICATION_REQUIRED': 'gold'
};

// Order Management Component
const OrderList: React.FC & { auth?: string[] } = () => {
  const tableRef = useRef<ActionType>();
  const navigate = useNavigate();

  // Navigate to order detail page
  const handleViewDetails = useMemoizedFn((orderId: number | string) => {
    navigate(`/client/orders/${orderId}`);
  });

  // Table columns
  const columns: ProColumns<Order>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      copyable: true,
      ellipsis: true,
    },
    {
      title: '买家ID',
      dataIndex: 'buyerId',
      key: 'buyerId',
      hideInSearch: true,
      render: (_text, record) => <ClientUserObj user={record.buyerInfo} />
    },
    {
      title: '卖家ID',
      dataIndex: 'sellerId',
      key: 'sellerId',
      hideInSearch: true,
      render: (_text, record) => <ClientUserObj user={record.sellerInfo} />
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      ellipsis: true,
    },
    {
      title: '总金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      hideInSearch: true,
      render: (_, record) => `${record.totalAmount} ${record.currencyCode}`,
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      key: 'status',
      valueEnum: ORDER_STATUS_OPTIONS.reduce((acc, option) => {
        acc[option.value] = { text: option.label };
        return acc;
      }, {} as Record<string, { text: string }>),
      render: (_, record) => (
        <Tag color={ORDER_STATUS_COLORS[record.status] || 'default'}>
          {ORDER_STATUS_OPTIONS.find(option => option.value === record.status)?.label || record.status}
        </Tag>
      ),
    },
    {
      title: '支付状态',
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      valueEnum: PAYMENT_STATUS_OPTIONS.reduce((acc, option) => {
        acc[option.value] = { text: option.label };
        return acc;
      }, {} as Record<string, { text: string }>),
      render: (_, record) => (
        <Tag color={PAYMENT_STATUS_COLORS[record.paymentStatus] || 'default'}>
          {PAYMENT_STATUS_OPTIONS.find(option => option.value === record.paymentStatus)?.label || record.paymentStatus}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
    },
    {
      title: '操作',
      key: 'actions',
      valueType: 'option',
      render: (_, record) => (
        <Space size="middle">
          <Button type="primary" icon={<EyeOutlined />} onClick={() => handleViewDetails(record.id)}>
            查看详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <ProTable<Order>
      actionRef={tableRef}
      headerTitle="订单管理"
      rowKey="id"
      columns={columns}
      search={{
        defaultCollapsed: false,
      }}
      request={async (params, sorter) => {
        const filters: any[] = [];

        // 添加搜索条件
        if (params.orderNumber) {
          filters.push({ field: 'orderNumber', operator: 'like', value: params.orderNumber });
        }
        if (params.productName) {
          filters.push({ field: 'productName', operator: 'like', value: params.productName });
        }
        if (params.status) {
          filters.push({ field: 'status', operator: 'eq', value: params.status });
        }
        if (params.paymentStatus) {
          filters.push({ field: 'paymentStatus', operator: 'eq', value: params.paymentStatus });
        }

        let sorts = ToolsUtil.tableSorterToPageListReqSort(sorter);

        // 添加默认排序，创建时间倒序
        if (!sorts || sorts.length === 0) {
          sorts = [{ field: 'createdAt', direction: 'desc' }];
        }

        try {
          const res = await apiOrderGetAllOrders({
            current: params.current - 1,
            size: params.pageSize,
            filters,
            sorts,
          });

          return {
            total: res.total || 0,
            data: res.records || [],
            success: true,
          };
        } catch (error) {
          console.error('获取订单列表失败:', error);
          message.error('获取订单列表失败');
          return {
            total: 0,
            data: [],
            success: false,
          };
        }
      }}
    />
  );
};

// Add auth property to the component
OrderList.auth = [
  "apiOrderGetAllOrders"
];

export default OrderList;

OrderList.auth = ['apiOrderGetAllOrders'];
