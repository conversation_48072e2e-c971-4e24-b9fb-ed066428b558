import http from '@/utils/http';

export function apiLoginByPwd(data: { username: string; password: string }) {
  return http.post<{
    user: TAdminUser;
  }>('/login', data);
}

export function apiMyUserInfoGet() {
  return http.get<{
    user: TAdminUser;
  }>('/session');
}


/** Admin User Management/根据ID获取管理员用户 */
export function apiGetUserById(id: string) {
  return http.get<TAdminUserBo>(`/users/${id}`);
}

/** Admin User Management/更新管理员用户 */
export function apiUpdateUser(id: string, data: TUpdateAdminUserRequest) {
  return http.put<TAdminUserBo>(`/users/${id}`, data);
}

/** Admin User Management/更新管理员用户密码 */
export function apiUpdatePassword(id: string, data: TUpdatePasswordRequest) {
  return http.put<TAdminUserBo>(`/users/${id}/password`, data);
}

/** Admin User Management/创建新的管理员用户 */
export function apiCreateUser(data: TCreateAdminUserRequest) {
  return http.post<TAdminUserBo>('/users', data);
}

/** Admin User Management/为管理员用户添加角色（通过角色ID） */
export function apiAddRoleToUser(userId: string, roleId: string) {
  return http.post<TAdminUserBo>(`/users/${userId}/roles/${roleId}`);
}

/** Admin User Management/从管理员用户中移除角色（通过角色ID） */
export function apiRemoveRoleFromUser(userId: string, roleId: string) {
  return http.delete<TAdminUserBo>(`/users/${userId}/roles/${roleId}`);
}

/** Admin User Management/为管理员用户添加角色（通过角色代码） */
export function apiAddRoleToUserByCode(userId: string, roleCode: string) {
  return http.post<TAdminUserBo>(`/users/${userId}/roles/code/${roleCode}`);
}

/** Admin User Management/从管理员用户中移除角色（通过角色代码） */
export function apiRemoveRoleFromUserByCode(userId: string, roleCode: string) {
  return http.delete<TAdminUserBo>(`/users/${userId}/roles/code/${roleCode}`);
}

/** Admin User Management/为管理员用户分配角色（通过角色ID） */
export function apiAssignRolesToUser(id: string, roleIds: string[]) {
  return http.post<TAdminUserBo>(`/users/${id}/roles`, roleIds);
}

/** Admin User Management/为管理员用户分配角色（通过角色代码） */
export function apiAssignRolesToUserByCodes(id: string, roleCodes: string[]) {
  return http.post<TAdminUserBo>(`/users/${id}/roles/codes`, roleCodes);
}

/** Admin User Management/分页获取管理员用户 */
export function apiGetUsers(data: TPageListReq) {
  return http.post<TPageResultAdminUserBo>('/users/page', data);
}

/** Admin User Management/设置管理员用户活动状态 */
export function apiSetUserActive(id: string, active: boolean) {
  return http.patch<TAdminUserBo>(`/users/${id}/active?active=${active}`);
}

/** Admin User Management/分页获取管理员用户 */
export function apiAdminUserPageList(data: TPageListReq) {
  return http.post<TPageListRes<TAdminUser>>('/users/page', data);
}

/** 管理员角色管理/分页获取管理员角色 */
export function apiGetRoles(data: TPageListReq) {
  return http.post<TPageResultAdminRoleBo>('/roles/page', data);
}

/** 管理员角色管理/通过ID获取管理员角色 */
export function apiGetRoleById(id: string) {
  return http.get<TAdminRoleBo>(`/roles/${id}`);
}

/** 管理员角色管理/更新管理员角色 */
export function apiUpdateRole(id: string, data: Partial<TCreateAdminRoleRequest>) {
  return http.put<TAdminRoleBo>(`/roles/${id}`, data);
}

/** 管理员角色管理/获取所有管理员角色 */
export function apiGetAllRoles() {
  return http.get<TAdminRoleBo[]>('/roles');
}

/** 管理员角色管理/创建新的管理员角色 */
export function apiCreateRole(data: TCreateAdminRoleRequest) {
  return http.post<TAdminRoleBo>('/roles', data);
}

/** 管理员角色管理/为管理员角色添加权限（通过权限ID） */
export function apiAddPermissionToRole(roleId: string, permissionId: string) {
  return http.post<TAdminRoleBo>(`/roles/${roleId}/permissions/${permissionId}`);
}

/** 管理员角色管理/从管理员角色中移除权限（通过权限ID） */
export function apiRemovePermissionFromRole(roleId: string, permissionId: string) {
  return http.delete<TAdminRoleBo>(`/roles/${roleId}/permissions/${permissionId}`);
}

/** 管理员角色管理/为管理员角色添加权限（通过权限代码） */
export function apiAddPermissionToRoleByCode(roleId: string, permissionCode: string) {
  return http.post<TAdminRoleBo>(`/roles/${roleId}/permissions/code/${permissionCode}`);
}

/** 管理员角色管理/从管理员角色中移除权限（通过权限代码） */
export function apiRemovePermissionFromRoleByCode(roleId: string, permissionCode: string) {
  return http.delete<TAdminRoleBo>(`/roles/${roleId}/permissions/code/${permissionCode}`);
}

/** 管理员角色管理/通过ID获取管理员角色权限 */
export function apiGetRolePermissions(id: string) {
  return http.get<string[]>(`/roles/${id}/permissions`);
}

/** 管理员角色管理/为管理员角色分配权限（通过权限ID） */
export function apiAssignPermissionsToRole(id: string, permissionIds: string[]) {
  return http.post<TAdminRoleBo>(`/roles/${id}/permissions`, permissionIds);
}

/** 管理员角色管理/为管理员角色分配权限（通过权限代码） */
export function apiAssignPermissionsToRoleByCodes(id: string, permissionCodes: string[]) {
  return http.post<TAdminRoleBo>(`/roles/${id}/permissions/codes`, permissionCodes);
}

/** 管理员角色管理/设置管理员角色激活状态 */
export function apiSetRoleActive(id: string, active: boolean) {
  return http.patch<TAdminRoleBo>(`/roles/${id}/active?active=${active}`);
}

/** 管理员角色管理/通过代码获取管理员角色 */
export function apiGetRoleByCode(code: string) {
  return http.get<TAdminRoleBo>(`/roles/code/${code}`);
}

/** 管理员角色管理/分页获取管理员角色 */
export function apiAdminRolePageList(data: TPageListReq) {
  return http.post<TPageResultAdminRoleBo>('/roles/page', data);
}

/** 管理员角色管理/通过ID获取管理员角色 */
export function apiAdminRoleGetById(id: string) {
  return http.get<TAdminRole>(`/roles/${id}`);
}

/** 管理员角色管理/更新管理员角色 */
export function apiAdminRoleUpdate(id: string, data: Partial<TCreateAdminRoleRequest>) {
  return http.put<TAdminRole>(`/roles/${id}`, data);
}

/** 管理员角色管理/获取所有管理员角色 */
export function apiAdminRoleGetAll() {
  return http.get<TAdminRole[]>('/roles');
}

/** 管理员角色管理/创建新的管理员角色 */
export function apiAdminRoleCreate(data: TCreateAdminRoleRequest) {
  return http.post<TAdminRole>('/roles', data);
}

/** 管理员角色管理/为管理员角色添加权限（通过权限ID） */
export function apiAdminRoleAddPermission(roleId: string, permissionId: string) {
  return http.post<TAdminRole>(`/roles/${roleId}/permissions/${permissionId}`);
}

/** 管理员角色管理/从管理员角色中移除权限（通过权限ID） */
export function apiAdminRoleRemovePermission(roleId: string, permissionId: string) {
  return http.delete<TAdminRole>(`/roles/${roleId}/permissions/${permissionId}`);
}

/** 管理员角色管理/通过ID获取管理员角色权限 */
export function apiAdminRolePermissions(id: string) {
  return http.get<string[]>(`/roles/${id}/permissions`);
}

/** 管理员角色管理/为管理员角色分配权限（通过权限ID） */
export function apiAdminRoleAssignPermissions(id: string, permissionIds: string[]) {
  return http.post<TAdminRole>(`/roles/${id}/permissions`, permissionIds);
}

/** 管理员角色管理/设置管理员角色激活状态 */
export function apiAdminRoleSetActive(id: string, active: boolean) {
  return http.patch<TAdminRole>(`/roles/${id}/active?active=${active}`);
}

/** 管理员角色管理/通过代码获取管理员角色 */
export function apiAdminRoleGetByCode(code: string) {
  return http.get<TAdminRole>(`/roles/code/${code}`);
}

/** 管理员权限管理/获取所有管理员权限 */
export function apiAdminPermissionList() {
  return http.get<TAdminPermission[]>('/permissions');
}

/** 管理员权限管理/分页获取管理员权限 */
export function apiAdminPermissionPageList(data: TPageListReq) {
  return http.post<TPageResultAdminPermissionBo>('/permissions/page', data);
}

/** 管理员权限管理/创建新的管理员权限 */
export function apiAdminPermissionCreate(data: Partial<TAdminPermission>) {
  return http.post<TAdminPermission>('/permissions', data);
}

/** 管理员权限管理/更新管理员权限 */
export function apiAdminPermissionUpdate(id: string, data: Partial<TAdminPermission>) {
  return http.put<TAdminPermission>(`/permissions/${id}`, data);
}

/** 管理员权限管理/通过ID获取管理员权限 */
export function apiAdminPermissionGetById(id: string) {
  return http.get<TAdminPermission>(`/permissions/${id}`);
}

/** 管理员权限管理/设置管理员权限的激活状态 */
export function apiAdminPermissionSetActive(id: string, active: boolean) {
  return http.patch<TAdminPermission>(`/permissions/${id}/active?active=${active}`);
}

/** 管理员权限管理/通过代码获取管理员权限 */
export function apiAdminPermissionGetByCode(code: string) {
  return http.get<TAdminPermission>(`/permissions/code/${code}`);
}

/** 管理员货币管理/分页获取所有货币 */
export function apiCurrencyPageList(data: TPageListReq) {
  return http.post<TPageListRes<TCurrency>>('/currencies/page', data);
}

/** 管理员货币管理/创建新货币 */
export function apiCurrencyCreate(data: Partial<TCurrency>) {
  return http.post<TCurrency>('/currencies', data);
}

/** 管理员货币管理/更新现有货币 */
export function apiCurrencyUpdate(code: string, data: Partial<TCurrency>) {
  return http.put<TCurrency>(`/currencies/${code}`, data);
}

/** 管理员货币管理/将货币设置为默认货币 */
export function apiCurrencySetDefault(code: string) {
  return http.patch<TCurrency>(`/currencies/${code}/default`);
}

/** 管理员货币管理/激活或停用货币 */
export function apiCurrencySetActive(code: string, active: boolean) {
  return http.patch<TCurrency>(`/currencies/${code}/active?active=${active}`);
}

/** 管理员语言管理/分页获取所有语言 */
export function apiLanguagePageList(data: TPageListReq) {
  return http.post<TPageListRes<TLanguage>>('/languages/page', data);
}

/** 管理员语言管理/创建新语言 */
export function apiLanguageCreate(data: Partial<TLanguage>) {
  return http.post<TLanguage>('/languages', data);
}

/** 管理员语言管理/更新现有语言 */
export function apiLanguageUpdate(code: string, data: Partial<TLanguage>) {
  return http.put<TLanguage>(`/languages/${code}`, data);
}

/** 管理员语言管理/设置默认语言 */
export function apiLanguageSetDefault(code: string) {
  return http.patch<TLanguage>(`/languages/${code}/default`);
}

/** 管理员语言管理/激活或停用语言 */
export function apiLanguageSetActive(code: string, active: boolean) {
  return http.patch<TLanguage>(`/languages/${code}/active?active=${active}`);
}

/** 管理员区域管理/分页获取所有区域 */
export function apiRegionPageList(data: TPageListReq) {
  return http.post<TPageListRes<TRegion>>('/regions/page', data);
}

/** 管理员区域管理/创建新区域 */
export function apiRegionCreate(data: Partial<TRegion>) {
  return http.post<TRegion>('/regions', data);
}

/** 管理员区域管理/更新区域 */
export function apiRegionUpdate(code: string, data: Partial<TRegion>) {
  return http.put<TRegion>(`/regions/${code}`, data);
}

/** 管理员区域管理/设置区域的激活状态 */
export function apiRegionSetActive(id: string, active: boolean) {
  return http.patch<TRegion>(`/regions/${id}/active?active=${active}`);
}

/** 管理员翻译管理/获取所有翻译（分页） */
export function apiTranslationPageList(data: TPageListReq) {
  return http.post<TPageListRes<TTranslationKey>>('/translations/page', data);
}

/** 管理员翻译管理/为多种语言创建新的翻译键 */
export function apiTranslationCreate(data: TUpdateTranslationsByKeyRequest) {
  return http.post<TTranslation[]>('/translations/create', data);
}

/** 管理员翻译管理/更新或创建特定键在多种语言中的翻译 */
export function apiTranslationUpdate(data: TUpdateTranslationsByKeyRequest) {
  return http.post<TTranslation[]>('/translations/update', data);
}

/** 管理员翻译管理/获取特定键在所有语言中的翻译 */
export function apiTranslationsByKey(key: string) {
  return http.get<TTranslationsByKeyDto>(`/translations/by-key/${key}`);
}

/** 管理员翻译管理/删除特定键的所有翻译 */
export function apiDeleteTranslationsByKey(key: string) {
  return http.delete(`/translations/by-key/${key}`);
}

/** 管理员文章管理/分页获取所有文章 */
export function apiArticlePageList(data: TArticlePageQuery) {
  return http.post<TPageListRes<TArticleBo>>('/articles/page', data);
}

/** 管理员文章管理/通过ID获取文章 */
export function apiArticleGetById(id: string) {
  return http.get<TArticleBo>(`/articles/${id}`);
}

/** 管理员文章管理/创建新文章 */
export function apiArticleCreate(data: Partial<TArticleBo>) {
  return http.post<TArticleBo>('/articles', data);
}

/** 管理员文章管理/更新文章 */
export function apiArticleUpdate(id: string, data: Partial<TArticleBo>) {
  return http.put<TArticleBo>(`/articles/${id}`, data);
}

/** 管理员文章管理/删除文章 */
export function apiArticleDelete(id: string) {
  return http.delete(`/articles/${id}`);
}

export function apiArticleCreateOrUpdate(data: Partial<TArticleBo> & { id?: string }) {
  if (data.id) {
    return apiArticleUpdate(data.id, data);
  } else {
    return apiArticleCreate(data);
  }
}

/** 管理员产品品牌管理/分页获取所有品牌 */
export function apiBrandPageList(data: API.PageQuery) {
  return http.post<API.PageResultProductBrandBo>('/brands/page', data);
}

/** 管理员产品品牌管理/根据ID获取品牌 */
export function apiBrandGetById(id: string) {
  return http.get<API.ProductBrandBo>(`/brands/${id}`);
}

/** 管理员产品品牌管理/创建新品牌 */
export function apiBrandCreate(data: Partial<API.ProductBrandBo>) {
  return http.post<API.ProductBrandBo>('/brands', data);
}

/** 管理员产品品牌管理/更新品牌 */
export function apiBrandUpdate(id: string, data: Partial<API.ProductBrandBo>) {
  return http.put<API.ProductBrandBo>(`/brands/${id}`, data);
}

/** 管理员产品品牌管理/删除品牌 */
export function apiBrandDelete(id: string) {
  return http.delete<void>(`/brands/${id}`);
}

/** 管理员产品品牌管理/设置品牌激活状态 */
export function apiBrandSetActive(id: string, active: boolean) {
  return http.patch<API.ProductBrandBo>(`/brands/${id}/active?active=${active}`);
}

/** 管理员产品品牌管理/设置品牌推荐状态 */
export function apiBrandSetFeatured(id: string, featured: boolean) {
  return http.patch<API.ProductBrandBo>(`/brands/${id}/featured?featured=${featured}`);
}

export function apiBrandCreateOrUpdate(data: Partial<API.ProductBrandBo> & { id?: number | string }) {
  if (data.id) {
    return apiBrandUpdate(data.id, data);
  } else {
    return apiBrandCreate(data);
  }
}


/** 管理员产品品牌管理/分页获取分类下的品牌 */
export function apiBrandGetByCategory(categoryId: string) {
  return http.post<API.ProductBrandBo[]>(`/brands/category/${categoryId}/page`);
}

/** 管理员产品品牌管理/将分类添加到品牌 */
export function apiBrandAddCategory(brandId: string, categoryId: string) {
  return http.post<API.ProductBrandBo>(`/brands/${brandId}/categories/${categoryId}`);
}

/** 管理员产品品牌管理/从品牌中移除分类 */
export function apiBrandRemoveCategory(brandId: string, categoryId: string) {
  return http.delete<API.ProductBrandBo>(`/brands/${brandId}/categories/${categoryId}`);
}

// 产品类别相关API
/** 产品分类管理/分页获取所有分类 */
export function apiCategoryPageList(data: API.CategoryPageQuery) {
  return http.post<API.PageResultProductCategoryBo>('/categories/page', data);
}

/** 产品分类管理/根据ID获取分类 */
export function apiCategoryGetById(id: string) {
  return http.get<API.ProductCategoryBo>(`/categories/${id}`);
}

/** 产品分类管理/创建新分类 */
export function apiCategoryCreate(data: Partial<API.ProductCategoryBo>) {
  return http.post<API.ProductCategoryBo>('/categories', data);
}

/** 产品分类管理/更新分类 */
export function apiCategoryUpdate(id: string, data: Partial<API.ProductCategoryBo>) {
  return http.put<API.ProductCategoryBo>(`/categories/${id}`, data);
}

/** 产品分类管理/删除分类 */
export function apiCategoryDelete(id: string) {
  return http.delete<void>(`/categories/${id}`);
}

export function apiCategoryCreateOrUpdate(data: Partial<API.ProductCategoryBo> & { id?: string }) {
  if (data.id) {
    return apiCategoryUpdate(data.id, data);
  } else {
    return apiCategoryCreate(data);
  }
}

/** 产品分类管理/获取完整分类树 */
export function apiCategoryTree(includeInactive: boolean = false) {
  return http.get<API.ProductCategoryBo[]>(`/categories/tree?includeInactive=${includeInactive}`);
}

/** 产品分类管理/获取分类子树 */
export function apiCategorySubtree(rootId: string, includeInactive: boolean = false) {
  return http.get<API.ProductCategoryBo>(`/categories/tree/${rootId}?includeInactive=${includeInactive}`);
}

/** 产品分类管理/根据编码获取分类 */
export function apiCategoryGetByCode(code: string) {
  return http.get<API.ProductCategoryBo>(`/categories/code/${code}`);
}


/** 产品分类管理/分页获取根分类 */
export function apiCategoryRootsPage(data: API.PageQuery) {
  return http.post<API.PageResultProductCategoryBo>('/categories/roots/page', data);
}

/** 产品分类管理/根据父级ID分页获取分类 */
export function apiCategoryByParentId(parentId: string, data: API.PageQuery) {
  return http.post<API.PageResultProductCategoryBo>(`/categories/parent/${parentId}/page`, data);
}

/** 产品分类管理/根据层级分页获取分类 */
export function apiCategoryByLevel(level: number, data: API.PageQuery) {
  return http.post<API.PageResultProductCategoryBo>(`/categories/level/${level}/page`, data);
}

/** 产品分类管理/设置分类激活状态 */
export function apiCategorySetActive(id: string, isActive: boolean) {
  return http.patch<API.ProductCategoryBo>(`/categories/${id}/active?isActive=${isActive}`);
}

/** 管理员货币管理/获取特定货币的汇率 */
export function apiExchangeRatesByCurrency(currencyCode: string) {
  return http.get<any>(`/currencies/${currencyCode}/exchange-rates`);
}

/** 管理员货币管理/为汇率添加规则 */
export function apiExchangeRateAddRule(fromCurrency: string, toCurrency: string, data: Partial<TSystemExchangeRateRule>) {
  return http.post<TExchangeRate>(`/currencies/exchange-rates/${fromCurrency}/${toCurrency}/rules`, data);
}

/** 管理员货币管理/将已有规则应用到汇率上 */
export function apiExchangeRateApplyRule(fromCurrency: string, toCurrency: string, ruleId: number) {
  return http.put<TExchangeRate>(`/currencies/exchange-rates/${fromCurrency}/${toCurrency}/rules/${ruleId}`);
}

// 商品相关API
/** 产品管理/通过ID获取产品 */
export function apiProductGetById(id: string) {
  return http.get<ProductBo>(`/products/${id}`);
}

/** 产品管理/通过ID获取产品 */
export function apiProductGetDetails(id: string | number) {
  return http.get<ProductBo>(`/products/${id}`);
}

/** 产品管理/更新产品 */
export function apiProductUpdate(id: string, data: Partial<ProductBo>) {
  return http.put<ProductBo>(`/products/${id}`, data);
}

/** 产品管理/删除产品 */
export function apiProductDelete(id: string | number) {
  return http.delete<boolean>(`/products/${id}`);
}

/** 产品管理/设置产品精选状态 */
export function apiProductSetFeatured(id: string, isFeatured: boolean) {
  return http.put<ProductBo>(`/products/${id}/featured/${isFeatured}`);
}

/** 产品管理/设置产品活动状态 */
export function apiProductSetActive(id: string, isActive: boolean) {
  return http.put<ProductBo>(`/products/${id}/active/${isActive}`);
}

/** 产品管理/创建新产品 */
export function apiProductCreate(data: Partial<ProductBo>) {
  return http.post<ProductBo>('/products', data);
}

/** 产品管理/分页获取所有产品 */
export function apiProductPageList(data: TPageListReq) {
  return http.post<TPageListRes<ProductBo>>('/products/page', data);
}

/** 客户用户管理/分页获取客户用户 */
export function apiClientUserPageList(data: TPageListReq) {
  return http.post<TPageListRes<ClientUserBo>>('/client-users/page', data);
}

/** 客户用户管理/获取所有客户用户 */
export function apiClientUserGetAll() {
  return http.get<ClientUserBo[]>('/client-users');
}

/** 客户用户管理/获取所有活跃客户用户 */
export function apiClientUserGetAllActive() {
  return http.get<ClientUserBo[]>('/client-users/active');
}

/** 客户用户管理/根据ID获取客户用户 */
export function apiClientUserGetById(id: number) {
  return http.get<ClientUserBo>(`/client-users/${id}`);
}

/** 客户用户管理/创建新客户用户 */
export function apiClientUserCreate(data: CreateClientUserRequest) {
  return http.post<ClientUserBo>('/client-users', data);
}

/** 客户用户管理/更新客户用户信息 */
export function apiClientUserUpdate(id: number, data: UpdateClientUserRequest) {
  return http.put<ClientUserBo>(`/client-users/${id}`, data);
}

/** 客户用户管理/重置客户用户密码 */
export function apiClientUserResetPassword(id: number, data: ResetPasswordRequest) {
  return http.post<ClientUserBo>(`/client-users/${id}/reset-password`, data);
}

/** 客户用户管理/设置客户用户活跃状态 */
export function apiClientUserSetActive(id: number, active: boolean) {
  return http.patch<ClientUserBo>(`/client-users/${id}/active?active=${active}`);
}

/** 管理员钱包管理/获取用户的所有钱包 */
export function apiWalletGetUserWallets(userId: number) {
  return http.get<WalletBo[]>(`/wallets/user/${userId}`);
}

/** 管理员钱包管理/获取用户的激活钱包 */
export function apiWalletGetUserActiveWallets(userId: number) {
  return http.get<WalletBo[]>(`/wallets/user/${userId}/active`);
}

/** 管理员钱包管理/获取用户的主钱包 */
export function apiWalletGetUserMainWallet(userId: number) {
  return http.get<WalletBo>(`/wallets/user/${userId}/main`);
}

/** 管理员钱包管理/获取用户的特定货币钱包 */
export function apiWalletGetUserWalletByCurrency(userId: number, currency: string) {
  return http.get<WalletBo>(`/wallets/user/${userId}/currency/${currency}`);
}

/** 管理员钱包管理/为用户创建默认钱包 */
export function apiWalletCreateDefaultWallet(userId: number, currency: string) {
  return http.post<WalletBo>(`/wallets/user/${userId}/default?currency=${currency}`);
}

/** 管理员钱包管理/钱包充值 */
export function apiWalletDeposit(id: number, amount: number, referenceId: string) {
  return http.post<WalletBo>(`/wallets/${id}/deposit?amount=${amount}&referenceId=${referenceId}`);
}

/** 管理员钱包管理/钱包提现 */
export function apiWalletWithdraw(id: number, amount: number, referenceId: string) {
  return http.post<WalletBo>(`/wallets/${id}/withdraw?amount=${amount}&referenceId=${referenceId}`);
}

/** 管理员钱包管理/获取钱包交易记录 */
export function apiWalletGetTransactions(walletId: number) {
  return http.get(`/wallets/${walletId}/transactions`);
}

/** 管理员钱包管理/分页获取钱包交易记录 */
export function apiWalletGetTransactionsPage(walletId: number, data: TPageListReq) {
  return http.get<TPageListRes<TransactionBo>>(`/wallets/${walletId}/transactions/page`, { params: data });
}

/** 管理员钱包管理/获取用户的待审批存款申请 */
export function apiWalletGetPendingDeposits(userId: number) {
  return http.get<TransactionBo[]>(`/wallets/user/${userId}/pending-deposits`);
}

/** 管理员钱包管理/批准存款申请 */
export function apiWalletApproveDeposit(transactionId: number) {
  return http.post(`/wallets/transactions/${transactionId}/approve`);
}

/** 管理员钱包管理/拒绝存款申请 */
export function apiWalletRejectDeposit(transactionId: number, reason: string) {
  return http.post(`/wallets/transactions/${transactionId}/reject`, { reason });
}

/** 管理员订单管理/分页获取所有订单 */
export function apiOrderGetAllOrders(data: TPageListReq) {
  return http.post<TPageListRes<Order>>('/orders/page', data);
}

/** 管理员订单管理/获取订单详情 */
export function apiOrderGetOrderDetails(orderId: number | string) {
  return http.get<Order>(`/orders/${orderId}`);
}

/** 管理员订单管理/根据订单号获取订单 */
export function apiOrderGetOrderByOrderNumber(orderNumber: string) {
  return http.get<Order>(`/orders/number/${orderNumber}`);
}

/** 管理员订单管理/更新订单状态 */
export function apiOrderUpdateOrderStatus(orderId: number | string, status: string) {
  return http.put<Order>(`/orders/${orderId}/status?status=${status}`);
}

/** 管理员订单管理/更新订单支付状态 */
export function apiOrderUpdatePaymentStatus(orderId: number | string, status: string) {
  return http.put<Order>(`/orders/${orderId}/payment-status?status=${status}`);
}

/** 管理员订单管理/添加管理员备注 */
export function apiOrderAddAdminNotes(orderId: number | string, notes: string) {
  return http.put<Order>(`/orders/${orderId}/admin-notes`, { notes });
}

/** 管理员订单管理/平台收货 */
export function apiOrderPlatformReceive(orderId: number | string) {
  return http.post<Order>(`/orders/${orderId}/platform-receive`);
}

/** 管理员订单管理/平台发货到买家 */
export function apiOrderPlatformShip(orderId: number | string, data: { trackingNumber: string, shippingCarrier: string }) {
  return http.post<Order>(`/orders/${orderId}/platform-ship`, data);
}

/** 管理员订单管理/平台验证完成 */
export function apiOrderPlatformVerify(orderId: number | string, data: { verificationNotes: string }) {
  return http.post<Order>(`/orders/${orderId}/platform-verify`, data);
}

/** 管理员订单管理/平台确认退货并退回卖家 */
export function apiOrderPlatformReceiveReturn(orderId: number | string, data: { returnNotes: string }) {
  return http.post<Order>(`/orders/${orderId}/platform-return-to-seller`, data);
}

/** 报价管理/分页获取所有报价 */
export function apiPriceQuoteGetAll(data: TPageListReq) {
  return http.post<TPageListRes<PriceQuote>>('/price-quotes/page', data);
}

/** 报价管理/获取报价详情 */
export function apiPriceQuoteGetDetails(quoteId: number) {
  return http.get<PriceQuote>(`/price-quotes/${quoteId}`);
}

/** 报价管理/更新报价状态 */
export function apiPriceQuoteUpdateStatus(quoteId: number, status: string) {
  return http.put<PriceQuote>(`/price-quotes/${quoteId}/status?status=${status}`);
}


/** 报价管理/分页获取所有报价 */
export function apiPriceQuoteGetPage(data: TPageListReq) {
  return http.post<TPageListRes<PriceQuoteBo>>('/price-quotes/page', data);
}

/** 管理员钱包管理/分页获取所有待审核的汇款充值记录 */
export function apiRemittanceGetPendingDepositsPage(data: TPageListReq) {
  return http.post<TPageListRes<TransactionBo>>('/wallets/remittance-deposits/pending/page', data);
}

/** 管理员钱包管理/批准存款申请 */
export function apiRemittanceApproveDeposit(transactionId: number) {
  return http.post<WalletBo>(`/wallets/transactions/${transactionId}/approve`);
}

/** 管理员钱包管理/拒绝存款申请 */
export function apiRemittanceRejectDeposit(transactionId: number, reason: string) {
  return http.post<boolean>(`/wallets/transactions/${transactionId}/reject`, { reason });
}

// 系统设置管理相关API

/** 系统设置管理/根据ID获取系统设置 */
export function apiSystemSettingGetById(id: string | number) {
  return http.get<TSystemSettingBo>(`/system/settings/${id}`);
}

/** 系统设置管理/更新现有系统设置 */
export function apiSystemSettingUpdate(id: string | number, data: Partial<TSystemSettingBo>) {
  return http.put<TSystemSettingBo>(`/system/settings/${id}`, data);
}

/** 系统设置管理/删除系统设置 */
export function apiSystemSettingDelete(id: string | number) {
  return http.delete<object>(`/system/settings/${id}`);
}

/** 系统设置管理/根据键获取系统设置 */
export function apiSystemSettingGetByKey(key: string) {
  return http.get<TSystemSettingBo>(`/system/settings/key/${key}`);
}

/** 系统设置管理/获取所有系统设置 */
export function apiSystemSettingGetAll() {
  return http.get<TSystemSettingBo[]>('/system/settings');
}

/** 系统设置管理/创建新的系统设置 */
export function apiSystemSettingCreate(data: Partial<TSystemSettingBo>) {
  return http.post<TSystemSettingBo>('/system/settings', data);
}

/** 系统设置管理/分页获取系统设置 */
export function apiSystemSettingPageList(data: TPageListReq) {
  return http.post<TPageResultSystemSettingBo>('/system/settings/page', data);
}

/** 系统设置管理/设置系统设置激活状态 */
export function apiSystemSettingSetActive(id: string | number, active: boolean) {
  return http.patch<TSystemSettingBo>(`/system/settings/${id}/active?active=${active}`);
}

/** 系统设置管理/根据分组获取系统设置 */
export function apiSystemSettingGetByGroup(group: string) {
  return http.get<TSystemSettingBo[]>(`/system/settings/group/${group}`);
}


// 标签管理相关API

/** 管理员标签管理/获取标签详情 */
export function apiGetTagDetails(tagId: string) {
  return http.get<TTagBo>(`/tags/${tagId}`);
}

/** 管理员标签管理/更新标签 */
export function apiUpdateTag(tagId: string, data: Partial<TTagBo>) {
  return http.put<TTagBo>(`/tags/${tagId}`, data);
}

/** 管理员标签管理/删除标签 */
export function apiDeleteTag(tagId: string) {
  return http.delete(`/tags/${tagId}`);
}

/** 管理员标签管理/创建新标签 */
export function apiCreateTag(data: Partial<TTagBo>) {
  return http.post<TTagBo>('/tags', data);
}

/** 管理员标签管理/分页获取所有标签 */
export function apiGetAllTags(data: TPageQuery) {
  return http.post<TPageResultTagBo>('/tags/page', data);
}

/** 管理员标签管理/获取标签关联的所有产品 */
export function apiGetProductsByTagId(tagId: string) {
  return http.get<ProductBo[]>(`/tags/${tagId}/products`);
}

/** 管理员标签管理/更新标签关联的产品 */
export function apiUpdateProductsForTag(tagId: string, productIds: string[]) {
  return http.put<TProductTagBo[]>(`/tags/${tagId}/products`, productIds);
}

/** 管理员货币管理/创建新的汇率规则 */
export function apiExchangeRateRuleCreate(data: Partial<TSystemExchangeRateRule>) {
  return http.post<TExchangeRateRuleBo>('/currencies/exchange-rate-rules', data);
}

/** 管理员货币管理/更新现有汇率规则 */
export function apiExchangeRateRuleUpdate(id: number, data: Partial<TSystemExchangeRateRule>) {
  return http.put<TExchangeRateRuleBo>(`/currencies/exchange-rate-rules/${id}`, data);
}

/** 管理员货币管理/删除汇率规则 */
export function apiExchangeRateRuleDelete(id: number) {
  return http.delete(`/currencies/exchange-rate-rules/${id}`);
}

/** 管理员货币管理/分页获取汇率规则 */
export function apiExchangeRateRulePageList(data: TPageListReq) {
  return http.post<TPageListRes<TExchangeRateRuleBo>>('/currencies/exchange-rate-rules/page', data);
}

/** 通知模板管理/获取通知模板详情 */
export function apiNotificationTemplateGet(id: string) {
  return http.get<TNotificationTemplateBo>(`/notification-templates/${id}`);
}

/** 通知模板管理/更新通知模板 */
export function apiNotificationTemplateUpdate(id: string, data: TUpdateTemplateRequest) {
  return http.put<TNotificationTemplateBo>(`/notification-templates/${id}`, data);
}

/** 通知模板管理/删除通知模板 */
export function apiNotificationTemplateDelete(id: string) {
  return http.delete(`/notification-templates/${id}`);
}

/** 通知模板管理/启用通知模板 */
export function apiNotificationTemplateEnable(id: string) {
  return http.put<TNotificationTemplateBo>(`/notification-templates/${id}/enable`);
}

/** 通知模板管理/禁用通知模板 */
export function apiNotificationTemplateDisable(id: string) {
  return http.put<TNotificationTemplateBo>(`/notification-templates/${id}/disable`);
}

/** 通知模板管理/获取通知模板列表 */
export function apiNotificationTemplatePage(data: TPageQuery) {
  return http.post<TPageResultNotificationTemplateBo>('/notification-templates/page', { data });
}

/** 通知模板管理/创建通知模板 */
export function apiNotificationTemplateCreate(data: TCreateTemplateRequest) {
  return http.post<TNotificationTemplateBo>('/notification-templates', data);
}

/** 通知模板管理/使用模板发送通知 */
export function apiNotificationTemplateSend(data: TSendTemplateNotificationRequest) {
  return http.post<TUserNotificationBo>('/notification-templates/send', data);
}

/** 通知模板管理/批量使用模板发送通知 */
export function apiNotificationTemplateBatchSend(data: TBatchSendTemplateNotificationRequest) {
  return http.post<TUserNotificationBo[]>('/notification-templates/batch-send', data);
}


export function apiSyncBrandCategories(brandId: string, categoryIds: string[]): Promise<TProductBrandBo> {
  return http.put<TProductBrandBo>(`/brands/${brandId}/categories`, categoryIds);
}

export function apiGetCategoriesByBrandId(brandId: string): Promise<TProductCategoryBo[]> {
  return http.get<TProductCategoryBo[]>(`/categories/brand/${brandId}`);
}
