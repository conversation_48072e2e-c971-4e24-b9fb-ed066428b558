import React from 'react';
import { <PERSON>, Button, Space, Tooltip } from 'antd';
import { DeleteOutlined, ArrowUpOutlined, ArrowDownOutlined, PlusOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';

interface BaseModuleWrapperProps {
  isFirst: boolean;
  isLast: boolean;
  onDelete: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onInsertBefore: () => void;
  children: React.ReactNode;
  isEditing?: boolean;
  onEditingChange?: (isEditing: boolean) => void;
}

const BaseModuleWrapper: React.FC<BaseModuleWrapperProps> = ({
  isFirst,
  isLast,
  onDelete,
  onMoveUp,
  onMoveDown,
  onInsertBefore,
  children,
  isEditing,
  onEditingChange
}) => {
  const toggleEdit = () => {
    onEditingChange?.(!isEditing);
  };

  return (
    <div className="mb-4 relative group">
      <div className="flex justify-end bg-zinc-100 p-2 pb-4 rounded-t -mb-2">
        <Space>
          <Tooltip title="编辑">
            <Button
              type={isEditing ? 'default' : 'primary'}
              icon={isEditing ? <EyeOutlined /> : <EditOutlined />}
              size="small"
              onClick={toggleEdit}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
              onClick={onDelete}
            />
          </Tooltip>
          <Tooltip title="上移">
            <Button
              icon={<ArrowUpOutlined />}
              size="small"
              onClick={onMoveUp}
              disabled={isFirst}
            />
          </Tooltip>
          <Tooltip title="下移">
            <Button
              icon={<ArrowDownOutlined />}
              size="small"
              onClick={onMoveDown}
              disabled={isLast}
            />
          </Tooltip>
          <Tooltip title="在此之前插入模块">
            <Button
              icon={<PlusOutlined />}
              size="small"
              onClick={onInsertBefore}
            />
          </Tooltip>
        </Space>
      </div>
      <Card bordered={true} className="shadow-sm">
        {children}
      </Card>
    </div>
  );
};

export default BaseModuleWrapper;
