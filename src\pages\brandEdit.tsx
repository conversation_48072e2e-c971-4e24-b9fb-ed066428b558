import React, { useEffect, useState } from 'react';
import { Button, Card, Form, Input, InputNumber, Select, Space, Switch, Upload, message } from 'antd';
import { ArrowLeftOutlined, LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { apiBrandCreateOrUpdate, apiBrandGetById } from '@/apis/apis.api';
import I18nField from '@/components/I18nField';
import I18nText from '@/components/I18nText';
import type { UploadChangeParam } from 'antd/es/upload';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';
import HttpUtils from '@/utils/http';

const { TextArea } = Input;

const getBase64 = (img: RcFile, callback: (url: string) => void) => {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result as string));
  reader.readAsDataURL(img);
};

const beforeUpload = (file: RcFile) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('只能上传 JPG/PNG 格式的图片!');
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB!');
  }
  return isJpgOrPng && isLt2M;
};

const BrandEdit: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [logoUrl, setLogoUrl] = useState<string>('');
  const [logoLoading, setLogoLoading] = useState<boolean>(false);

  useEffect(() => {
    if (id) {
      fetchBrandDetail();
    }
  }, [id]);

  const fetchBrandDetail = async () => {
    try {
      setLoading(true);
      const brandData = await apiBrandGetById(id!);

      // Handle multi-language fields
      const formData = {
        code: brandData.code,
        website: brandData.website,
        isActive: brandData.isActive,
        isFeatured: brandData.isFeatured,
        sortOrder: brandData.sortOrder,
        firstLetter: brandData.firstLetter,
      };

      // Process name field (multi-language)
      if (typeof brandData.name === 'string') {
        try {
          // Try to parse as JSON for multi-language
          formData.name = brandData.name;
        } catch (e) {
          // If not valid JSON, use as is
          formData.name = brandData.name;
        }
      } else {
        formData.name = brandData.name;
      }

      // Process description field (multi-language)
      if (typeof brandData.description === 'string') {
        try {
          // Try to parse as JSON for multi-language
          formData.description = brandData.description;
        } catch (e) {
          // If not valid JSON, use as is
          formData.description = brandData.description;
        }
      } else {
        formData.description = brandData.description;
      }

      form.setFieldsValue(formData);

      if (brandData.logo) {
        setLogoUrl(brandData.logo);
      }
    } catch (error) {
      console.error('获取品牌详情失败:', error);
      message.error('获取品牌详情失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      await form.validateFields();
      const values = form.getFieldsValue();

      setSubmitting(true);

      // Get form values
      const formValues = form.getFieldsValue();

      const data = {
        ...formValues,
        logo: logoUrl,
        firstLetter: formValues.firstLetter?.toUpperCase() || '',
      };

      if (id) {
        data.id = id;
      }

      await apiBrandCreateOrUpdate(data);
      message.success('保存成功');
      navigate('/brand/list');
    } catch (error) {
      console.error('保存品牌失败:', error);
      message.error('保存失败');
    } finally {
      setSubmitting(false);
    }
  };

  const handleLogoChange: UploadProps['onChange'] = (info: UploadChangeParam<UploadFile>) => {
    if (info.file.status === 'uploading') {
      setLogoLoading(true);
      return;
    }

    if (info.file.status === 'done') {
      // 这里假设上传接口返回的数据格式为 { url: 'xxx' }
      // 实际项目中需要根据后端接口返回的数据格式进行调整
      const logoUrl = info.file.response?.url || '';
      setLogoUrl(logoUrl);
      setLogoLoading(false);
    }
  };

  // 模拟上传图片的函数，实际项目中需要替换为真实的上传接口
  const customUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;
    HttpUtils.uploadFile(file).then((res) => {
      onSuccess({ url: res }, file);
    }).catch((error) => {
      onError(error);
    });
  };

  return (
    <Card
      title={
        <div>
          <Space>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/brand/list')}
            >
              返回
            </Button>
            <span>{id ? '编辑' : '新增'}品牌</span>
          </Space>
        </div>
      }
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <LoadingOutlined style={{ fontSize: 24 }} />
          <p>加载中...</p>
        </div>
      ) : (
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            isActive: true,
            isFeatured: false,
            sortOrder: 0,
          }}
        >
          <Form.Item
            name="name"
            label="品牌名称"
            rules={[{ required: true, message: '请输入品牌名称' }]}
          >
            <I18nField>
              <Input placeholder="请输入品牌名称" />
            </I18nField>
          </Form.Item>

          <Form.Item
            name="code"
            label="品牌编码"
            rules={[{ required: true, message: '请输入品牌编码' }]}
          >
            <Input placeholder="请输入品牌编码" />
          </Form.Item>

          <Form.Item
            name="firstLetter"
            label="首字母"
            rules={[
              { required: true, message: '请输入首字母' },
              { max: 1, message: '首字母只能是单个字符' },
              { pattern: /^[A-Za-z]$/, message: '首字母必须是英文字母' }
            ]}
          >
            <Input
              placeholder="请输入品牌名称首字母（用于字母排序）"
              maxLength={1}
              onChange={(e) => form.setFieldsValue({ firstLetter: e.target.value.toUpperCase() })}
            />
          </Form.Item>

          <Form.Item
            name="website"
            label="品牌网站"
          >
            <Input placeholder="请输入品牌网站URL" />
          </Form.Item>

          <Form.Item
            name="description"
            label="品牌描述"
          >
            <I18nField>
              <Input.TextArea rows={4} placeholder="请输入品牌描述" />
            </I18nField>
          </Form.Item>

          <Form.Item label="品牌Logo">
            <Upload
              name="logo"
              listType="picture-card"
              showUploadList={false}
              beforeUpload={beforeUpload}
              onChange={handleLogoChange}
              customRequest={customUpload}
            >
              {logoUrl ? (
                <img src={logoUrl} alt="logo" style={{ width: '100%' }} />
              ) : (
                <div>
                  {logoLoading ? <LoadingOutlined /> : <PlusOutlined />}
                  <div style={{ marginTop: 8 }}>上传</div>
                </div>
              )}
            </Upload>
          </Form.Item>

          <Form.Item
            name="sortOrder"
            label="排序"
            rules={[{ required: true, message: '请输入排序值' }]}
          >
            <InputNumber min={0} placeholder="请输入排序值，数值越大越靠前" style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="是否启用"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="isFeatured"
            label="是否推荐"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSubmit} loading={submitting}>
                保存
              </Button>
              <Button onClick={() => navigate('/brand/list')}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      )}
    </Card>
  );
};

export default BrandEdit;

BrandEdit.auth = ['apiBrandCreateOrUpdate', 'apiBrandGetById'];
