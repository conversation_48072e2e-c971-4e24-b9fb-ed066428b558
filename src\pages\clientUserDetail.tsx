import { apiGetClientUserById } from '@/apis/gen.apis';
import ApiApplicationListComponent from '@/components/ApiApplicationListComponent';
import ClientTransactionListComponent from '@/components/ClientTransactionListComponent';
import ClientWalletListComponent from '@/components/ClientWalletListComponent';
import { ArrowLeftOutlined, UserOutlined } from '@ant-design/icons';
import { useMemoizedFn } from 'ahooks';
import {
  Avatar,
  Button,
  Card,
  Col,
  Descriptions,
  message,
  Row,
  Space,
  Tabs,
  Tag
} from 'antd';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

const { TabPane } = Tabs;

function ClientUserDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState<TClientUserBO | null>(null);

  // 获取用户详情
  const fetchUserDetails = useMemoizedFn(async () => {
    if (!id) return;

    setLoading(true);
    try {
      const userData = await apiGetClientUserById(id);
      setUserInfo(userData);
    } catch (error) {
      console.error('获取用户详情失败:', error);
      message.error('获取用户详情失败');
    } finally {
      setLoading(false);
    }
  });

  useEffect(() => {
    fetchUserDetails();
  }, [id, fetchUserDetails]);

  // 返回列表页面
  const handleGoBack = useMemoizedFn(() => {
    navigate('/client/user-management');
  });

  // 格式化时间显示
  const formatDateTime = (dateTime: any) => {
    if (!dateTime) return '-';
    return new Date(dateTime).toLocaleString();
  };

  // 获取用户状态颜色
  const getUserStatusColor = (enabled: boolean) => {
    return enabled ? 'green' : 'red';
  };

  // 获取用户状态文本
  const getUserStatusText = (enabled: boolean) => {
    return enabled ? '启用' : '禁用';
  };

  return (
    <div className='space-y-4'>
      <Space>
        <Button icon={<ArrowLeftOutlined />} onClick={handleGoBack}>返回</Button>
        <div className='text-xl font-bold'>客户用户详情</div>
      </Space>

      {userInfo && (
        <>
          {/* 用户基本信息卡片 */}
          <Card title="基本信息" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={4}>
                <div style={{ textAlign: 'center' }}>
                  <Avatar
                    size={80}
                    src={userInfo.avatar}
                    icon={<UserOutlined />}
                  />
                  <div style={{ marginTop: 8, fontWeight: 'bold' }}>
                    {userInfo.nickname || userInfo.username}
                  </div>
                  <Tag color={getUserStatusColor(userInfo.enabled)}>
                    {getUserStatusText(userInfo.enabled)}
                  </Tag>
                </div>
              </Col>
              <Col span={20}>
                <Descriptions bordered column={3}>
                  <Descriptions.Item label="用户ID" span={1}>{userInfo.id}</Descriptions.Item>
                  <Descriptions.Item label="用户名" span={1}>{userInfo.username}</Descriptions.Item>
                  <Descriptions.Item label="昵称" span={1}>{userInfo.nickname || '-'}</Descriptions.Item>
                  <Descriptions.Item label="邮箱" span={1}>{userInfo.email}</Descriptions.Item>
                  <Descriptions.Item label="手机号" span={1}>{userInfo.phone || '-'}</Descriptions.Item>
                  <Descriptions.Item label="最后登录时间" span={1}>{formatDateTime(userInfo.lastLoginAt)}</Descriptions.Item>
                  <Descriptions.Item label="最后登录IP" span={1}>{userInfo.lastLoginIp || '-'}</Descriptions.Item>
                  <Descriptions.Item label="创建时间" span={1}>{formatDateTime(userInfo.createdAt)}</Descriptions.Item>
                  <Descriptions.Item label="更新时间" span={1}>{formatDateTime(userInfo.updatedAt)}</Descriptions.Item>
                  <Descriptions.Item label="备注" span={3}>{userInfo.remarks || '-'}</Descriptions.Item>
                </Descriptions>
              </Col>
            </Row>
          </Card>

          {/* 标签页内容 */}
          <Card>
            <Tabs defaultActiveKey="applications">
              <TabPane tab="API应用" key="applications">
                <ApiApplicationListComponent
                  userId={userInfo.id}
                  showUserIdColumn={false}
                  headerTitle={`${userInfo.username} 的API应用`}
                  showAddButton={true}
                />
              </TabPane>
              <TabPane tab="钱包信息" key="wallets">
                <ClientWalletListComponent
                  userId={userInfo.id}
                  showUserIdColumn={false}
                  headerTitle={`${userInfo.username} 的钱包`}
                  showAddButton={true}
                />
              </TabPane>
              <TabPane tab="交易记录" key="transactions">
                <ClientTransactionListComponent
                  userId={userInfo.id}
                  showUserIdColumn={false}
                  headerTitle={`${userInfo.username} 的交易记录`}
                  showAddButton={true}
                />
              </TabPane>
              <TabPane tab="操作日志" key="logs">
                <div style={{ padding: '20px', textAlign: 'center', color: '#999' }}>
                  操作日志功能开发中...
                </div>
              </TabPane>
            </Tabs>
          </Card>
        </>
      )}
    </div>
  );
}

export default ClientUserDetail;

ClientUserDetail.auth = ['apiGetClientUserById'];
