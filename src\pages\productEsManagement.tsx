import React, { useEffect, useState, useRef } from 'react';
import { Card, Button, Space, Table, message, Input, Tabs, Statistic, Row, Col, Modal, Form, Select, Tag, Tooltip } from 'antd';
import { SyncOutlined, SearchOutlined, DeleteOutlined, ReloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import {
  apiProductEsSearch,
  apiProductEsIndex,
  apiProductEsDelete,
  apiProductEsIndexBatch,
  apiProductEsIndexAll,
  apiProductEsCount
} from '@/apis/productEs.api';
import { apiProductPageList } from '@/apis/apis.api';
import I18nText from '@/components/I18nText';

const { TabPane } = Tabs;
const { confirm } = Modal;

const ProductEsManagement: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [indexingAll, setIndexingAll] = useState<boolean>(false);
  const [productCount, setProductCount] = useState<number>(0);
  const [esProductCount, setEsProductCount] = useState<number>(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const tableRef = useRef<ActionType>();
  const esTableRef = useRef<ActionType>();

  // Load product counts
  const loadCounts = async () => {
    try {
      // Get ES product count
      const esCountResponse = await apiProductEsCount();
      setEsProductCount(esCountResponse.count || 0);

      // Get total product count from regular API
      const productResponse = await apiProductPageList({
        current: 0,
        size: 1
      });
      setProductCount(productResponse.total || 0);
    } catch (error) {
      console.error('Failed to load counts:', error);
      message.error('加载产品数量失败');
    }
  };

  useEffect(() => {
    loadCounts();
  }, []);

  // Index a single product
  const handleIndexProduct = async (productId: number | string) => {
    try {
      setLoading(true);
      await apiProductEsIndex(productId);
      message.success('产品索引成功');
      if (esTableRef.current) {
        esTableRef.current.reload();
      }
      loadCounts();
    } catch (error) {
      console.error('Failed to index product:', error);
      message.error('产品索引失败');
    } finally {
      setLoading(false);
    }
  };

  // Delete a product from index
  const handleDeleteProduct = async (productId: number | string) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要从Elasticsearch中删除此产品吗？',
      onOk: async () => {
        try {
          setLoading(true);
          await apiProductEsDelete(productId);
          message.success('产品从索引中删除成功');
          if (esTableRef.current) {
            esTableRef.current.reload();
          }
          loadCounts();
        } catch (error) {
          console.error('Failed to delete product from index:', error);
          message.error('删除产品索引失败');
        } finally {
          setLoading(false);
        }
      }
    });
  };

  // Index selected products
  const handleIndexSelected = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要索引的产品');
      return;
    }

    try {
      setLoading(true);
      await apiProductEsIndexBatch(selectedRowKeys as (number | string)[]);
      message.success(`${selectedRowKeys.length}个产品索引成功`);
      setSelectedRowKeys([]);
      if (esTableRef.current) {
        esTableRef.current.reload();
      }
      loadCounts();
    } catch (error) {
      console.error('Failed to index selected products:', error);
      message.error('批量索引产品失败');
    } finally {
      setLoading(false);
    }
  };

  // Index all products
  const handleIndexAll = async () => {
    confirm({
      title: '确认索引所有产品',
      icon: <ExclamationCircleOutlined />,
      content: '确定要索引所有产品吗？这可能需要一些时间。',
      onOk: async () => {
        try {
          setIndexingAll(true);
          await apiProductEsIndexAll();
          message.success('所有产品索引请求已提交，索引过程将在后台进行');
          setTimeout(() => {
            if (esTableRef.current) {
              esTableRef.current.reload();
            }
            loadCounts();
          }, 3000); // Give some time for indexing to start
        } catch (error) {
          console.error('Failed to index all products:', error);
          message.error('索引所有产品失败');
        } finally {
          setIndexingAll(false);
        }
      }
    });
  };

  // Regular product table columns
  const productColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },

    {
      title: '产品名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string) => <I18nText value={name} />,
    },
    {
      title: '产品编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: '品牌',
      dataIndex: 'brandName',
      key: 'brandName',
      width: 120,
      render: (brandName: string) => <I18nText value={brandName} />,
    },
    {
      title: '分类',
      dataIndex: 'categoryName',
      key: 'categoryName',
      width: 120,
      render: (categoryName: string) => <I18nText value={categoryName} />,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      render: (price: any) => {
        // Check if price is a valid number
        if (price === null || price === undefined) {
          return '-';
        }

        // Convert to number if it's a string
        const numPrice = typeof price === 'string' ? parseFloat(price) : price;

        // Check if conversion resulted in a valid number
        return !isNaN(numPrice) ? `¥${numPrice.toFixed(2)}` : '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any, record: any) => (
        <Space>
          <Button
            type="primary"
            icon={<SyncOutlined />}
            size="small"
            onClick={() => handleIndexProduct(record.id)}
            loading={loading}
          >
            索引
          </Button>
        </Space>
      ),
    },
  ];

  // ES product table columns
  const esProductColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },

    {
      title: '产品名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string) => <I18nText value={name} />,
    },
    {
      title: '产品编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: '品牌',
      dataIndex: 'brandName',
      key: 'brandName',
      width: 120,
      render: (brandName: string) => <I18nText value={brandName} />,
    },
    {
      title: '分类',
      dataIndex: 'categoryName',
      key: 'categoryName',
      width: 120,
      render: (categoryName: string) => <I18nText value={categoryName} />,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      render: (price: any) => {
        // Check if price is a valid number
        if (price === null || price === undefined) {
          return '-';
        }

        // Convert to number if it's a string
        const numPrice = typeof price === 'string' ? parseFloat(price) : price;

        // Check if conversion resulted in a valid number
        return !isNaN(numPrice) ? `¥${numPrice.toFixed(2)}` : '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any, record: any) => (
        <Space>
          <Button
            type="primary"
            icon={<SyncOutlined />}
            size="small"
            onClick={() => handleIndexProduct(record.id)}
            loading={loading}
          >
            更新
          </Button>
          <Button
            danger
            icon={<DeleteOutlined />}
            size="small"
            onClick={() => handleDeleteProduct(record.id)}
            loading={loading}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Card title="产品缓存管理">
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="数据库产品总数"
              value={productCount}
              suffix={
                <Tooltip title="刷新数量">
                  <Button
                    type="text"
                    icon={<ReloadOutlined />}
                    onClick={loadCounts}
                    size="small"
                  />
                </Tooltip>
              }
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="ES索引产品数量"
              value={esProductCount}
              suffix={
                <Tooltip title="刷新数量">
                  <Button
                    type="text"
                    icon={<ReloadOutlined />}
                    onClick={loadCounts}
                    size="small"
                  />
                </Tooltip>
              }
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="缓存覆盖率"
              value={productCount > 0 ? (esProductCount / productCount * 100).toFixed(2) : 0}
              suffix="%"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <Space>
              <Button
                type="primary"
                icon={<SyncOutlined />}
                onClick={handleIndexSelected}
                disabled={selectedRowKeys.length === 0}
                loading={loading}
              >
                索引选中产品
              </Button>
              <Button
                type="primary"
                danger
                icon={<SyncOutlined spin={indexingAll} />}
                onClick={handleIndexAll}
                loading={indexingAll}
              >
                索引所有产品
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>

      <Tabs defaultActiveKey="1">
        <TabPane tab="数据库产品" key="1">
          <ProTable
            actionRef={tableRef}
            rowKey="id"
            columns={productColumns}
            search={{
              labelWidth: 120,
            }}
            rowSelection={{
              selectedRowKeys,
              onChange: (newSelectedRowKeys) => {
                setSelectedRowKeys(newSelectedRowKeys);
              },
            }}
            request={async (params, sorter, filter) => {
              const { current, pageSize, ...rest } = params;

              // Convert params to filters
              const filters = Object.entries(rest)
                .filter(([_, value]) => value !== undefined && value !== null && value !== '')
                .map(([key, value]) => ({
                  field: key,
                  operator: 'like',
                  value,
                }));

              // Convert sorter to sorts
              const sorts = Object.entries(sorter)
                .map(([key, value]) => ({
                  field: key,
                  direction: value === 'ascend' ? 'asc' : 'desc',
                }));

              try {
                const response = await apiProductPageList({
                  current: current ? current - 1 : 0,
                  size: pageSize || 10,
                  filters,
                  sorts,
                });

                return {
                  data: response.records || [],
                  success: true,
                  total: response.total || 0,
                };
              } catch (error) {
                console.error('Failed to fetch products:', error);
                message.error('获取产品列表失败');
                return {
                  data: [],
                  success: false,
                  total: 0,
                };
              }
            }}
            pagination={{
              showQuickJumper: true,
              pageSize: 10,
            }}
          />
        </TabPane>
        <TabPane tab="ES索引产品" key="2">
          <ProTable
            actionRef={esTableRef}
            rowKey="id"
            columns={esProductColumns}
            search={{
              labelWidth: 120,
            }}
            request={async (params, sorter, filter) => {
              const { current, pageSize, ...rest } = params;

              // Convert params to filters
              const filters = Object.entries(rest)
                .filter(([_, value]) => value !== undefined && value !== null && value !== '')
                .map(([key, value]) => ({
                  field: key,
                  operator: 'like',
                  value,
                }));

              // Convert sorter to sorts
              const sorts = Object.entries(sorter)
                .map(([key, value]) => ({
                  field: key,
                  direction: value === 'ascend' ? 'asc' : 'desc',
                }));

              try {
                const response = await apiProductEsSearch({
                  current: current ? current - 1 : 0,
                  size: pageSize || 10,
                  filters,
                  sorts,
                });

                return {
                  data: response.records || [],
                  success: true,
                  total: response.total || 0,
                };
              } catch (error) {
                console.error('Failed to fetch ES products:', error);
                message.error('获取ES产品列表失败');
                return {
                  data: [],
                  success: false,
                  total: 0,
                };
              }
            }}
            pagination={{
              showQuickJumper: true,
              pageSize: 10,
            }}
          />
        </TabPane>
      </Tabs>
    </Card>
  );
};


export default ProductEsManagement;

ProductEsManagement.auth = ['apiProductPageList'];
