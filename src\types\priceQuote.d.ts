declare interface PriceQuote {
  id: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  ver: number;
  quoteType: 'BUYER_QUOTE' | 'SELLER_OFFER';
  userId: number;
  userInfo?: TClientUser;
  productId: number;
  productVariantId: number;
  quantity: number;
  price: number;
  currencyCode: string;
  usdPrice: number;
  status: 'ACTIVE' | 'EXPIRED' | 'ACCEPTED' | 'REJECTED' | 'COUNTERED' | 'CANCELLED';
  expirationTime: string;
  orderId: number;
  relatedQuoteId: number;
}

declare interface PriceQuoteBo {
  id: number;
  quoteType: string;
  userId: number;
  productId: number;
  productVariantId: number;
  quantity: number;
  price: number;
  originalPrice: number;
  currencyCode: string;
  status: string;
  message: string;
  expirationTime: string;
  responseUserId: number;
  responseTime: string;
  responseMessage: string;
  responsePrice: number;
  orderId: number;
  relatedQuoteId: number;
  createdAt: string;
  updatedAt: string;
  userName: string;
  responseUserName: string;
  productName: string;
  variantName: string;
  productImage: string;
  priceDifference: number;
  percentageChange: number;
  isExpired: boolean;
  canRespond: boolean;
}

declare interface PriceQuoteRequest {
  userId: number;
  productId: number;
  productVariantId: number;
  quantity: number;
  price: number;
  currencyCode: string;
  message: string;
  expirationHours: number;
}

declare interface CreateOrderFromQuoteRequest {
  quoteId: number;
  buyerId: number;
  shippingAddressId: number;
  paymentMethodId: number;
  notes: string;
}
