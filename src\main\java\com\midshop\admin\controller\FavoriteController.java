package com.midshop.admin.controller;

import com.midshop.admin.dto.FavoriteBo;
import com.midshop.admin.dto.FavoriteGroupBo;
import com.midshop.admin.dto.TPageListRes;
import com.midshop.admin.dto.request.FavoriteGroupPageQuery;
import com.midshop.admin.dto.request.FavoriteGroupUpdateRequest;
import com.midshop.admin.dto.request.FavoritePageQuery;
import com.midshop.admin.dto.request.FavoriteUpdateRequest;
import com.midshop.admin.service.FavoriteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/favorites")
@Tag(name = "Favorites Management")
public class FavoriteController {

    @Autowired
    private FavoriteService favoriteService;

    @PutMapping("/{favoriteId}/note")
    @Operation(summary = "Update favorite note")
    public FavoriteBo updateFavoriteNote(@PathVariable Long favoriteId, @RequestBody FavoriteUpdateRequest request) {
        return favoriteService.updateFavoriteNote(favoriteId, request);
    }

    @PutMapping("/{favoriteId}/group")
    @Operation(summary = "Move favorite to another group")
    public FavoriteBo moveFavoriteToGroup(@PathVariable Long favoriteId, @RequestParam(required = false) Long groupId) {
        return favoriteService.moveFavoriteToGroup(favoriteId, groupId);
    }

    @PutMapping("/groups/{groupId}")
    @Operation(summary = "Update favorite group")
    public FavoriteGroupBo updateFavoriteGroup(@PathVariable Long groupId, @RequestBody FavoriteGroupUpdateRequest request) {
        return favoriteService.updateFavoriteGroup(groupId, request);
    }

    @DeleteMapping("/groups/{groupId}")
    @Operation(summary = "Delete favorite group")
    public void deleteFavoriteGroup(@PathVariable Long groupId) {
        favoriteService.deleteFavoriteGroup(groupId);
    }

    @PutMapping("/groups/{groupId}/order")
    @Operation(summary = "Update favorite group order")
    public FavoriteGroupBo updateFavoriteGroupOrder(@PathVariable Long groupId, @RequestParam Integer newOrder) {
        return favoriteService.updateFavoriteGroupOrder(groupId, newOrder);
    }

    @PostMapping("/page")
    @Operation(summary = "Get paginated list of favorites")
    public TPageListRes<FavoriteBo> getFavoritesPage(@RequestBody FavoritePageQuery query) {
        return favoriteService.getFavoritesPage(query);
    }

    @PostMapping("/groups/page")
    @Operation(summary = "Get paginated list of favorite groups")
    public TPageListRes<FavoriteGroupBo> getFavoriteGroupsPage(@RequestBody FavoriteGroupPageQuery query) {
        return favoriteService.getFavoriteGroupsPage(query);
    }

    @PostMapping("/groups")
    @Operation(summary = "Create favorite group")
    public FavoriteGroupBo createFavoriteGroup(@RequestBody FavoriteGroupBo request) {
        return favoriteService.createFavoriteGroup(request);
    }
}
