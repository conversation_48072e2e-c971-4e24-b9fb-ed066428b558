import { useMemo } from "react";
import { useModel } from "foca";
import { languageModel } from "@/stores/languageModel";

export interface TI18nTextProps {
  value: string;
  showAllLanguages?: boolean;
}

function I18nText(props: TI18nTextProps) {
  const { showAllLanguages = false } = props;
  const { languages } = useModel(languageModel);

  // Get default language code
  const defaultLanguage = useMemo(() => {
    const defaultLang = languages.find(lang => lang.isDefault);
    return defaultLang?.code || 'zh-CN';
  }, [languages]);

  // Parse the value
  const parsedValue = useMemo(() => {
    if (!props.value) return { text: '', isI18n: false };

    try {
      if (typeof props.value === 'string' && props.value.startsWith('{')) {
        const parsed = JSON.parse(props.value);
        // Check if it has the _i18n flag
        if (parsed && parsed._i18n === true) {
          return {
            text: parsed,
            isI18n: true
          };
        }
      }
      // If not a valid i18n object, return as is
      return {
        text: props.value,
        isI18n: false
      };
    } catch (e) {
      // If parsing fails, return as is
      return {
        text: props.value,
        isI18n: false
      };
    }
  }, [props.value]);

  // If not an i18n object, display directly
  if (!parsedValue.isI18n) {
    return <span>{parsedValue.text}</span>;
  }

  // Get the i18n object
  const i18nObj = parsedValue.text as Record<string, any>;

  // If showing all languages
  if (showAllLanguages) {
    return (
      <div className="I18nText">
        {Object.keys(i18nObj).filter(key => key !== '_i18n').map(key => (
          <div key={key}>
            <span className="text-zinc-500">{key}: </span>
            <span>{i18nObj[key]}</span>
          </div>
        ))}
      </div>
    );
  }

  // Show only default language
  return (
    <span>
      {i18nObj[defaultLanguage] ||
       // Fallback to first available language if default not found
       i18nObj[Object.keys(i18nObj).find(key => key !== '_i18n') || ''] ||
       ''}
    </span>
  );
}

export default I18nText
