
import {
  connect,
  mapReadPretty,
  useField,
} from '@formily/react'
import I18nField from '../I18nField';
import { FormItem } from '@formily/antd-v5';


const I18nFormItem = (props: any) => {
  const field = useField() as any;
  return (
    <FormItem {...props}>
      <I18nField onChange={v => field.setValue(v)} value={field.value}>
        {props.children}
      </I18nField>
    </FormItem>

  );
};

export default connect(
  I18nFormItem,
  mapReadPretty(({ value }) => <div className='text-bold'>
    {Object.keys(value).map(key => <div key={key}>{key}: {value[key]}</div>)}
  </div>),
);
