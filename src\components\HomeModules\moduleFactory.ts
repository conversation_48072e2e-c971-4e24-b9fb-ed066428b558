import { nanoid } from 'nanoid';
import { 
  ModuleType, 
  THomeModule, 
  TCarouselModule, 
  TProductListModule, 
  TBrandListModule, 
  TArticleListModule, 
  THotspotImageModule 
} from './types';

export const createModule = (type: ModuleType, order: number): THomeModule => {
  const baseModule = {
    id: nanoid(),
    type,
    order
  };

  switch (type) {
    case ModuleType.Carousel:
      return {
        ...baseModule,
        type: ModuleType.Carousel,
        items: []
      } as TCarouselModule;

    case ModuleType.ProductList:
      return {
        ...baseModule,
        type: ModuleType.ProductList,
        title: '商品列表',
        moreText: '查看更多',
        moreLink: '#',
        products: []
      } as TProductListModule;

    case ModuleType.BrandList:
      return {
        ...baseModule,
        type: ModuleType.BrandList,
        title: '品牌列表',
        moreText: '查看更多',
        moreLink: '#',
        brands: []
      } as TBrandListModule;

    case ModuleType.ArticleList:
      return {
        ...baseModule,
        type: ModuleType.ArticleList,
        title: '文章列表',
        moreText: '查看更多',
        moreLink: '#',
        articles: []
      } as TArticleListModule;

    case ModuleType.HotspotImage:
      return {
        ...baseModule,
        type: ModuleType.HotspotImage,
        imageUrl: '',
        hotspots: []
      } as THotspotImageModule;

    default:
      throw new Error(`未知模块类型: ${type}`);
  }
};
