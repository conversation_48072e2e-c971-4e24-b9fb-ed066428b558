import React, { useRef, useState } from 'react';
import { <PERSON>ton, Modal, Space, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { ToolsUtil } from '@/utils/tools';
import {
  apiExchangeRateRulePageList,
  apiExchangeRateRuleCreate,
  apiExchangeRateRuleUpdate,
  apiExchangeRateRuleDelete
} from '@/apis/apis.api';
import RuleFormModal from '@/components/exchangeRateRuleForm';

// Exchange Rate Rule List Component
const ExchangeRateRuleList: React.FC = () => {
  const tableRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentRule, setCurrentRule] = useState<TExchangeRateRuleBo | null>(null);

  // Open modal for creating a new rule
  const handleAdd = useMemoizedFn(() => {
    setCurrentRule(null);
    setModalVisible(true);
  });

  // Open modal for editing an existing rule
  const handleEdit = useMemoizedFn((rule: TExchangeRateRuleBo) => {
    setCurrentRule(rule);
    setModalVisible(true);
  });

  // Handle rule deletion
  const handleDelete = useMemoizedFn(async (id: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个汇率规则吗？如果该规则正在被使用，删除可能会影响相关汇率。',
      onOk: async () => {
        try {
          await apiExchangeRateRuleDelete(id);
          message.success('汇率规则删除成功');
          tableRef.current?.reload();
        } catch (error) {
          console.error('删除汇率规则失败:', error);
          message.error('删除汇率规则失败');
        }
      }
    });
  });

  // Handle form submission (create/update)
  const handleFormSubmit = useMemoizedFn(async (values: any) => {
    try {
      if (currentRule) {
        // Update existing rule
        await apiExchangeRateRuleUpdate(currentRule.id, {
          ...values,
          isActive: 'TRUE',
          isTemporary: 'TRUE'
        });
        message.success('汇率规则更新成功');
      } else {
        // Create new rule
        await apiExchangeRateRuleCreate({
          ...values,
          isActive: 'TRUE',
          isTemporary: 'TRUE'
        });
        message.success('汇率规则创建成功');
      }

      setModalVisible(false);
      tableRef.current?.reload();
    } catch (error) {
      console.error('保存汇率规则失败:', error);
      message.error('保存汇率规则失败');
    }
  });

  // Table columns
  const columns = [
    {
      title: '规则名称',
      dataIndex: 'name',
      width: 150,
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 200,
      ellipsis: true,
      search: false,
    },
    {
      title: '规则类型',
      dataIndex: 'ruleType',
      width: 120,
      valueEnum: {
        'FIXED_RATE': { text: '固定汇率' },
        'PERCENTAGE_MARKUP': { text: '百分比加价' },
        'PERCENTAGE_DISCOUNT': { text: '百分比折扣' },
        'FIXED_MARKUP': { text: '固定加价' },
        'FIXED_DISCOUNT': { text: '固定折扣' },
      },
    },
    {
      title: '规则值',
      dataIndex: 'value',
      width: 100,
      search: false,
      render: (value: number) => value.toFixed(6),
    },
    {
      title: '引用次数',
      dataIndex: 'referenceCount',
      width: 100,
      search: false,
      render: (count: number) => count || 0,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      valueType: 'option',
      render: (_, record: TExchangeRateRuleBo) => (
        <Space>
          <a onClick={() => handleEdit(record)}>
            <EditOutlined /> 编辑
          </a>
          <a onClick={() => handleDelete(record.id)} style={{ color: 'red' }}>
            <DeleteOutlined /> 删除
          </a>
        </Space>
      ),
    }
  ];

  return (
    <>
      <ProTable
        actionRef={tableRef}
        headerTitle="汇率规则管理"
        rowKey="id"
        columns={columns}
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            创建规则
          </Button>,
        ]}
        request={async (params, sorter) => {
          setLoading(true);
          const filters: TQueryFilter[] = [];

          // 添加搜索条件
          if (params.name) {
            filters.push({ field: 'name', operator: 'like', value: params.name });
          }
          if (params.ruleType) {
            filters.push({ field: 'ruleType', operator: 'eq', value: params.ruleType });
          }

          let sorts = ToolsUtil.tableSorterToPageListReqSort(sorter);

          // 添加默认排序
          if (!sorts || sorts.length === 0) {
            sorts = [{ field: 'id', direction: 'DESC' }];
          }

          try {
            const res = await apiExchangeRateRulePageList({
              current: params.current - 1,
              size: params.pageSize,
              filters,
              sorts,
            });

            setLoading(false);
            return {
              total: res.total || 0,
              data: res.records || [],
              success: true,
            };
          } catch (error) {
            console.error('获取汇率规则列表失败:', error);
            message.error('获取汇率规则列表失败');
            setLoading(false);
            return {
              total: 0,
              data: [],
              success: false,
            };
          }
        }}
        loading={loading}
      />

      {/* Rule Form Modal */}
      <RuleFormModal
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onSubmit={handleFormSubmit}
        initialValues={currentRule}
      />
    </>
  );
};

// Declare the auth property for React.FC components
declare module 'react' {
  interface FunctionComponent {
    auth?: string[];
  }
}

// Add auth property to the component
export default ExchangeRateRuleList;

ExchangeRateRuleList.auth = ['apiExchangeRateRulePageList', 'apiExchangeRateRuleCreate', 'apiExchangeRateRuleUpdate', 'apiExchangeRateRuleDelete'];
