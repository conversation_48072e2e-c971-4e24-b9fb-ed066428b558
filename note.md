## 常用的正则

```
将  
/**  
 * name  
 */  
替换为  
/** name */  

\/\*\*\n\s+\*(.*)\n\s+\*\/
/** $1 */
```


读取 ./mid-shop.openapi.json 里的内容
请参考./src/apis/apis.api.ts已有的接口作为模板
如果接口在./src/apis/apis.api.ts不存在，请新增到./src/apis/apis.api.ts, 如果存在，使用新的替换原来的
请将接口描述翻译成中文注释在接口函数的上面
./src/types/model.d.ts 用于存放接口相关的类型
如果接口用到的类型不存在，请新增到./src/types/model.d.ts, 类型开头加上T，以便与其他class区分, 如果存在，使用新的替换原来的
如果接口以/admin开头，去掉/admin
如果类型某个字段是id，且id是number，请改为string
接口函数名需要以api开头
请按照你的理解为新增的类型和字段添加注释，注释使用一行/** 注释内容 */的格式



/** 同步品牌下的分类列表 */
export function apiSyncBrandCategories(brandId: string, categoryIds: string[]): Promise<TProductBrandBo> {
  return http.put<TProductBrandBo>(`/brands/${brandId}/categories`, categoryIds);
}

/** 根据品牌ID获取分类 */
export function apiGetCategoriesByBrandId(brandId: string): Promise<TProductCategoryBo[]> {
  return http.get<TProductCategoryBo[]>(`/categories/brand/${brandId}`);
}

给 @brandList.tsx 每一项添加一个设置分类按钮，点击打开一个模态框, 使用 @apiGetCategoriesByBrandId加载品牌的分类，从 @apiCategoryTree 加载分类树渲染成TreeSelect
，多选分类之后，使用  @apiSyncBrandCategories 更新品牌的分类。


模仿 @d:\Works\ipproxy\ipproxy-admin/src\pages\adminRoleList.tsx 
使用 src\apis\gen.apis.ts apiStaticOrders相关接口创建一个页面
使用 src\apis\gen.apis.ts apiStaticInstances相关接口创建一个页面
创建好之后加入到 src/router.tsx 里