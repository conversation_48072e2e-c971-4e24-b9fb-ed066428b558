import React from 'react';
import { THomeModule, ModuleType } from './types';
import CarouselModuleComponent from './CarouselModule';
import ProductListModuleComponent from './ProductListModule';
import BrandListModuleComponent from './BrandListModule';
import ArticleListModuleComponent from './ArticleListModule';
import HotspotImageModuleComponent from './HotspotImageModule';

interface ModuleRendererProps {
  module: THomeModule;
  isFirst: boolean;
  isLast: boolean;
  onUpdate: (updatedModule: THomeModule) => void;
  onDelete: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onInsertBefore: () => void;
  isPreview?: boolean;
  isEditing?: boolean;
}

const ModuleRenderer: React.FC<ModuleRendererProps> = (props) => {
  const { module } = props;

  switch (module.type) {
    case ModuleType.Carousel:
      return <CarouselModuleComponent {...props} module={module} />;
    case ModuleType.ProductList:
      return <ProductListModuleComponent {...props} module={module} />;
    case ModuleType.BrandList:
      return <BrandListModuleComponent {...props} module={module} />;
    case ModuleType.ArticleList:
      return <ArticleListModuleComponent {...props} module={module} />;
    case ModuleType.HotspotImage:
      return <HotspotImageModuleComponent {...props} module={module} />;
    default:
      return <div>未知模块类型</div>;
  }
};

export default ModuleRenderer;
